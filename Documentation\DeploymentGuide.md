# UFU2 Deployment and Configuration Guide

## Table of Contents

1. [System Requirements and Compatibility](#system-requirements-and-compatibility)
2. [Installation Procedures](#installation-procedures)
3. [Configuration File Documentation](#configuration-file-documentation)
4. [Database Deployment](#database-deployment)
5. [Application Configuration](#application-configuration)
6. [Troubleshooting Common Deployment Issues](#troubleshooting-common-deployment-issues)
7. [Performance Optimization](#performance-optimization)
8. [Security Considerations](#security-considerations)

---

## System Requirements and Compatibility

### Minimum System Requirements

| Component | Requirement | Recommended |
|-----------|-------------|-------------|
| **Operating System** | Windows 10 (1903) or later | Windows 11 22H2 or later |
| **Architecture** | x64 (64-bit) | x64 (64-bit) |
| **Memory (RAM)** | 4 GB | 8 GB or more |
| **Storage** | 500 MB free space | 2 GB free space |
| **Display** | 1366x768 minimum | 1920x1080 or higher |
| **Graphics** | DirectX 11 compatible | DirectX 12 compatible |

### .NET Runtime Requirements

UFU2 requires the following .NET runtime components:

```json
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App",
        "version": "8.0.0"
      }
    ]
  }
}
```

#### Runtime Installation
1. **Download .NET 8.0 Desktop Runtime** from Microsoft:
   - URL: `https://dotnet.microsoft.com/download/dotnet/8.0`
   - Select "Desktop Runtime" for Windows x64
   - File: `windowsdesktop-runtime-8.0.x-win-x64.exe`

2. **Verify Installation**:
   ```cmd
   dotnet --list-runtimes
   ```
   Expected output should include:
   ```
   Microsoft.NETCore.App 8.0.x [C:\Program Files\dotnet\shared\Microsoft.NETCore.App]
   Microsoft.WindowsDesktop.App 8.0.x [C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App]
   ```

### Windows Compatibility Matrix

| Windows Version | Support Status | Notes |
|----------------|----------------|-------|
| **Windows 11 22H2+** | ✅ Fully Supported | Recommended platform |
| **Windows 11 21H2** | ✅ Fully Supported | All features available |
| **Windows 10 22H2** | ✅ Fully Supported | Recommended for Windows 10 |
| **Windows 10 21H2** | ✅ Supported | Minor UI scaling issues possible |
| **Windows 10 20H2** | ⚠️ Limited Support | Some MaterialDesign features may not render correctly |
| **Windows 10 1903-2004** | ⚠️ Limited Support | Minimum supported version |
| **Windows 8.1 or earlier** | ❌ Not Supported | .NET 8.0 not compatible |

### Hardware Compatibility

#### Display Requirements
- **Minimum Resolution**: 1366x768 (Arabic RTL layout optimized for this resolution)
- **Recommended Resolution**: 1920x1080 or higher
- **DPI Support**: 96 DPI to 300 DPI (automatic scaling)
- **Color Depth**: 32-bit color recommended for MaterialDesign themes

#### Performance Considerations
- **CPU**: Modern x64 processor (Intel Core i3 or AMD Ryzen 3 equivalent)
- **Memory**: 4GB minimum (8GB recommended for optimal performance)
- **Storage**: SSD recommended for database operations
- **Network**: Not required (offline application)

---

## Installation Procedures

### Method 1: Self-Contained Deployment (Recommended)

Self-contained deployment includes all required .NET runtime components, eliminating the need for separate runtime installation.

#### Step 1: Download UFU2 Package
```
UFU2-SelfContained-v1.0.0-win-x64.zip
├── UFU2.exe                    # Main application executable
├── UFU2.dll                    # Application library
├── UFU2.runtimeconfig.json     # Runtime configuration
├── UFU2.deps.json              # Dependency manifest
├── *.dll                       # Required runtime libraries
└── Resources/                  # Application resources
    ├── Styles/                 # MaterialDesign styles
    └── Themes/                 # Light/Dark themes
```

#### Step 2: Extract and Install
1. **Create Installation Directory**:
   ```cmd
   mkdir "C:\Program Files\UFU2"
   ```

2. **Extract Package**:
   ```cmd
   # Extract to installation directory
   # Ensure all files maintain their directory structure
   ```

3. **Set Permissions**:
   ```cmd
   # Grant read/execute permissions to Users group
   icacls "C:\Program Files\UFU2" /grant Users:(RX) /T
   ```

4. **Create Desktop Shortcut**:
   ```cmd
   # Target: C:\Program Files\UFU2\UFU2.exe
   # Start in: C:\Program Files\UFU2
   # Icon: UFU2.exe
   ```

#### Step 3: Verify Installation
1. **Launch Application**:
   ```cmd
   cd "C:\Program Files\UFU2"
   UFU2.exe
   ```

2. **Expected Startup Sequence**:
   ```
   [INFO] UFU2 Application starting up
   [DEBUG] ServiceLocator initialized
   [DEBUG] ThemeManager initialized successfully
   [INFO] DatabaseService initialized with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db
   [DEBUG] Database schema initialization completed
   [DEBUG] UFU2 Application startup completed in [X]ms
   ```

### Method 2: Framework-Dependent Deployment

Requires .NET 8.0 Desktop Runtime to be pre-installed on the target system.

#### Prerequisites
1. **Install .NET 8.0 Desktop Runtime** (see Runtime Requirements section)

#### Installation Steps
1. **Download Framework-Dependent Package**:
   ```
   UFU2-Framework-v1.0.0.zip
   ├── UFU2.exe
   ├── UFU2.dll
   ├── UFU2.runtimeconfig.json
   ├── UFU2.deps.json
   └── Resources/
   ```

2. **Extract and Configure** (same as Method 1, Steps 2-3)

3. **Verify Runtime Dependency**:
   ```cmd
   dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 8.0"
   ```

### Method 3: Development Environment Setup

For development or testing environments.

#### Prerequisites
- Visual Studio 2022 (17.8 or later) or Visual Studio Code
- .NET 8.0 SDK

#### Setup Steps
1. **Clone Repository**:
   ```cmd
   git clone [repository-url] UFU2
   cd UFU2
   ```

2. **Restore Dependencies**:
   ```cmd
   dotnet restore
   ```

3. **Build Application**:
   ```cmd
   dotnet build --configuration Release
   ```

4. **Run Application**:
   ```cmd
   dotnet run --configuration Release
   ```

---

## Configuration File Documentation

### Runtime Configuration (UFU2.runtimeconfig.json)

UFU2's runtime configuration file controls .NET runtime behavior:

```json
{
  "runtimeOptions": {
    "tfm": "net8.0",
    "frameworks": [
      {
        "name": "Microsoft.NETCore.App",
        "version": "8.0.0"
      },
      {
        "name": "Microsoft.WindowsDesktop.App", 
        "version": "8.0.0"
      }
    ],
    "configProperties": {
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": true,
      "CSWINRT_USE_WINDOWS_UI_XAML_PROJECTIONS": false
    }
  }
}
```

#### Configuration Properties Explained

| Property | Purpose | Default Value | Notes |
|----------|---------|---------------|-------|
| `tfm` | Target Framework Moniker | `net8.0` | Do not modify |
| `frameworks` | Required runtime frameworks | See above | Do not modify |
| `EnableUnsafeBinaryFormatterSerialization` | Legacy serialization support | `true` | Required for some MaterialDesign components |
| `CSWINRT_USE_WINDOWS_UI_XAML_PROJECTIONS` | Windows Runtime projections | `false` | Prevents WinUI conflicts |

### Application Configuration

UFU2 uses code-based configuration with the following default settings:

#### Database Configuration
```csharp
// Default database path (automatically created)
string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
string databasePath = Path.Combine(appDataPath, "UFU2", "Data", "UFU2_Database.db");

// SQLite connection configuration
var connectionString = new SqliteConnectionStringBuilder
{
    DataSource = databasePath,
    Mode = SqliteOpenMode.ReadWriteCreate,
    Cache = SqliteCacheMode.Shared
}.ToString();
```

#### Logging Configuration
```csharp
// Session-based logging configuration
string logsDirectory = Path.Combine(appDataPath, "UFU2", "Logs");
string logFileName = $"UFU2_App_Log_{sessionStartTime:yyyy-MM-dd_HH-mm-ss}.log";

// Log retention: 30 files maximum (automatic rotation)
const int MAX_LOG_FILES = 30;
```

#### Theme Configuration
```csharp
// Default theme settings
ApplicationTheme defaultTheme = ApplicationTheme.Dark;
PrimaryColor primaryColor = PrimaryColor.DeepPurple;
SecondaryColor secondaryColor = SecondaryColor.Purple;
```

### Environment Variables (Optional)

UFU2 supports the following optional environment variables for advanced configuration:

| Variable | Purpose | Default | Example |
|----------|---------|---------|---------|
| `UFU2_DATABASE_PATH` | Custom database location | `%APPDATA%\UFU2\Data\UFU2_Database.db` | `C:\CustomPath\UFU2.db` |
| `UFU2_LOG_LEVEL` | Logging verbosity | `Info` | `Debug`, `Info`, `Warning`, `Error` |
| `UFU2_LOG_PATH` | Custom log directory | `%APPDATA%\UFU2\Logs` | `C:\Logs\UFU2` |
| `UFU2_THEME` | Default theme | `Dark` | `Light`, `Dark` |

#### Setting Environment Variables
```cmd
# System-wide (requires administrator)
setx UFU2_LOG_LEVEL "Debug" /M

# User-specific
setx UFU2_DATABASE_PATH "C:\UFU2Data\database.db"
```

---

## Database Deployment

### Automatic Database Initialization

UFU2 automatically creates and configures the SQLite database on first launch:

#### Database Location
```
Default Path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db
```

#### Initialization Process
1. **Directory Creation**:
   ```csharp
   // UFU2 automatically creates required directories
   string ufu2DataPath = Path.Combine(appDataPath, "UFU2", "Data");
   Directory.CreateDirectory(ufu2DataPath);
   ```

2. **Schema Deployment**:
   ```sql
   -- UFU2_Schema.sql is embedded in the application
   -- Automatically applied during first launch
   CREATE TABLE Clients (
       UID TEXT PRIMARY KEY,
       NameFr TEXT NOT NULL,
       NameAr TEXT,
       -- Additional columns...
   );
   ```

3. **Performance Optimization**:
   ```sql
   -- Automatic PRAGMA configuration for optimal performance
   PRAGMA journal_mode = WAL;           -- Write-Ahead Logging
   PRAGMA synchronous = NORMAL;         -- Balanced safety/performance
   PRAGMA cache_size = 10000;           -- 10MB cache
   PRAGMA temp_store = MEMORY;          -- Memory-based temp storage
   PRAGMA auto_vacuum = INCREMENTAL;    -- Space reclamation
   PRAGMA foreign_keys = ON;            -- Referential integrity
   ```

### Database Migration

UFU2 includes automatic schema migration capabilities:

#### Version Management
```csharp
// Current schema version (embedded in application)
private const int CURRENT_SCHEMA_VERSION = 1;

// Migration process
public async Task InitializeSchemaAsync()
{
    int currentVersion = await GetCurrentSchemaVersionAsync();
    
    if (currentVersion == 0)
    {
        // New database - create initial schema
        await CreateInitialSchemaAsync();
    }
    else if (currentVersion < CURRENT_SCHEMA_VERSION)
    {
        // Existing database - apply migrations
        await ApplyMigrationsAsync(currentVersion);
    }
}
```

#### Migration Verification
```csharp
// Automatic verification after migration
var stats = await GetDatabaseStatsAsync();
LoggingService.LogInfo($"Database initialized - Version: {stats.SchemaVersion}, Tables: {stats.TableCount}");
```

### Database Backup and Recovery

#### Automatic Backup Strategy
```csharp
// UFU2 uses SQLite WAL mode for automatic backup capability
// Database file can be safely copied while application is running
string backupPath = Path.Combine(dataDirectory, $"UFU2_Database_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db");
```

#### Manual Backup Procedure
1. **Locate Database File**:
   ```
   C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db
   ```

2. **Create Backup**:
   ```cmd
   copy "C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db" "C:\Backup\UFU2_Database_Backup.db"
   ```

3. **Restore from Backup**:
   ```cmd
   # Stop UFU2 application first
   copy "C:\Backup\UFU2_Database_Backup.db" "C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db"
   ```

---

## Application Configuration

### Startup Configuration

UFU2 follows a specific initialization sequence that can be monitored for deployment verification:

#### Initialization Sequence
```csharp
// 1. Logging Service Initialization
LoggingService.InitializeSessionLogging();

// 2. ServiceLocator Initialization
ServiceLocator.Initialize();

// 3. Theme Manager Initialization
await ThemeManager.InitializeAsync(ApplicationTheme.Dark);

// 4. Database Services Initialization
await ServiceLocator.InitializeDatabaseServicesAsync();
```

#### Expected Log Output
```
[INFO] UFU2 Application starting up
[DEBUG] ServiceLocator initialized
[DEBUG] ThemeManager initialized successfully
[INFO] DatabaseService initialized with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db
[DEBUG] Database schema initialization completed
[DEBUG] UFU2 Application startup completed in [X]ms
```

### Directory Structure

UFU2 creates the following directory structure in the user's AppData:

```
C:\Users\<USER>\AppData\Roaming\UFU2\
├── Data\
│   ├── UFU2_Database.db          # Main SQLite database
│   ├── UFU2_Database.db-wal      # Write-Ahead Log file
│   └── UFU2_Database.db-shm      # Shared memory file
├── Logs\
│   ├── UFU2_App_Log_2025-01-15_09-30-15.log
│   ├── UFU2_App_Log_2025-01-15_14-22-08.log
│   └── [Additional log files...]
└── Temp\                         # Temporary files (auto-cleaned)
    └── [Image processing cache]
```

### Service Configuration

UFU2 uses ServiceLocator pattern for dependency injection. Services are automatically registered during startup:

#### Core Services Registration
```csharp
// Automatically registered services:
- DatabaseService              // SQLite database operations
- ClientDatabaseService        // Client management operations
- UIDGenerationService         // Unique identifier generation
- ClientValidationService      // Data validation with Arabic messages
- FileCheckBusinessRuleService // Algerian regulation compliance
- ValidationService            // General validation utilities
- DatabaseMigrationService     // Schema migration management
- ToastService                 // UI notification system
```

#### Service Health Verification
```csharp
// UFU2 automatically validates service registration
bool servicesValid = ValidateDatabaseServices();
if (!servicesValid)
{
    LoggingService.LogWarning("Some required database services are missing");
}
```

### Theme and Localization Configuration

#### Default Theme Settings
```csharp
// MaterialDesign theme configuration
BaseTheme: Dark
PrimaryColor: DeepPurple
SecondaryColor: Purple
FlowDirection: RightToLeft (Arabic RTL support)
```

#### Arabic RTL Support
```csharp
// Automatic RTL detection and configuration
var currentCulture = CultureInfo.CurrentUICulture;
var isRtl = currentCulture.TextInfo.IsRightToLeft;

// UI adjustments for RTL layout
TitleBarFlowDirection = isRtl ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
```

---

## Troubleshooting Common Deployment Issues

### Issue 1: .NET Runtime Not Found

#### Symptoms
```
Error: The application requires .NET Desktop Runtime 8.0.x
Application fails to start with runtime error
```

#### Arabic Error Message
```
خطأ: التطبيق يتطلب .NET Desktop Runtime 8.0.x
فشل في بدء تشغيل التطبيق مع خطأ في وقت التشغيل
```

#### Solution
1. **Download and Install .NET 8.0 Desktop Runtime**:
   ```cmd
   # Download from: https://dotnet.microsoft.com/download/dotnet/8.0
   # Install: windowsdesktop-runtime-8.0.x-win-x64.exe
   ```

2. **Verify Installation**:
   ```cmd
   dotnet --list-runtimes
   # Should show: Microsoft.WindowsDesktop.App 8.0.x
   ```

3. **Alternative: Use Self-Contained Deployment**:
   - Download UFU2-SelfContained package instead
   - No separate runtime installation required

### Issue 2: Database Initialization Failure

#### Symptoms
```
[ERROR] Database service initialization failed
[WARNING] Application will continue with limited database functionality
```

#### Arabic Error Message
```
فشل في تهيئة قاعدة البيانات. سيستمر التطبيق ولكن قد لا تعمل ميزات قاعدة البيانات بشكل صحيح
```

#### Possible Causes and Solutions

**Cause 1: Insufficient Permissions**
```cmd
# Solution: Grant write permissions to UFU2 data directory
icacls "%APPDATA%\UFU2" /grant %USERNAME%:(F) /T
```

**Cause 2: Antivirus Blocking Database Creation**
```
# Solution: Add UFU2 data directory to antivirus exclusions
# Path: C:\Users\<USER>\AppData\Roaming\UFU2\
```

**Cause 3: Disk Space Insufficient**
```cmd
# Check available space (minimum 100MB required)
dir "%APPDATA%" | findstr "bytes free"
```

**Cause 4: Corrupted Database File**
```cmd
# Solution: Delete existing database to force recreation
del "%APPDATA%\UFU2\Data\UFU2_Database.db"
del "%APPDATA%\UFU2\Data\UFU2_Database.db-wal"
del "%APPDATA%\UFU2\Data\UFU2_Database.db-shm"
```

### Issue 3: Theme Loading Failure

#### Symptoms
```
[WARNING] ThemeManager initialization failed, continuing with default theme
UI appears with incorrect colors or styling
```

#### Arabic Error Message
```
فشل في تهيئة مدير المظهر، سيتم المتابعة بالمظهر الافتراضي
```

#### Solution
1. **Verify MaterialDesign Resources**:
   ```
   # Ensure these files exist in installation directory:
   Resources\Styles\*.xaml
   Resources\Themes\*.xaml
   ```

2. **Reset Theme Configuration**:
   ```csharp
   // UFU2 will automatically fallback to default theme
   // No user action required - application will continue normally
   ```

### Issue 4: Application Crashes on Startup

#### Symptoms
```
Application starts then immediately closes
No visible error message
Event Viewer shows .NET application error
```

#### Diagnostic Steps

**Step 1: Enable Detailed Logging**
```cmd
# Set environment variable for debug logging
setx UFU2_LOG_LEVEL "Debug"
```

**Step 2: Check Event Viewer**
```cmd
# Open Event Viewer
eventvwr.msc

# Navigate to: Windows Logs > Application
# Look for .NET Runtime errors with UFU2 source
```

**Step 3: Run from Command Line**
```cmd
cd "C:\Program Files\UFU2"
UFU2.exe
# Observe console output for error details
```

**Step 4: Check Dependencies**
```cmd
# Verify all required DLL files are present
dir "C:\Program Files\UFU2\*.dll"

# Expected files include:
# - UFU2.dll
# - MaterialDesignThemes.Wpf.dll
# - MaterialDesignColors.dll
# - Microsoft.Data.Sqlite.dll
# - Dapper.dll
# - Newtonsoft.Json.dll
```

### Issue 5: Performance Issues

#### Symptoms
```
Slow application startup (>10 seconds)
UI freezing during operations
Database operations timing out
```

#### Arabic Error Message
```
أداء التطبيق بطيء - قد تحتاج إلى تحسين النظام
```

#### Performance Optimization

**Solution 1: Database Optimization**
```cmd
# UFU2 automatically applies these optimizations:
# - WAL mode for better concurrency
# - Increased cache size (10MB)
# - Memory-based temporary storage
# - Incremental auto-vacuum
```

**Solution 2: System Requirements Check**
```cmd
# Verify system meets minimum requirements:
# - 4GB RAM minimum (8GB recommended)
# - SSD storage recommended
# - Windows 10 1903 or later
```

**Solution 3: Antivirus Exclusions**
```
# Add these paths to antivirus exclusions:
# - C:\Program Files\UFU2\
# - C:\Users\<USER>\AppData\Roaming\UFU2\
```

### Issue 6: Arabic Text Display Issues

#### Symptoms
```
Arabic text appears as squares or question marks
RTL layout not working correctly
Mixed Arabic/Latin text alignment issues
```

#### Arabic Error Message
```
مشكلة في عرض النص العربي - تحقق من إعدادات النظام
```

#### Solution
1. **Verify Arabic Language Support**:
   ```cmd
   # Windows Settings > Time & Language > Language
   # Ensure Arabic language pack is installed
   ```

2. **Check Font Installation**:
   ```
   # UFU2 uses system fonts for Arabic text
   # Ensure Arabic-compatible fonts are available:
   # - Segoe UI (default)
   # - Tahoma
   # - Arial Unicode MS
   ```

3. **Regional Settings**:
   ```cmd
   # Control Panel > Region > Administrative
   # Ensure "Language for non-Unicode programs" supports Arabic
   ```

### Issue 7: File Permissions Error

#### Symptoms
```
[ERROR] Failed to write to session log file: Access denied
[ERROR] Database connection failed: Access denied
```

#### Arabic Error Message
```
خطأ في الأذونات - فشل في الوصول إلى الملفات المطلوبة
```

#### Solution
```cmd
# Grant full permissions to UFU2 directories
icacls "%APPDATA%\UFU2" /grant %USERNAME%:(F) /T
icacls "C:\Program Files\UFU2" /grant %USERNAME%:(RX) /T

# If running as administrator is required:
# Right-click UFU2.exe > Run as administrator
```

---

## Performance Optimization

### Database Performance Tuning

UFU2 automatically applies optimal SQLite configuration, but additional tuning may be beneficial for large datasets:

#### Automatic Optimizations Applied
```sql
-- UFU2 automatically configures these PRAGMA settings:
PRAGMA journal_mode = WAL;           -- Write-Ahead Logging (better concurrency)
PRAGMA synchronous = NORMAL;         -- Balanced safety/performance
PRAGMA cache_size = 10000;           -- 10MB memory cache
PRAGMA temp_store = MEMORY;          -- Memory-based temporary storage
PRAGMA auto_vacuum = INCREMENTAL;    -- Automatic space reclamation
PRAGMA foreign_keys = ON;            -- Referential integrity enforcement
```

#### Manual Database Maintenance
```cmd
# UFU2 includes automatic maintenance, but manual optimization can be performed:
# 1. Stop UFU2 application
# 2. Run SQLite VACUUM command (if needed)
# 3. Restart UFU2 application
```

### System-Level Optimizations

#### Windows Performance Settings
```cmd
# Recommended Windows settings for optimal UFU2 performance:
# 1. Power Plan: High Performance or Balanced
# 2. Visual Effects: Adjust for best performance
# 3. Virtual Memory: System managed (minimum 4GB)
```

#### Antivirus Configuration
```
# Add UFU2 to antivirus exclusions for better performance:
# Process Exclusions:
# - UFU2.exe
#
# Folder Exclusions:
# - C:\Program Files\UFU2\
# - C:\Users\<USER>\AppData\Roaming\UFU2\
```

#### Storage Optimization
```
# For optimal database performance:
# 1. Install UFU2 on SSD if available
# 2. Ensure at least 20% free space on system drive
# 3. Regular disk cleanup and defragmentation (HDD only)
```

---

## Security Considerations

### Application Security

UFU2 implements multiple security layers to protect user data:

#### Database Security
```csharp
// UFU2 uses parameterized queries exclusively to prevent SQL injection
var result = await connection.QueryAsync<Client>(
    "SELECT * FROM Clients WHERE UID = @UID",
    new { UID = clientUID }
);

// No dynamic SQL construction - all queries are parameterized
```

#### Input Validation
```csharp
// Multi-layer validation prevents malicious input:
// 1. UI-level validation (real-time)
// 2. Business logic validation (service layer)
// 3. Database constraints (schema level)
```

#### Error Information Security
```csharp
// Technical details logged securely, user sees generic Arabic messages
LoggingService.LogError($"Database error: {ex.Message}", "DatabaseService");
ErrorManager.ShowUserErrorToast("حدث خطأ في قاعدة البيانات", "خطأ في النظام", "DatabaseService");
```

### Deployment Security

#### File System Permissions
```cmd
# Recommended permissions for UFU2 installation:
# Installation Directory (C:\Program Files\UFU2\):
# - Administrators: Full Control
# - Users: Read & Execute
# - SYSTEM: Full Control

# User Data Directory (%APPDATA%\UFU2\):
# - User: Full Control
# - Administrators: Full Control
# - SYSTEM: Full Control
```

#### Network Security
```
# UFU2 is an offline application with no network requirements:
# - No inbound network connections
# - No outbound network connections
# - No data transmission over network
# - All data stored locally in encrypted SQLite database
```

#### Data Protection
```csharp
// UFU2 stores all data locally with the following protections:
// 1. SQLite database with WAL mode (atomic transactions)
// 2. User data directory protected by Windows file system permissions
// 3. No sensitive data stored in plain text configuration files
// 4. Automatic database backup capability through WAL mode
```

### Compliance Considerations

#### Data Privacy
```
# UFU2 complies with data privacy requirements:
# - All data stored locally (no cloud storage)
# - No telemetry or usage tracking
# - No external data transmission
# - User has full control over data location and backup
```

#### Algerian Regulatory Compliance
```csharp
// UFU2 implements Algerian business regulation compliance:
// - File check business rules for Commercial/Craft/Professional activities
// - Required document validation (CAS, NIF, NIS, RC, ART, AGR)
// - UID generation following Algerian business patterns
// - Arabic language support for all user-facing content
```

---

## Deployment Checklist

### Pre-Deployment Verification

- [ ] **System Requirements Met**
  - [ ] Windows 10 (1903) or later
  - [ ] 4GB RAM minimum (8GB recommended)
  - [ ] 500MB free disk space
  - [ ] x64 architecture

- [ ] **Runtime Dependencies**
  - [ ] .NET 8.0 Desktop Runtime installed (framework-dependent deployment)
  - [ ] OR Self-contained deployment package used

- [ ] **Security Configuration**
  - [ ] Antivirus exclusions configured
  - [ ] File system permissions verified
  - [ ] User account has appropriate privileges

### Post-Deployment Verification

- [ ] **Application Startup**
  - [ ] UFU2.exe launches successfully
  - [ ] No error messages in Event Viewer
  - [ ] Log files created in %APPDATA%\UFU2\Logs\

- [ ] **Database Initialization**
  - [ ] Database file created: %APPDATA%\UFU2\Data\UFU2_Database.db
  - [ ] Schema version matches application version
  - [ ] No database errors in log files

- [ ] **UI Functionality**
  - [ ] MaterialDesign theme loads correctly
  - [ ] Arabic RTL layout displays properly
  - [ ] All UI components responsive

- [ ] **Core Features**
  - [ ] Client creation workflow functional
  - [ ] Database operations complete successfully
  - [ ] Error messages display in Arabic
  - [ ] Toast notifications work correctly

### Maintenance Schedule

#### Daily
- [ ] Monitor log files for errors
- [ ] Verify application startup performance

#### Weekly
- [ ] Check database file size growth
- [ ] Review system performance metrics
- [ ] Verify backup procedures

#### Monthly
- [ ] Clean old log files (automatic rotation)
- [ ] Review antivirus exclusion settings
- [ ] Check for Windows updates compatibility

#### Quarterly
- [ ] Full database backup
- [ ] Performance optimization review
- [ ] Security configuration audit

---

## Support and Troubleshooting Resources

### Log File Locations
```
Application Logs: %APPDATA%\UFU2\Logs\UFU2_App_Log_[timestamp].log
Database Location: %APPDATA%\UFU2\Data\UFU2_Database.db
Configuration: Embedded in application (no external config files)
```

### Common Log Messages

#### Successful Startup
```
[INFO] UFU2 Application starting up
[DEBUG] ServiceLocator initialized
[DEBUG] ThemeManager initialized successfully
[INFO] DatabaseService initialized with path: [path]
[DEBUG] Database schema initialization completed
[DEBUG] UFU2 Application startup completed in [X]ms
```

#### Database Issues
```
[ERROR] Database service initialization failed: [details]
[WARNING] Application will continue with limited database functionality
```

#### Theme Issues
```
[WARNING] ThemeManager initialization failed, continuing with default theme
```

### Emergency Recovery Procedures

#### Complete Application Reset
```cmd
# 1. Stop UFU2 application
# 2. Delete user data directory
rmdir /s "%APPDATA%\UFU2"
# 3. Restart UFU2 (will recreate all data structures)
```

#### Database Recovery
```cmd
# 1. Stop UFU2 application
# 2. Restore from backup
copy "C:\Backup\UFU2_Database_Backup.db" "%APPDATA%\UFU2\Data\UFU2_Database.db"
# 3. Restart UFU2
```

This comprehensive deployment guide enables system administrators to successfully deploy UFU2 in production environments with confidence, following established security practices and ensuring optimal performance while maintaining full Arabic localization support.
