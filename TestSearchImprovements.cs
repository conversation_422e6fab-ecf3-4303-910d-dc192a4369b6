using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UFU2.Common.Utilities;
using UFU2.Models;
using UFU2.Services;

namespace UFU2
{
    /// <summary>
    /// Test program to validate the Arabic search improvements.
    /// Tests the specific issue with "حرفي نجار وصانع" search term.
    /// </summary>
    public static class TestSearchImprovements
    {
        /// <summary>
        /// Main test method to validate search improvements.
        /// </summary>
        public static async Task<string> RunSearchTests()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("=== UFU2 Search Improvements Test ===");
            results.AppendLine($"Test Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                // Test 1: Text Normalization
                results.AppendLine("1. Testing Text Normalization:");
                string testNormalization = TextNormalizationHelper.TestArabicConjunctionNormalization();
                results.AppendLine(testNormalization);
                results.AppendLine();

                // Test 2: Create sample craft data for testing
                var sampleCrafts = CreateSampleCraftData();
                results.AppendLine("2. Sample Craft Data Created:");
                foreach (var craft in sampleCrafts)
                {
                    results.AppendLine($"   {craft.Code}: {craft.Description}");
                }
                results.AppendLine();

                // Test 3: Enhanced Search Service Test
                results.AppendLine("3. Testing Enhanced Search Service:");
                var enhancedSearchService = new EnhancedSearchService();
                
                string searchTerm = "حرفي نجار وصانع";
                results.AppendLine($"Search Term: '{searchTerm}'");
                results.AppendLine();

                var searchResults = await enhancedSearchService.SearchAsync(
                    sampleCrafts,
                    searchTerm,
                    craft => craft.Description,
                    maxResults: 10,
                    minSimilarity: 0.3
                );

                results.AppendLine("Search Results (ordered by relevance):");
                for (int i = 0; i < searchResults.Count; i++)
                {
                    var result = searchResults[i];
                    results.AppendLine($"   {i + 1}. Score: {result.SimilarityScore:F3} | " +
                                     $"Type: {result.MatchType} | " +
                                     $"Code: {result.Item.Code} | " +
                                     $"Description: {result.Item.Description}");
                }

                // Test 4: Validate expected result
                results.AppendLine();
                results.AppendLine("4. Validation:");
                
                string expectedTopResult = "حرفي نجار و صانع الأثاث";
                bool testPassed = searchResults.Count > 0 && 
                                 searchResults[0].Item.Description.Contains("نجار") && 
                                 searchResults[0].Item.Description.Contains("صانع");

                results.AppendLine($"Expected top result should contain both 'نجار' and 'صانع'");
                results.AppendLine($"Actual top result: {(searchResults.Count > 0 ? searchResults[0].Item.Description : "No results")}");
                results.AppendLine($"Test Status: {(testPassed ? "PASS" : "FAIL")}");

                if (testPassed)
                {
                    results.AppendLine("✅ Search improvements are working correctly!");
                    results.AppendLine($"The search term '{searchTerm}' correctly returns '{searchResults[0].Item.Description}' as the top result.");
                }
                else
                {
                    results.AppendLine("❌ Search improvements need further adjustment.");
                    results.AppendLine("The expected result is not appearing as the top match.");
                }

                return results.ToString();
            }
            catch (Exception ex)
            {
                results.AppendLine($"ERROR during testing: {ex.Message}");
                results.AppendLine($"Stack trace: {ex.StackTrace}");
                return results.ToString();
            }
        }

        /// <summary>
        /// Creates sample craft data for testing, including the problematic case.
        /// </summary>
        private static List<CraftTypeBaseModel> CreateSampleCraftData()
        {
            return new List<CraftTypeBaseModel>
            {
                // The target result that should appear first
                new CraftTypeBaseModel("02-14-012", "حرفي نجار و صانع الأثاث. (النجارة و النجارة العامة)", 
                    "صناعة مواد النجارة العامة للبناء والأثاث", ""),

                // Other results that currently appear higher
                new CraftTypeBaseModel("01-04-003", "حرفي نجار فني. ( النجارة الفنية ).", 
                    "صنع الأثاث الخشبي المنقوش", ""),

                new CraftTypeBaseModel("02-11-001", "حرفي مشكل الحديد (تشكيل الحديد)", 
                    "صناعة كل أنواع مواد الحديد", ""),

                new CraftTypeBaseModel("01-08-005", "حرفي في نجارة الألمنيوم (نجارة الألمنيوم)", 
                    "صناعة النوافذ والأبواب من الألمنيوم", ""),

                new CraftTypeBaseModel("01-04-004", "حرفي نجار", 
                    "النجارة التقليدية", ""),

                new CraftTypeBaseModel("02-09-003", "حرفي مركب هياكل البناء ونجارة البناءات", 
                    "تركيب الهياكل الخشبية للبناء", ""),

                // Additional test cases
                new CraftTypeBaseModel("01-01-001", "حرفي صانع الخبز التقليدي", 
                    "صناعة الخبز التقليدي", ""),

                new CraftTypeBaseModel("01-02-001", "حرفي صانع الحلويات", 
                    "صناعة الحلويات التقليدية", "")
            };
        }

        /// <summary>
        /// Quick test method that can be called from anywhere in the application.
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                var task = RunSearchTests();
                task.Wait();
                string results = task.Result;

                // Log the results
                UFU2.Common.LoggingService.LogInfo("Search Test Results:\n" + results, "TestSearchImprovements");

                // Also output to console if available
                Console.WriteLine(results);
            }
            catch (Exception ex)
            {
                UFU2.Common.LoggingService.LogError($"Error running search tests: {ex.Message}", "TestSearchImprovements");
            }
        }

        /// <summary>
        /// Test the specific search issue with real CraftTypeBaseService.
        /// This method tests the actual search functionality using the database.
        /// </summary>
        public static async Task<string> TestRealSearchFunctionality()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("=== Real UFU2 Search Test ===");
            results.AppendLine($"Test Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                // Get the actual CraftTypeBaseService from ServiceLocator
                var craftTypeService = UFU2.Services.ServiceLocator.GetService<CraftTypeBaseService>();
                if (craftTypeService == null)
                {
                    results.AppendLine("❌ CraftTypeBaseService not available from ServiceLocator");
                    return results.ToString();
                }

                string searchTerm = "حرفي نجار وصانع";
                results.AppendLine($"Testing search term: '{searchTerm}'");
                results.AppendLine();

                // Test the enhanced search
                var searchResults = await craftTypeService.SearchByDescriptionEnhancedAsync(searchTerm, 10, 0.3);

                results.AppendLine($"Enhanced Search Results ({searchResults.Count} found):");
                for (int i = 0; i < searchResults.Count; i++)
                {
                    var result = searchResults[i];
                    results.AppendLine($"   {i + 1}. {result.Code}: {result.Description}");
                }
                results.AppendLine();

                // Check if the expected result is in the top results
                bool foundExpectedResult = searchResults.Any(r =>
                    r.Description.Contains("نجار") &&
                    r.Description.Contains("صانع") &&
                    r.Description.Contains("الأثاث"));

                results.AppendLine("=== Validation ===");
                results.AppendLine($"Expected: Result containing 'نجار', 'صانع', and 'الأثاث' should be in top results");
                results.AppendLine($"Found expected result: {(foundExpectedResult ? "✅ YES" : "❌ NO")}");

                if (foundExpectedResult)
                {
                    var expectedResult = searchResults.First(r =>
                        r.Description.Contains("نجار") &&
                        r.Description.Contains("صانع") &&
                        r.Description.Contains("الأثاث"));
                    int position = searchResults.IndexOf(expectedResult) + 1;
                    results.AppendLine($"Position in results: {position}");
                    results.AppendLine($"Description: {expectedResult.Description}");
                }

                return results.ToString();
            }
            catch (Exception ex)
            {
                results.AppendLine($"❌ ERROR: {ex.Message}");
                results.AppendLine($"Stack trace: {ex.StackTrace}");
                return results.ToString();
            }
        }
    }
}
