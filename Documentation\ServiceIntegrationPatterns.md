# UFU2 Service Integration Patterns Documentation

## Overview

This comprehensive guide documents UFU2's service integration patterns, covering ServiceLocator dependency injection, cross-service communication, error handling integration, and MVVM service coordination. All patterns follow UFU2's established architectural principles while maintaining consistency with MaterialDesign styling and Arabic RTL support.

## Table of Contents

1. [ServiceLocator Registration and Resolution Patterns](#servicelocator-registration-and-resolution-patterns)
2. [Cross-Service Communication Examples](#cross-service-communication-examples)
3. [Error Handling Integration Across Services](#error-handling-integration-across-services)
4. [Service Lifecycle Management](#service-lifecycle-management)
5. [Async Service Patterns](#async-service-patterns)
6. [Service Testing and Validation Patterns](#service-testing-and-validation-patterns)
7. [Performance Optimization in Service Integration](#performance-optimization-in-service-integration)
8. [MVVM Service Integration](#mvvm-service-integration)

---

## ServiceLocator Registration and Resolution Patterns

### Service Registration During Application Startup

UFU2 uses a two-phase service registration approach to handle dependencies properly:

<augment_code_snippet path="App.xaml.cs" mode="EXCERPT">
````csharp
protected override async void OnStartup(StartupEventArgs e)
{
    try
    {
        // Phase 1: Initialize basic ServiceLocator
        ServiceLocator.Initialize();

        // Phase 2: Initialize database services with dependencies
        await ServiceLocator.InitializeDatabaseServicesAsync();
        
        base.OnStartup(e);
    }
    catch (Exception ex)
    {
        ErrorManager.HandleErrorToast(ex, "فشل في تهيئة التطبيق", "خطأ في بدء التشغيل");
    }
}
````
</augment_code_snippet>

### Phase 1: Basic Service Registration

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
public static void Initialize()
{
    try
    {
        LoggingService.LogDebug("Initializing ServiceLocator", "ServiceLocator");

        // Register basic services that don't depend on database
        RegisterService<IToastService>(new ToastServiceWrapper());
        RegisterService<ValidationService>(new ValidationService());
        RegisterService<IWindowChromeService>(new WindowChromeService());

        _isInitialized = true;
        LoggingService.LogDebug("ServiceLocator initialized successfully", "ServiceLocator");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error initializing ServiceLocator: {ex.Message}", "ServiceLocator");
        throw;
    }
}
````
</augment_code_snippet>

### Phase 2: Database Service Registration

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
public static async Task InitializeDatabaseServicesAsync()
{
    try
    {
        // Initialize core database service
        var databaseService = new DatabaseService();
        RegisterService(databaseService);

        // Initialize database migration service
        var migrationService = new DatabaseMigrationService(databaseService);
        RegisterService(migrationService);

        // Apply database schema migrations
        await migrationService.InitializeSchemaAsync();

        // Initialize UID generation service
        var uidGenerationService = new UIDGenerationService(databaseService);
        RegisterService(uidGenerationService);

        // Initialize client database service with dependencies
        var clientDatabaseService = new ClientDatabaseService(databaseService, uidGenerationService);
        RegisterService(clientDatabaseService);

        // Initialize validation service with database dependency
        var clientValidationService = new ClientValidationService(databaseService);
        RegisterService(clientValidationService);

        LoggingService.LogDebug("Database services initialized successfully", "ServiceLocator");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error initializing database services: {ex.Message}", "ServiceLocator");
        throw;
    }
}
````
</augment_code_snippet>

### Service Resolution Patterns

UFU2 provides multiple patterns for service resolution based on usage scenarios:

```csharp
public class ServiceResolutionExamples
{
    // Pattern 1: Direct resolution (throws exception if not found)
    public void DirectResolution()
    {
        var clientService = ServiceLocator.GetService<ClientDatabaseService>();
        // Use service directly - guaranteed to be non-null or exception thrown
    }

    // Pattern 2: Safe resolution with null checking
    public void SafeResolution()
    {
        if (ServiceLocator.TryGetService<ClientDatabaseService>(out var clientService))
        {
            // Service is available, use it
            await clientService.CreateClientAsync(clientData);
        }
        else
        {
            // Handle service not available scenario
            LoggingService.LogWarning("ClientDatabaseService not available", "ServiceResolution");
        }
    }

    // Pattern 3: Service availability checking
    public void AvailabilityCheck()
    {
        if (ServiceLocator.IsServiceRegistered<ClientDatabaseService>())
        {
            var clientService = ServiceLocator.GetService<ClientDatabaseService>();
            // Proceed with service usage
        }
    }
}
```

### Service Registration Validation

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Validates that all required database services are registered
/// </summary>
public static bool ValidateDatabaseServices()
{
    var requiredServices = new[]
    {
        typeof(DatabaseService),
        typeof(DatabaseMigrationService),
        typeof(UIDGenerationService),
        typeof(ClientDatabaseService),
        typeof(DatabasePerformanceMonitoringService),
        typeof(EnhancedDatabaseService),
        typeof(DatabaseSchemaValidator),
        typeof(ActivityTypeBaseService),
        typeof(FileCheckBusinessRuleService),
        typeof(ClientValidationService)
    };

    bool allServicesRegistered = true;
    foreach (var serviceType in requiredServices)
    {
        if (!_services.ContainsKey(serviceType))
        {
            LoggingService.LogWarning($"Required service {serviceType.Name} is not registered", "ServiceLocator");
            allServicesRegistered = false;
        }
    }

    return allServicesRegistered;
}
````
</augment_code_snippet>

---

## Cross-Service Communication Examples

### ClientDatabaseService Integration Pattern

UFU2's ClientDatabaseService demonstrates comprehensive cross-service communication:

```mermaid
graph TB
    subgraph "ClientDatabaseService Integration"
        CDS[ClientDatabaseService] --> UGS[UIDGenerationService]
        CDS --> FCBRS[FileCheckBusinessRuleService]
        CDS --> DS[DatabaseService]
        CDS --> EM[ErrorManager]
        CDS --> LS[LoggingService]
        
        UGS --> DS
        FCBRS --> DS
        FCBRS --> EM
        FCBRS --> LS
        
        subgraph "Service Dependencies"
            UGS --> EM
            UGS --> LS
            DS --> EM
            DS --> LS
        end
    end
```

### Service Dependency Injection Pattern

<augment_code_snippet path="Services/ClientDatabaseService.cs" mode="EXCERPT">
````csharp
public class ClientDatabaseService : IDisposable
{
    private readonly DatabaseService _databaseService;
    private readonly UIDGenerationService _uidGenerationService;
    private readonly FileCheckBusinessRuleService _fileCheckBusinessRuleService;

    /// <summary>
    /// Initializes a new instance with required service dependencies
    /// </summary>
    public ClientDatabaseService(DatabaseService databaseService, UIDGenerationService uidGenerationService)
    {
        _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
        _uidGenerationService = uidGenerationService ?? throw new ArgumentNullException(nameof(uidGenerationService));
        _fileCheckBusinessRuleService = new FileCheckBusinessRuleService(databaseService);
    }
}
````
</augment_code_snippet>

### Cross-Service Coordination Example

<augment_code_snippet path="Services/ClientDatabaseService.cs" mode="EXCERPT">
````csharp
private async Task CreateActivityAsync(IDbConnection connection, IDbTransaction transaction, 
    string clientUID, ActivityCreationData activityData, string? operationId = null)
{
    try
    {
        // Step 1: Generate Activity UID through UIDGenerationService
        string activityUID = await _uidGenerationService.GenerateActivityUIDAsync(clientUID, operationId);

        // Step 2: Insert activity record
        await InsertActivityEntityAsync(connection, transaction, activityUID, clientUID, activityData);

        // Step 3: Validate file check states through FileCheckBusinessRuleService
        if (activityData.FileCheckStates != null && activityData.FileCheckStates.Count > 0)
        {
            var validation = _fileCheckBusinessRuleService.ValidateFileCheckStates(
                activityData.ActivityType, activityData.FileCheckStates, false);
            
            if (!validation.IsValid)
            {
                var errorMessages = string.Join("; ", validation.Errors.SelectMany(e => e.Value));
                throw new InvalidOperationException($"File check validation failed: {errorMessages}");
            }

            await CreateFileCheckStatesAsync(connection, transaction, activityUID, activityData.FileCheckStates);
        }

        // Step 4: Ensure required file check states exist
        if (!string.IsNullOrWhiteSpace(activityData.ActivityType))
        {
            await _fileCheckBusinessRuleService.EnsureRequiredFileCheckStatesAsync(activityUID, activityData.ActivityType);
        }

        LoggingService.LogInfo($"Activity created successfully: {activityUID}", "ClientDatabaseService");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error creating activity: {ex.Message}", "ClientDatabaseService");
        throw;
    }
}
````
</augment_code_snippet>

### Service Communication Coordination

```csharp
public class ServiceCommunicationCoordinator
{
    private readonly ClientDatabaseService _clientService;
    private readonly ClientValidationService _validationService;
    private readonly FileCheckBusinessRuleService _fileCheckService;

    public ServiceCommunicationCoordinator()
    {
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
        _fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
    }

    public async Task<string> CreateCompleteClientAsync(ClientCreationData clientData)
    {
        try
        {
            // Step 1: Validate client data
            var validationResult = _validationService.ValidateClientCreation(clientData);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                throw new ValidationException(errorMessage);
            }

            // Step 2: Validate file check business rules for all activities
            foreach (var activity in clientData.Activities)
            {
                var fileCheckResult = _fileCheckService.ValidateFileCheckBusinessRules(
                    activity.ActivityType, activity.FileCheckStates, enforceCompletion: false);

                if (!fileCheckResult.IsValid)
                {
                    foreach (var error in fileCheckResult.Errors)
                    {
                        ErrorManager.ShowUserErrorToast(error.Value, "خطأ في التحقق من الملفات", "FileCheckValidation");
                    }
                    throw new ValidationException("File check validation failed");
                }
            }

            // Step 3: Create client with all coordinated services
            return await _clientService.CreateClientAsync(clientData);
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في إنشاء العميل");
            throw;
        }
    }
}
```

---

## Error Handling Integration Across Services

### Centralized Error Handling Pattern

All UFU2 services integrate with ErrorManager for consistent error handling:

```csharp
public class ServiceErrorHandlingPattern
{
    public async Task<T> ExecuteServiceOperationAsync<T>(
        Func<Task<T>> serviceOperation,
        string operationName,
        string arabicErrorMessage,
        string source = "ServiceOperation")
    {
        return await ErrorManager.ExecuteWithErrorHandlingAsync(async () =>
        {
            LoggingService.LogInfo($"Starting {operationName}", source);
            
            var result = await serviceOperation();
            
            LoggingService.LogInfo($"Completed {operationName} successfully", source);
            return result;
            
        }, source, operationName, arabicErrorMessage, LogLevel.Error);
    }
}
```

### Service-Specific Error Handling

<augment_code_snippet path="Services/ActivityTypeBaseService.cs" mode="EXCERPT">
````csharp
public async Task<List<ActivityTypeBaseModel>> GetAllAsync()
{
    try
    {
        using var connection = _databaseService.CreateConnection();
        await connection.OpenAsync().ConfigureAwait(false);

        var result = await connection.QueryAsync<ActivityTypeBaseModel>(SelectAllSql).ConfigureAwait(false);
        return result.ToList();
    }
    catch (Exception ex)
    {
        ErrorManager.HandleErrorToast(ex, "فشل في جلب بيانات أنواع الأنشطة", "خطأ في قاعدة البيانات",
                               LogLevel.Error, "ActivityTypeBaseService");
        throw;
    }
}
````
</augment_code_snippet>

### Error Propagation Patterns

```csharp
public class ErrorPropagationExample
{
    private readonly ClientDatabaseService _clientService;
    private readonly UIDGenerationService _uidService;

    public async Task<string> CreateClientWithErrorPropagationAsync(ClientCreationData clientData)
    {
        try
        {
            // Service call that may throw specific exceptions
            var clientUID = await _uidService.GenerateClientUIDAsync(clientData.NameFr);
            
            // Propagate UID to next service call
            return await _clientService.CreateClientAsync(clientData);
        }
        catch (ArgumentException ex)
        {
            // Handle specific validation errors with Arabic messages
            ErrorManager.ShowUserErrorToast("بيانات العميل غير صحيحة", "خطأ في البيانات", "ClientCreation");
            LoggingService.LogWarning($"Client validation failed: {ex.Message}", "ClientCreation");
            throw;
        }
        catch (InvalidOperationException ex)
        {
            // Handle business rule violations
            ErrorManager.ShowUserErrorToast("لا يمكن إنشاء العميل - انتهاك قواعد العمل", "خطأ في العملية", "ClientCreation");
            LoggingService.LogError($"Business rule violation: {ex.Message}", "ClientCreation");
            throw;
        }
        catch (Exception ex)
        {
            // Handle unexpected errors
            ErrorManager.HandleError(ex, "خطأ غير متوقع في إنشاء العميل");
            throw;
        }
    }
}
```

### Arabic Error Message Integration

```csharp
public static class ServiceErrorMessages
{
    // Database service errors
    public const string DatabaseConnectionFailed = "فشل في الاتصال بقاعدة البيانات";
    public const string DatabaseOperationFailed = "فشل في عملية قاعدة البيانات";
    
    // Validation service errors
    public const string ValidationFailed = "فشل في التحقق من صحة البيانات";
    public const string RequiredFieldMissing = "حقل مطلوب مفقود";
    
    // UID generation errors
    public const string UIDGenerationFailed = "فشل في توليد المعرف الفريد";
    public const string DuplicateUIDDetected = "تم اكتشاف معرف مكرر";
    
    // File check errors
    public const string FileCheckValidationFailed = "فشل في التحقق من الملفات";
    public const string RequiredFileCheckMissing = "فحص ملف مطلوب مفقود";
    
    // Service integration errors
    public const string ServiceNotAvailable = "الخدمة غير متاحة";
    public const string ServiceInitializationFailed = "فشل في تهيئة الخدمة";
}
```

---

## Service Lifecycle Management

### Service Initialization Pattern

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Manages the lifecycle of a service, ensuring proper initialization and disposal
/// </summary>
public static async Task<T> ManageServiceLifecycleAsync<T>(
    Func<T> serviceFactory, 
    Func<T, Task>? initializeAction = null) where T : class
{
    try
    {
        LoggingService.LogDebug($"Managing lifecycle for service {typeof(T).Name}", "ServiceLocator");

        // Create the service
        var service = serviceFactory();
        
        // Initialize if needed
        if (initializeAction != null)
        {
            await initializeAction(service);
        }

        // Register the service
        RegisterService(service);

        LoggingService.LogDebug($"Service {typeof(T).Name} lifecycle managed successfully", "ServiceLocator");
        return service;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error managing lifecycle for service {typeof(T).Name}: {ex.Message}", "ServiceLocator");
        throw;
    }
}
````
</augment_code_snippet>

### Service Disposal Pattern

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Disposes all registered services that implement IDisposable
/// Services are disposed in reverse order of registration to handle dependencies
/// </summary>
public static void DisposeServices()
{
    try
    {
        LoggingService.LogDebug("Disposing registered services", "ServiceLocator");

        // Get services in reverse order to handle dependencies properly
        var servicesInReverseOrder = _services.Values.Reverse().ToList();

        foreach (var service in servicesInReverseOrder)
        {
            if (service is IDisposable disposableService)
            {
                try
                {
                    disposableService.Dispose();
                    LoggingService.LogDebug($"Disposed service: {service.GetType().Name}", "ServiceLocator");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError($"Error disposing service {service.GetType().Name}: {ex.Message}", "ServiceLocator");
                }
            }
        }

        _services.Clear();
        LoggingService.LogDebug("All services disposed", "ServiceLocator");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error during service disposal: {ex.Message}", "ServiceLocator");
    }
}
````
</augment_code_snippet>

### Resource Management in Services

```csharp
public class ServiceResourceManagement : IDisposable
{
    private readonly DatabaseService _databaseService;
    private readonly Timer _cleanupTimer;
    private bool _disposed = false;

    public ServiceResourceManagement(DatabaseService databaseService)
    {
        _databaseService = databaseService;
        
        // Setup periodic cleanup
        _cleanupTimer = new Timer(PerformCleanup, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    private void PerformCleanup(object? state)
    {
        try
        {
            // Perform periodic resource cleanup
            LoggingService.LogDebug("Performing periodic service cleanup", "ServiceResourceManagement");
            
            // Clean up any cached data, temporary files, etc.
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error during periodic cleanup: {ex.Message}", "ServiceResourceManagement");
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _cleanupTimer?.Dispose();
                _databaseService?.Dispose();
            }

            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
```

---

## Async Service Patterns

### ConfigureAwait(false) Pattern

UFU2 services consistently use ConfigureAwait(false) for library code:

<augment_code_snippet path="Services/ActivityTypeBaseService.cs" mode="EXCERPT">
````csharp
public async Task<List<ActivityTypeBaseModel>> GetAllAsync()
{
    try
    {
        using var connection = _databaseService.CreateConnection();
        await connection.OpenAsync().ConfigureAwait(false);

        var result = await connection.QueryAsync<ActivityTypeBaseModel>(SelectAllSql).ConfigureAwait(false);
        return result.ToList();
    }
    catch (Exception ex)
    {
        ErrorManager.HandleErrorToast(ex, "فشل في جلب بيانات أنواع الأنشطة", "خطأ في قاعدة البيانات",
                               LogLevel.Error, "ActivityTypeBaseService");
        throw;
    }
}
````
</augment_code_snippet>

### Background Task Coordination

<augment_code_snippet path="ViewModels/MultipleActivitiesDialogViewModel.cs" mode="EXCERPT">
````csharp
private async Task PerformSearchAsync(string searchTerm)
{
    try
    {
        IsSearching = true;

        // Add a small delay to debounce rapid typing
        await Task.Delay(300).ConfigureAwait(false);

        // Check if search term is still the same (user might have continued typing)
        if (searchTerm != SearchText)
            return;

        // Use database-level search with caching for improved performance
        var searchResults = await _activityTypeService.SearchByDescriptionAsync(searchTerm, 10).ConfigureAwait(false);

        // Update UI on the UI thread
        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
        {
            // Replace collection efficiently instead of Clear + Add
            var newResults = new ObservableCollection<ActivityTypeBaseModel>(searchResults);
            SearchResults = newResults;
        });
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error performing search: {ex.Message}", "MultipleActivitiesDialogViewModel");
    }
    finally
    {
        IsSearching = false;
    }
}
````
</augment_code_snippet>

### Thread-Safe Service Operations

```csharp
public class ThreadSafeServicePattern
{
    private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
    private readonly ClientDatabaseService _clientService;

    public async Task<string> CreateClientSafelyAsync(ClientCreationData clientData)
    {
        await _semaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            // Perform thread-safe operation
            return await _clientService.CreateClientAsync(clientData).ConfigureAwait(false);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public void Dispose()
    {
        _semaphore?.Dispose();
    }
}
```

### Async Service Coordination

```csharp
public class AsyncServiceCoordination
{
    public async Task<CompleteClientResult> CreateCompleteClientAsync(ClientCreationData clientData)
    {
        var tasks = new List<Task>();
        var results = new CompleteClientResult();

        try
        {
            // Start multiple async operations concurrently
            var clientTask = CreateClientAsync(clientData);
            var validationTask = ValidateClientDataAsync(clientData);
            var fileCheckTask = ValidateFileChecksAsync(clientData.Activities);

            // Wait for all operations to complete
            await Task.WhenAll(clientTask, validationTask, fileCheckTask).ConfigureAwait(false);

            // Collect results
            results.ClientUID = await clientTask;
            results.ValidationResult = await validationTask;
            results.FileCheckResult = await fileCheckTask;

            return results;
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في إنشاء العميل المتكامل");
            throw;
        }
    }
}

---

## Service Testing and Validation Patterns

### Service Validation Framework

UFU2 provides comprehensive service validation patterns for ensuring service integrity:

```csharp
public class ServiceValidationFramework
{
    public static async Task<ServiceValidationResult> ValidateServiceIntegrityAsync()
    {
        var result = new ServiceValidationResult();

        try
        {
            // Validate ServiceLocator state
            result.ServiceLocatorValid = ServiceLocator.IsInitialized;

            // Validate required services are registered
            result.RequiredServicesValid = ServiceLocator.ValidateDatabaseServices();

            // Validate database connectivity
            if (ServiceLocator.TryGetService<DatabaseService>(out var dbService))
            {
                result.DatabaseConnectivityValid = await ValidateDatabaseConnectivityAsync(dbService);
            }

            // Validate service dependencies
            result.ServiceDependenciesValid = await ValidateServiceDependenciesAsync();

            // Overall validation result
            result.IsValid = result.ServiceLocatorValid &&
                           result.RequiredServicesValid &&
                           result.DatabaseConnectivityValid &&
                           result.ServiceDependenciesValid;

            LoggingService.LogInfo($"Service validation completed. Valid: {result.IsValid}", "ServiceValidation");
            return result;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Service validation failed: {ex.Message}", "ServiceValidation");
            result.IsValid = false;
            result.ValidationErrors.Add(ex.Message);
            return result;
        }
    }

    private static async Task<bool> ValidateDatabaseConnectivityAsync(DatabaseService dbService)
    {
        try
        {
            using var connection = dbService.CreateConnection();
            await connection.OpenAsync().ConfigureAwait(false);
            return true;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Database connectivity validation failed: {ex.Message}", "ServiceValidation");
            return false;
        }
    }

    private static async Task<bool> ValidateServiceDependenciesAsync()
    {
        try
        {
            // Validate ClientDatabaseService dependencies
            if (ServiceLocator.TryGetService<ClientDatabaseService>(out var clientService))
            {
                // Test basic operation
                var testResult = await clientService.GetClientCountAsync();
                LoggingService.LogDebug($"ClientDatabaseService validation: {testResult} clients", "ServiceValidation");
            }

            // Validate UIDGenerationService
            if (ServiceLocator.TryGetService<UIDGenerationService>(out var uidService))
            {
                // Test UID generation without persisting
                var testUID = await uidService.GenerateClientUIDAsync("TestClient");
                LoggingService.LogDebug($"UIDGenerationService validation: Generated {testUID}", "ServiceValidation");
            }

            return true;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Service dependency validation failed: {ex.Message}", "ServiceValidation");
            return false;
        }
    }
}

public class ServiceValidationResult
{
    public bool IsValid { get; set; }
    public bool ServiceLocatorValid { get; set; }
    public bool RequiredServicesValid { get; set; }
    public bool DatabaseConnectivityValid { get; set; }
    public bool ServiceDependenciesValid { get; set; }
    public List<string> ValidationErrors { get; set; } = new List<string>();

    public string GetSummary()
    {
        var status = IsValid ? "VALID" : "INVALID";
        var errorCount = ValidationErrors.Count;
        return $"Service Validation: {status} ({errorCount} errors)";
    }
}
```

### Integration Testing Patterns

```csharp
public class ServiceIntegrationTesting
{
    public static async Task<bool> TestCompleteClientWorkflowAsync()
    {
        try
        {
            LoggingService.LogInfo("Starting complete client workflow test", "IntegrationTest");

            // Test data preparation
            var testClientData = new ClientCreationData
            {
                NameFr = "TestClient",
                NameAr = "عميل تجريبي",
                Activities = new List<ActivityCreationData>
                {
                    new ActivityCreationData
                    {
                        ActivityType = "MainCommercial",
                        ActivityCodes = new List<int> { 123456 }
                    }
                }
            };

            // Test service coordination
            var clientService = ServiceLocator.GetService<ClientDatabaseService>();
            var validationService = ServiceLocator.GetService<ClientValidationService>();
            var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();

            // Step 1: Test validation service
            var validationResult = validationService.ValidateClientCreation(testClientData);
            if (!validationResult.IsValid)
            {
                LoggingService.LogWarning("Client validation failed in integration test", "IntegrationTest");
                return false;
            }

            // Step 2: Test file check business rules
            foreach (var activity in testClientData.Activities)
            {
                var fileCheckResult = fileCheckService.ValidateFileCheckBusinessRules(
                    activity.ActivityType, new Dictionary<string, bool>(), enforceCompletion: false);

                if (!fileCheckResult.IsValid)
                {
                    LoggingService.LogWarning("File check validation failed in integration test", "IntegrationTest");
                    return false;
                }
            }

            // Step 3: Test client creation (without actually persisting)
            // This would require a test database or transaction rollback
            LoggingService.LogInfo("Integration test completed successfully", "IntegrationTest");
            return true;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Integration test failed: {ex.Message}", "IntegrationTest");
            return false;
        }
    }
}
```

---

## Performance Optimization in Service Integration

### Service Caching Patterns

```csharp
public class ServiceCachingPattern
{
    private readonly ConcurrentDictionary<string, object> _cache = new ConcurrentDictionary<string, object>();
    private readonly ActivityTypeBaseService _activityService;
    private readonly Timer _cacheCleanupTimer;

    public ServiceCachingPattern()
    {
        _activityService = ServiceLocator.GetService<ActivityTypeBaseService>();

        // Setup cache cleanup every 10 minutes
        _cacheCleanupTimer = new Timer(CleanupCache, null,
            TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
    }

    public async Task<List<ActivityTypeBaseModel>> GetCachedActivitiesAsync()
    {
        const string cacheKey = "AllActivities";

        if (_cache.TryGetValue(cacheKey, out var cachedResult))
        {
            LoggingService.LogDebug("Returning cached activities", "ServiceCaching");
            return (List<ActivityTypeBaseModel>)cachedResult;
        }

        // Cache miss - fetch from service
        var activities = await _activityService.GetAllAsync().ConfigureAwait(false);

        // Cache the result with expiration
        var cacheEntry = new CacheEntry<List<ActivityTypeBaseModel>>
        {
            Data = activities,
            ExpirationTime = DateTime.UtcNow.AddMinutes(30)
        };

        _cache.TryAdd(cacheKey, cacheEntry);
        LoggingService.LogDebug($"Cached {activities.Count} activities", "ServiceCaching");

        return activities;
    }

    private void CleanupCache(object? state)
    {
        try
        {
            var expiredKeys = new List<string>();
            var currentTime = DateTime.UtcNow;

            foreach (var kvp in _cache)
            {
                if (kvp.Value is CacheEntry<object> entry && entry.ExpirationTime < currentTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                LoggingService.LogDebug($"Cleaned up {expiredKeys.Count} expired cache entries", "ServiceCaching");
            }
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Cache cleanup error: {ex.Message}", "ServiceCaching");
        }
    }
}

public class CacheEntry<T>
{
    public T Data { get; set; }
    public DateTime ExpirationTime { get; set; }
}
```

### Connection Pooling and Resource Management

```csharp
public class OptimizedServicePattern
{
    private readonly DatabaseService _databaseService;
    private readonly SemaphoreSlim _connectionSemaphore;

    public OptimizedServicePattern()
    {
        _databaseService = ServiceLocator.GetService<DatabaseService>();

        // Limit concurrent database connections
        _connectionSemaphore = new SemaphoreSlim(10, 10);
    }

    public async Task<T> ExecuteWithConnectionPoolingAsync<T>(Func<IDbConnection, Task<T>> operation)
    {
        await _connectionSemaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync().ConfigureAwait(false);

            return await operation(connection).ConfigureAwait(false);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task<List<T>> ExecuteBatchOperationAsync<T>(
        IEnumerable<Func<IDbConnection, IDbTransaction, Task<T>>> operations)
    {
        var results = new List<T>();

        await _connectionSemaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            using var connection = _databaseService.CreateConnection();
            await connection.OpenAsync().ConfigureAwait(false);

            using var transaction = connection.BeginTransaction();
            try
            {
                foreach (var operation in operations)
                {
                    var result = await operation(connection, transaction).ConfigureAwait(false);
                    results.Add(result);
                }

                transaction.Commit();
                LoggingService.LogDebug($"Batch operation completed: {results.Count} operations", "OptimizedService");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                LoggingService.LogError($"Batch operation failed: {ex.Message}", "OptimizedService");
                throw;
            }
        }
        finally
        {
            _connectionSemaphore.Release();
        }

        return results;
    }
}
```

### Performance Monitoring Integration

<augment_code_snippet path="Services/ComprehensiveMonitoringService.cs" mode="EXCERPT">
````csharp
public async Task<MonitoringReport> GenerateMonitoringReportAsync(DateTime fromTime, DateTime toTime)
{
    try
    {
        var report = new MonitoringReport
        {
            ReportPeriod = $"{fromTime:yyyy-MM-dd} to {toTime:yyyy-MM-dd}",
            GeneratedAt = DateTime.UtcNow
        };

        // Get performance metrics
        var performanceReport = _performanceMonitoringService.GeneratePerformanceReport(fromTime, toTime);
        report.PerformanceMetrics = performanceReport;

        // Get index analysis
        report.IndexAnalysis = await _performanceMonitoringService.AnalyzeIndexEffectivenessAsync();

        // Get UID generation statistics
        report.UIDGenerationStats = await _uidGenerationService.GetUIDGenerationStatsAsync();

        LoggingService.LogInfo($"Generated monitoring report for period {report.ReportPeriod}", "ComprehensiveMonitoringService");
        return report;
    }
    catch (Exception ex)
    {
        ErrorManager.HandleErrorToast(ex, "فشل في إنشاء تقرير المراقبة", "خطأ في التقرير", LogLevel.Error, "ComprehensiveMonitoringService");
        throw;
    }
}
````
</augment_code_snippet>

---

## MVVM Service Integration

### ViewModel Service Access Pattern

<augment_code_snippet path="Services/ServiceLocator.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Example of how ViewModels should access database services through ServiceLocator
/// This demonstrates the proper pattern for dependency injection in UFU2
/// </summary>
/// <example>
/// // In ViewModel constructor:
/// if (ServiceLocator.TryGetService&lt;ClientDatabaseService&gt;(out var clientDbService))
/// {
///     _clientDatabaseService = clientDbService;
/// }
///
/// if (ServiceLocator.TryGetService&lt;UIDGenerationService&gt;(out var uidService))
/// {
///     _uidGenerationService = uidService;
/// }
///
/// // In ViewModel methods:
/// var clientUID = await _uidGenerationService.GenerateClientUIDAsync(nameFr);
/// await _clientDatabaseService.CreateClientAsync(clientData);
/// </example>
````
</augment_code_snippet>

### Complete ViewModel Service Integration

```csharp
public class ClientManagementViewModel : BaseViewModel
{
    #region Private Fields

    private readonly ClientDatabaseService _clientDatabaseService;
    private readonly ClientValidationService _clientValidationService;
    private readonly FileCheckBusinessRuleService _fileCheckBusinessRuleService;
    private readonly UIDGenerationService _uidGenerationService;

    private bool _isLoading;
    private string _statusMessage = string.Empty;

    #endregion

    #region Constructor

    public ClientManagementViewModel()
    {
        // Initialize services through ServiceLocator
        _clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
        _clientValidationService = ServiceLocator.GetService<ClientValidationService>();
        _fileCheckBusinessRuleService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
        _uidGenerationService = ServiceLocator.GetService<UIDGenerationService>();

        // Initialize commands
        SaveClientCommand = new RelayCommand(async () => await SaveClientAsync(), () => CanSaveClient);
        ValidateClientCommand = new RelayCommand(async () => await ValidateClientAsync());
        LoadClientsCommand = new RelayCommand(async () => await LoadClientsAsync());
    }

    #endregion

    #region Properties

    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    public bool CanSaveClient => !IsLoading && ClientData != null;

    #endregion

    #region Commands

    public RelayCommand SaveClientCommand { get; }
    public RelayCommand ValidateClientCommand { get; }
    public RelayCommand LoadClientsCommand { get; }

    #endregion

    #region Service Integration Methods

    private async Task SaveClientAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "جاري حفظ بيانات العميل...";

            // Step 1: Validate client data
            var validationResult = _clientValidationService.ValidateClientCreation(ClientData);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                ErrorManager.ShowUserWarningToast(errorMessage, "بيانات غير صحيحة", "ClientManagementViewModel");
                return;
            }

            // Step 2: Validate file check business rules
            foreach (var activity in ClientData.Activities)
            {
                var fileCheckResult = _fileCheckBusinessRuleService.ValidateFileCheckBusinessRules(
                    activity.ActivityType, activity.FileCheckStates, enforceCompletion: false);

                if (!fileCheckResult.IsValid)
                {
                    foreach (var error in fileCheckResult.Errors)
                    {
                        ErrorManager.ShowUserErrorToast(error.Value, "خطأ في التحقق من الملفات", "FileCheckValidation");
                    }
                    return;
                }
            }

            // Step 3: Save client through database service
            StatusMessage = "جاري إنشاء معرف العميل...";
            var clientUID = await _clientDatabaseService.CreateClientAsync(ClientData);

            // Step 4: Success notification
            StatusMessage = $"تم حفظ العميل بنجاح - معرف العميل: {clientUID}";
            ErrorManager.ShowUserSuccessToast($"تم حفظ بيانات العميل بنجاح\nمعرف العميل: {clientUID}", "تم الحفظ");

            // Step 5: Refresh client list
            await LoadClientsAsync();
        }
        catch (Exception ex)
        {
            StatusMessage = "فشل في حفظ بيانات العميل";
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task ValidateClientAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "جاري التحقق من صحة البيانات...";

            var validationResult = _clientValidationService.ValidateClientCreation(ClientData);

            if (validationResult.IsValid)
            {
                StatusMessage = "البيانات صحيحة ومكتملة";
                ErrorManager.ShowUserSuccessToast("تم التحقق من صحة البيانات بنجاح", "التحقق مكتمل");
            }
            else
            {
                var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                StatusMessage = "توجد أخطاء في البيانات";
                ErrorManager.ShowUserWarningToast(errorMessage, "أخطاء في البيانات", "ClientValidation");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "فشل في التحقق من البيانات";
            ErrorManager.HandleError(ex, "خطأ في التحقق من صحة البيانات");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadClientsAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "جاري تحميل قائمة العملاء...";

            var clients = await _clientDatabaseService.GetAllClientsAsync();

            // Update UI on main thread
            Application.Current.Dispatcher.Invoke(() =>
            {
                ClientsList.Clear();
                foreach (var client in clients)
                {
                    ClientsList.Add(client);
                }
            });

            StatusMessage = $"تم تحميل {clients.Count} عميل";
        }
        catch (Exception ex)
        {
            StatusMessage = "فشل في تحميل قائمة العملاء";
            ErrorManager.HandleError(ex, "خطأ في تحميل بيانات العملاء");
        }
        finally
        {
            IsLoading = false;
        }
    }

    #endregion
}
```

### Command Pattern with Service Integration

```csharp
public class ServiceIntegratedCommand : ICommand
{
    private readonly Func<Task> _executeAsync;
    private readonly Func<bool> _canExecute;
    private readonly string _operationName;
    private readonly string _arabicErrorMessage;
    private bool _isExecuting;

    public ServiceIntegratedCommand(
        Func<Task> executeAsync,
        Func<bool> canExecute,
        string operationName,
        string arabicErrorMessage)
    {
        _executeAsync = executeAsync;
        _canExecute = canExecute;
        _operationName = operationName;
        _arabicErrorMessage = arabicErrorMessage;
    }

    public event EventHandler? CanExecuteChanged
    {
        add => CommandManager.RequerySuggested += value;
        remove => CommandManager.RequerySuggested -= value;
    }

    public bool CanExecute(object? parameter)
    {
        return !_isExecuting && (_canExecute?.Invoke() ?? true);
    }

    public async void Execute(object? parameter)
    {
        if (!CanExecute(parameter))
            return;

        try
        {
            _isExecuting = true;
            CommandManager.InvalidateRequerySuggested();

            LoggingService.LogInfo($"Executing command: {_operationName}", "ServiceIntegratedCommand");

            await _executeAsync();

            LoggingService.LogInfo($"Command executed successfully: {_operationName}", "ServiceIntegratedCommand");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Command execution failed: {_operationName} - {ex.Message}", "ServiceIntegratedCommand");
            ErrorManager.HandleError(ex, _arabicErrorMessage);
        }
        finally
        {
            _isExecuting = false;
            CommandManager.InvalidateRequerySuggested();
        }
    }
}
```

---

## Best Practices Summary

### Do's ✅

#### Service Registration
- **Use two-phase initialization** for services with dependencies
- **Validate service registration** during application startup
- **Register services in dependency order** to avoid circular references
- **Use ServiceLocator.TryGetService** for optional dependencies
- **Log all service registration activities** for debugging

#### Cross-Service Communication
- **Inject dependencies through constructors** when possible
- **Use async/await with ConfigureAwait(false)** in service methods
- **Coordinate transactions** across multiple services
- **Validate data at service boundaries** before processing
- **Handle service failures gracefully** with fallback mechanisms

#### Error Handling
- **Use ErrorManager consistently** across all services
- **Provide Arabic error messages** for user-facing errors
- **Log technical details** while showing user-friendly messages
- **Implement proper exception propagation** between services
- **Use operation IDs** for error deduplication

#### Performance Optimization
- **Cache frequently accessed data** with appropriate expiration
- **Use connection pooling** for database operations
- **Implement batch operations** for multiple related tasks
- **Monitor service performance** with metrics collection
- **Optimize async operations** with proper threading

### Don'ts ❌

#### Service Registration
- **Don't register services multiple times** without proper handling
- **Don't ignore service registration failures** during startup
- **Don't create circular dependencies** between services
- **Don't access services before initialization** is complete
- **Don't forget to dispose services** during application shutdown

#### Cross-Service Communication
- **Don't create tight coupling** between unrelated services
- **Don't ignore transaction boundaries** in multi-service operations
- **Don't use synchronous calls** in async service methods
- **Don't bypass validation** when calling other services
- **Don't ignore service method return values** and error states

#### Error Handling
- **Don't show technical error details** to end users
- **Don't ignore exceptions** in service integration code
- **Don't use English error messages** for Arabic-speaking users
- **Don't create error handling inconsistencies** across services
- **Don't forget to log service integration failures**

#### Performance Optimization
- **Don't create memory leaks** with improper caching
- **Don't ignore connection limits** in database services
- **Don't perform blocking operations** on UI thread
- **Don't ignore performance monitoring** data
- **Don't create unnecessary service instances** or calls

This comprehensive documentation enables developers to implement consistent, efficient, and maintainable service integration patterns while following UFU2's established architectural principles and Arabic localization requirements.
```
