using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.ViewModels
{
    /// <summary>
    /// ViewModel for managing client contact information.
    /// Handles phone numbers and potentially other contact methods.
    /// Extracted from NewClientViewModel to improve maintainability and separation of concerns.
    /// </summary>
    public class ContactInformationViewModel : BaseViewModel
    {
        #region Private Fields

        private PhoneNumbersCollectionModel _phoneNumbers;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the phone numbers collection for personal information.
        /// This collection is used by the NPersonalView for managing multiple phone numbers.
        /// Data persists throughout the client creation process until Save or Cancel.
        /// </summary>
        public PhoneNumbersCollectionModel PhoneNumbers
        {
            get => _phoneNumbers;
            private set => SetProperty(ref _phoneNumbers, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to open the phone numbers management dialog.
        /// </summary>
        public ICommand ManagePhoneNumbersCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ContactInformationViewModel class.
        /// </summary>
        public ContactInformationViewModel()
        {
            _phoneNumbers = new PhoneNumbersCollectionModel();

            ManagePhoneNumbersCommand = new RelayCommand(
                execute: ManagePhoneNumbers,
                commandName: "ManagePhoneNumbers"
            );

            LoggingService.LogDebug("ContactInformationViewModel initialized", "ContactInformationViewModel");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validates the contact information data.
        /// </summary>
        /// <returns>True if all contact information is valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Phone numbers are optional, but if provided, they should be valid
                if (PhoneNumbers?.PhoneNumbers?.Count > 0)
                {
                    foreach (var phoneNumber in PhoneNumbers.PhoneNumbers)
                    {
                        if (string.IsNullOrWhiteSpace(phoneNumber.PhoneNumber))
                        {
                            LoggingService.LogDebug("Contact information validation failed: Empty phone number found", "ContactInformationViewModel");
                            return false;
                        }
                    }
                }

                LoggingService.LogDebug("Contact information validation passed", "ContactInformationViewModel");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error validating contact information: {ex.Message}", "ContactInformationViewModel");
                return false;
            }
        }

        /// <summary>
        /// Clears all contact information.
        /// </summary>
        public void Clear()
        {
            try
            {
                PhoneNumbers?.PhoneNumbers?.Clear();
                LoggingService.LogDebug("Contact information cleared", "ContactInformationViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing contact information: {ex.Message}", "ContactInformationViewModel");
            }
        }

        /// <summary>
        /// Gets the primary phone number for display purposes.
        /// </summary>
        /// <returns>The primary phone number or empty string if none exists</returns>
        public string GetPrimaryPhoneNumber()
        {
            try
            {
                var primaryPhone = PhoneNumbers?.PhoneNumbers?.FirstOrDefault(p => p.IsPrimary);
                return primaryPhone?.PhoneNumber ?? string.Empty;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting primary phone number: {ex.Message}", "ContactInformationViewModel");
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets all phone numbers as a formatted string for display.
        /// </summary>
        /// <returns>Formatted string of all phone numbers</returns>
        public string GetFormattedPhoneNumbers()
        {
            try
            {
                if (PhoneNumbers?.PhoneNumbers?.Count == 0)
                {
                    return "لا توجد أرقام هاتف";
                }

                var phoneList = PhoneNumbers?.PhoneNumbers?
                    .Where(p => !string.IsNullOrWhiteSpace(p.PhoneNumber))
                    .Select(p => p.IsPrimary ? $"{p.PhoneNumber} (أساسي)" : p.PhoneNumber)
                    .ToList();

                return phoneList?.Count > 0 ? string.Join(", ", phoneList) : "لا توجد أرقام هاتف";
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error formatting phone numbers: {ex.Message}", "ContactInformationViewModel");
                return "خطأ في عرض أرقام الهاتف";
            }
        }

        /// <summary>
        /// Adds a new phone number to the collection.
        /// </summary>
        /// <param name="phoneNumber">The phone number to add</param>
        /// <param name="phoneType">The type of phone number</param>
        /// <param name="isPrimary">Whether this is the primary phone number</param>
        public void AddPhoneNumber(string phoneNumber, int phoneType = 0, bool isPrimary = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    LoggingService.LogWarning("Cannot add empty phone number", "ContactInformationViewModel");
                    return;
                }

                var newPhoneNumber = new PhoneNumberModel
                {
                    PhoneNumber = phoneNumber.Trim(),
                    PhoneType = (PhoneType)phoneType,
                    IsPrimary = isPrimary
                };

                // If this is set as primary, make sure no other phone number is primary
                if (isPrimary)
                {
                    foreach (var existingPhone in PhoneNumbers.PhoneNumbers)
                    {
                        existingPhone.IsPrimary = false;
                    }
                }

                PhoneNumbers.PhoneNumbers.Add(newPhoneNumber);
                LoggingService.LogInfo($"Phone number added: {phoneNumber}", "ContactInformationViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error adding phone number: {ex.Message}", "ContactInformationViewModel");
            }
        }

        /// <summary>
        /// Removes a phone number from the collection.
        /// </summary>
        /// <param name="phoneNumber">The phone number model to remove</param>
        public void RemovePhoneNumber(PhoneNumberModel phoneNumber)
        {
            try
            {
                if (phoneNumber == null)
                {
                    LoggingService.LogWarning("Cannot remove null phone number", "ContactInformationViewModel");
                    return;
                }

                PhoneNumbers.PhoneNumbers.Remove(phoneNumber);
                LoggingService.LogInfo($"Phone number removed: {phoneNumber.PhoneNumber}", "ContactInformationViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error removing phone number: {ex.Message}", "ContactInformationViewModel");
            }
        }

        /// <summary>
        /// Sets a phone number as the primary contact number.
        /// </summary>
        /// <param name="phoneNumber">The phone number model to set as primary</param>
        public void SetAsPrimary(PhoneNumberModel phoneNumber)
        {
            try
            {
                if (phoneNumber == null)
                {
                    LoggingService.LogWarning("Cannot set null phone number as primary", "ContactInformationViewModel");
                    return;
                }

                // Clear primary flag from all phone numbers
                foreach (var phone in PhoneNumbers.PhoneNumbers)
                {
                    phone.IsPrimary = false;
                }

                // Set the selected phone number as primary
                phoneNumber.IsPrimary = true;
                LoggingService.LogInfo($"Phone number set as primary: {phoneNumber.PhoneNumber}", "ContactInformationViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting phone number as primary: {ex.Message}", "ContactInformationViewModel");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Opens the phone numbers management dialog.
        /// </summary>
        private void ManagePhoneNumbers()
        {
            try
            {
                // This will be implemented to open the phone numbers management dialog
                // For now, just log the action
                LoggingService.LogInfo("Phone numbers management dialog requested", "ContactInformationViewModel");
                
                // TODO: Implement phone numbers management dialog opening
                // This should integrate with the existing phone numbers dialog
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening phone numbers management dialog: {ex.Message}", "ContactInformationViewModel");
                ErrorManager.HandleErrorToast(ex, 
                    "حدث خطأ أثناء فتح نافذة إدارة أرقام الهاتف", 
                    "خطأ في إدارة أرقام الهاتف", 
                    LogLevel.Error, 
                    "ContactInformationViewModel");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ViewModel resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                PhoneNumbers?.PhoneNumbers?.Clear();
                LoggingService.LogDebug("ContactInformationViewModel disposed", "ContactInformationViewModel");
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}