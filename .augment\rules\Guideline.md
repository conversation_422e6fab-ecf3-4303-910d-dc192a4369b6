---
type: "always_apply"
---

---
type: "always_apply"---
Before adding new code or creating new files, you must first analyze the existing codebase to identify and remove any obsolete, duplicate, or conflicting code that could cause issues. Follow this process:

1. **Analyze existing implementations**: Search for similar functionality, duplicate classes, or conflicting implementations that might interfere with your new code
2. **Identify obsolete code**: Look for old files, unused methods, or deprecated implementations that should be removed
3. **Check for naming conflicts**: Ensure new classes, methods, or variables don't conflict with existing ones
4. **Remove before adding**: Delete or refactor conflicting/obsolete code BEFORE implementing new functionality
5. **Verify integration**: Ensure your new code integrates cleanly with the existing architecture without breaking existing functionality
6. **Aviod generic  Naming** : don't use generic prefixes like "Optimized", "Performance", "Enhanced", and similar vague terms with more descriptive, domain-specific names that clearly indicate their purpose and functionality.

The goal is to improve the application, not make it worse by adding code that conflicts with or duplicates existing functionality. Always clean up the codebase as part of your implementation process.
