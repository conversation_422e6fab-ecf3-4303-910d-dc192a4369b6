# UFU2 Business Process Flows Documentation

## Overview

This comprehensive guide documents UFU2's core business processes, covering the complete workflows for client registration, activity management, document verification, and payment processing. All processes follow Algerian business registration regulations and integrate seamlessly with UFU2's MVVM architecture, ServiceLocator pattern, and Arabic RTL support.

## Table of Contents

1. [Client Registration Complete Workflow](#client-registration-complete-workflow)
2. [Activity Management Processes](#activity-management-processes)
3. [Document Management and File Check Procedures](#document-management-and-file-check-procedures)
4. [Payment Processing Workflows](#payment-processing-workflows)
5. [Integration Points](#integration-points)
6. [Arabic Business Rules](#arabic-business-rules)

---

## Client Registration Complete Workflow

### Overview

The client registration process is the foundation of UFU2's business operations, creating a complete client record with unique identification, activities, and associated documentation requirements.

### UID Generation Pattern

UFU2 uses a specific UID generation pattern for clients: `{FirstLetter}{SequentialNumber:D2}`

**Examples:**
- Client "<PERSON>" → UID: "A01"
- Client "Bachir" → UID: "B01" 
- Client "<PERSON><PERSON>" → UID: "Z01"
- Client "<PERSON>" (second client starting with A) → UID: "A02"

### Complete Client Registration Flow

```mermaid
sequenceDiagram
    participant UI as NewClientView
    participant VM as NewClientViewModel
    participant CVS as ClientValidationService
    participant UGS as UIDGenerationService
    participant CDS as ClientDatabaseService
    participant FCBRS as FileCheckBusinessRuleService
    participant DS as DatabaseService
    participant EM as ErrorManager

    UI->>VM: SaveClient Command
    VM->>CVS: ValidateClientCreation(clientData)
    
    alt Validation Successful
        CVS->>VM: ValidationResult.IsValid = true
        VM->>UGS: GenerateClientUIDAsync(nameFr)
        UGS->>DS: BeginTransaction()
        UGS->>DS: GetNextSequenceAsync("Client", firstLetter)
        DS->>UGS: Return sequence number
        UGS->>UGS: Generate UID: {firstLetter}{sequence:D2}
        UGS->>DS: CommitTransaction()
        UGS->>VM: Return Client UID
        
        VM->>CDS: CreateClientAsync(clientData, operationId)
        CDS->>DS: BeginTransaction()
        CDS->>DS: INSERT INTO Clients
        
        loop For each activity
            CDS->>UGS: GenerateActivityUIDAsync(clientUID)
            UGS->>CDS: Return Activity UID: {clientUID}_Act{sequence}
            CDS->>DS: INSERT INTO Activities
            CDS->>FCBRS: EnsureRequiredFileCheckStatesAsync()
            FCBRS->>DS: INSERT INTO FileCheckStates
        end
        
        loop For each phone number
            CDS->>DS: INSERT INTO PhoneNumbers
        end
        
        CDS->>DS: CommitTransaction()
        CDS->>VM: Return Client UID
        VM->>EM: ShowUserSuccessToast("تم حفظ بيانات العميل بنجاح")
        VM->>UI: Close dialog with success
        
    else Validation Failed
        CVS->>VM: ValidationResult with errors
        VM->>EM: ShowUserWarningToast(arabicErrors)
        VM->>UI: Display validation errors
    end
```

### Implementation Example

<augment_code_snippet path="ViewModels/NewClientViewModel.cs" mode="EXCERPT">
````csharp
private async Task SaveClientDataAsync(string operationId)
{
    try
    {
        // Create client data from UI models
        var clientData = await CreateClientDataFromUIAsync();

        // Validate client data using ClientValidationService
        var validationResult = _clientValidationService.ValidateClientCreation(clientData);
        if (!validationResult.IsValid)
        {
            var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
            ErrorManager.ShowUserWarningToast(errorMessage, "بيانات غير صحيحة", "NewClientViewModel");
            return;
        }

        // Save client to database with operation tracking
        string clientUID = await _clientDatabaseService.CreateClientAsync(clientData, operationId);

        // Show success notification with client UID
        ErrorManager.ShowUserSuccessToast($"تم حفظ بيانات العميل بنجاح\nمعرف العميل: {clientUID}", "تم الحفظ");
    }
    catch (Exception ex)
    {
        ErrorManager.HandleErrorToast(ex, "حدث خطأ أثناء حفظ البيانات في قاعدة البيانات", "خطأ في قاعدة البيانات");
        throw;
    }
}
````
</augment_code_snippet>

### UID Generation Service Integration

<augment_code_snippet path="Services/UIDGenerationService.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Generates a unique Client UID following the pattern {FirstLetter}{SequentialNumber:D2}.
/// Example: "A01", "B02", "Z99", "A100"
/// </summary>
public async Task<string> GenerateClientUIDAsync(string nameFr, string? operationId = null)
{
    if (string.IsNullOrWhiteSpace(nameFr))
    {
        var errorMessage = "اسم العميل مطلوب لتوليد المعرف الفريد";
        throw new ArgumentException(errorMessage, nameof(nameFr));
    }

    // Extract first letter and convert to uppercase
    string firstLetter = ExtractFirstLetter(nameFr);
    
    using var connection = _databaseService.CreateConnection();
    await connection.OpenAsync();
    using var transaction = connection.BeginTransaction();
    
    try
    {
        // Get next sequence number for this prefix
        int sequenceNumber = await GetNextSequenceAsync(connection, transaction, "Client", firstLetter);
        
        // Generate UID with zero-padded sequence (minimum 2 digits)
        string clientUID = $"{firstLetter}{sequenceNumber:D2}";
        
        // Verify uniqueness (additional safety check)
        await EnsureClientUIDUniquenessAsync(connection, transaction, clientUID);
        
        transaction.Commit();
        return clientUID;
    }
    catch (Exception ex)
    {
        transaction.Rollback();
        throw;
    }
}
````
</augment_code_snippet>

---

## Activity Management Processes

### Activity Types and Requirements

UFU2 supports four distinct activity types, each with specific requirements and validation rules:

#### 1. Main Commercial (النشاط التجاري الرئيسي)
- **ActivityCode**: Required (6-digit numeric code)
- **ActivityDescription**: Optional
- **Required File Checks**: CAS, NIF, NIS, RC, DEX
- **Business Rules**: Primary commercial activity for the client

#### 2. Secondary Commercial (النشاط التجاري الثانوي)
- **ActivityCode**: Required (6-digit numeric code)
- **ActivityDescription**: Optional
- **Required File Checks**: CAS, NIF, NIS, RC, DEX
- **Business Rules**: Additional commercial activities

#### 3. Craft (النشاط الحرفي)
- **ActivityCode**: Not used
- **ActivityDescription**: Required (free text description)
- **Required File Checks**: CAS, NIF, NIS, ART, DEX
- **Business Rules**: Artisanal and craft-based activities

#### 4. Professional (النشاط المهني)
- **ActivityCode**: Not used
- **ActivityDescription**: Required (free text description)
- **Required File Checks**: CAS, NIF, NIS, AGR, DEX
- **Business Rules**: Professional services and liberal professions

### Activity Creation Workflow

```mermaid
sequenceDiagram
    participant VM as ActivityViewModel
    participant CVS as ClientValidationService
    participant CDS as ClientDatabaseService
    participant UGS as UIDGenerationService
    participant FCBRS as FileCheckBusinessRuleService
    participant DS as DatabaseService

    VM->>CVS: ValidateActivity(activityData)
    
    alt Activity Valid
        CVS->>VM: ValidationResult.IsValid = true
        VM->>CDS: CreateActivityAsync(clientUID, activityData)
        CDS->>UGS: GenerateActivityUIDAsync(clientUID)
        UGS->>CDS: Return Activity UID: {clientUID}_Act{sequence}
        
        CDS->>DS: INSERT INTO Activities
        CDS->>FCBRS: EnsureRequiredFileCheckStatesAsync(activityUID, activityType)
        
        loop For each required file check type
            FCBRS->>DS: INSERT INTO FileCheckStates
        end
        
        alt Commercial Activity
            CDS->>DS: INSERT INTO CommercialActivityCodes
        end
        
        CDS->>VM: Return Activity UID
        VM->>VM: ShowSuccessMessage("تم إنشاء النشاط بنجاح")
        
    else Activity Invalid
        CVS->>VM: ValidationResult with errors
        VM->>VM: ShowValidationErrors(arabicErrors)
    end
```

### Activity Type-Specific Validation

<augment_code_snippet path="Services/ClientValidationService.cs" mode="EXCERPT">
````csharp
private ValidationResult ValidateActivityTypeSpecificData(ActivityCreationData activityData)
{
    var result = new ValidationResult();
    
    switch (activityData.ActivityType)
    {
        case "MainCommercial":
        case "SecondaryCommercial":
            // Commercial activities require ActivityCode
            if (activityData.ActivityCodes == null || !activityData.ActivityCodes.Any())
            {
                result.AddError("ActivityCodes", "كود النشاط مطلوب للأنشطة التجارية");
            }
            break;
            
        case "Craft":
        case "Professional":
            // Craft and Professional activities require ActivityDescription
            if (string.IsNullOrWhiteSpace(activityData.ActivityDescription))
            {
                result.AddError("ActivityDescription", "وصف النشاط مطلوب للأنشطة الحرفية والمهنية");
            }
            break;
    }
    
    return result;
}
````
</augment_code_snippet>

---

## Document Management and File Check Procedures

### File Check Types by Activity

UFU2 implements Algerian business registration requirements through a comprehensive file check system:

#### Common File Check Types (All Activities)
- **CAS** (Certificat d'Affiliation Sociale) - شهادة الانتماء الاجتماعي
- **NIF** (Numéro d'Identification Fiscale) - رقم التعريف الجبائي
- **NIS** (Numéro d'Identification Statistique) - رقم التعريف الإحصائي
- **DEX** (Déclaration d'Existence) - تصريح الوجود

#### Activity-Specific File Check Types
- **RC** (Registre de Commerce) - السجل التجاري (Commercial only)
- **ART** (Registre des Métiers) - سجل الحرف (Craft only)
- **AGR** (Agrément Professionnel) - الاعتماد المهني (Professional only)

### File Check Business Rules Implementation

<augment_code_snippet path="Services/FileCheckBusinessRuleService.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Validates file check business rules based on activity types.
/// Enforces Algerian business registration requirements.
/// </summary>
public ValidationResult ValidateFileCheckBusinessRules(
    string activityType, 
    Dictionary<string, bool> fileCheckStates, 
    bool enforceCompletion = false)
{
    var result = new ValidationResult();
    
    if (string.IsNullOrWhiteSpace(activityType))
    {
        result.AddError("ActivityType", "نوع النشاط مطلوب للتحقق من حالة فحص الملفات");
        return result;
    }

    // Validate file check types
    var typeValidation = ValidateFileCheckTypes(activityType, fileCheckStates.Keys);
    result.Merge(typeValidation);

    // Validate required types are present
    var requiredValidation = ValidateRequiredFileCheckTypes(activityType, fileCheckStates.Keys, enforceCompletion);
    result.Merge(requiredValidation);

    // Validate completion if enforced
    if (enforceCompletion)
    {
        var requiredTypes = FileCheckTypeRules.GetRequiredFileCheckTypes(activityType);
        var incompleteTypes = requiredTypes.Where(required => 
            fileCheckStates.ContainsKey(required) && !fileCheckStates[required]).ToList();

        if (incompleteTypes.Any())
        {
            var activityDisplayName = FileCheckTypeRules.GetActivityTypeDisplayName(activityType);
            var incompleteTypesDisplay = string.Join(", ", incompleteTypes.Select(FileCheckTypeRules.GetFileCheckTypeDisplayName));
            result.AddError("IncompleteFileChecks", 
                $"فحص الملفات التالية غير مكتمل لنوع النشاط '{activityDisplayName}': {incompleteTypesDisplay}");
        }
    }

    return result;
}
````
</augment_code_snippet>

### File Check Type Rules

<augment_code_snippet path="Models/FileCheckTypeRules.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Gets the valid file check types for the specified activity type.
/// </summary>
public static List<string> GetValidFileCheckTypes(string activityType)
{
    var commonTypes = CommonFileCheckTypes.ToList(); // CAS, NIF, NIS, DEX

    return activityType switch
    {
        MainCommercial or SecondaryCommercial => commonTypes.Concat(new[] { "RC" }).ToList(),
        Craft => commonTypes.Concat(new[] { "ART" }).ToList(),
        Professional => commonTypes.Concat(new[] { "AGR" }).ToList(),
        _ => commonTypes
    };
}

/// <summary>
/// Gets the required file check types for the specified activity type.
/// These are the file check types that must be present for the activity type.
/// </summary>
public static List<string> GetRequiredFileCheckTypes(string activityType)
{
    // For now, all valid types are considered required
    return GetValidFileCheckTypes(activityType);
}
````
</augment_code_snippet>

---

## Payment Processing Workflows

### G12Check and BisCheck Overview

UFU2 manages two types of payment tracking systems:

#### G12Check (نظام G12)
- **Purpose**: Government tax payment tracking
- **Scope**: Annual payment verification
- **Integration**: Linked to activity registration
- **Validation**: Year range 2000-2100

#### BisCheck (نظام BIS)
- **Purpose**: Business license fee tracking  
- **Scope**: Annual license payment verification
- **Integration**: Linked to activity registration
- **Validation**: Year range 2000-2100

### Payment Year Calculation

Payment years are automatically calculated based on activity start dates:

<augment_code_snippet path="Common/Utilities/PaymentYearRangeCalculator.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Auto-populates payment years based on activity start date.
/// Returns all years from start date to current year for both G12 and BIS.
/// </summary>
public static (List<int> G12Years, List<int> BISYears) AutoPopulatePaymentYears(string activityStartDate)
{
    if (!IsValidActivityStartDate(activityStartDate))
    {
        return (new List<int>(), new List<int>());
    }

    var allYears = GenerateYearList(activityStartDate);
    
    // Return all years for both G12 and BIS (following new default behavior)
    return (new List<int>(allYears), new List<int>(allYears));
}

/// <summary>
/// Generates a list of years from the activity start date to current year.
/// </summary>
public static List<int> GenerateYearList(string activityStartDate)
{
    var yearRange = CalculateYearRange(activityStartDate);
    var years = new List<int>();

    for (int year = yearRange.StartYear; year <= yearRange.EndYear; year++)
    {
        years.Add(year);
    }

    return years;
}
````
</augment_code_snippet>

### Payment Processing Workflow

```mermaid
sequenceDiagram
    participant VM as PaymentViewModel
    participant PYRC as PaymentYearRangeCalculator
    participant CDS as ClientDatabaseService
    participant DS as DatabaseService
    participant EM as ErrorManager

    VM->>PYRC: AutoPopulatePaymentYears(activityStartDate)
    PYRC->>VM: Return (G12Years, BISYears)
    
    VM->>VM: Display payment year selection UI
    
    alt User Confirms Payment Years
        VM->>CDS: UpdateG12CheckYearsAsync(activityUID, selectedG12Years)
        CDS->>DS: BeginTransaction()
        CDS->>DS: DELETE FROM G12Check WHERE ActivityUid = @activityUID
        
        loop For each selected G12 year
            CDS->>DS: INSERT INTO G12Check (ActivityUid, Year)
        end
        
        CDS->>CDS: UpdateBisCheckYearsAsync(activityUID, selectedBISYears)
        CDS->>DS: DELETE FROM BisCheck WHERE ActivityUid = @activityUID
        
        loop For each selected BIS year
            CDS->>DS: INSERT INTO BisCheck (ActivityUid, Year)
        end
        
        CDS->>DS: CommitTransaction()
        CDS->>VM: Return success
        VM->>EM: ShowUserSuccessToast("تم تحديث سنوات الدفع بنجاح")
        
    else User Cancels
        VM->>VM: Restore previous payment years
    end
```

### Payment Year Management Implementation

<augment_code_snippet path="Services/ClientDatabaseService.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Internal method to update payment years for either G12 or BIS checks.
/// </summary>
private async Task<bool> UpdatePaymentYearsAsync(string activityUID, List<int> years, string tableName, string checkType)
{
    try
    {
        using var connection = _databaseService.CreateConnection();
        await connection.OpenAsync();
        using var transaction = connection.BeginTransaction();
        
        try
        {
            // Delete existing years
            var deleteSql = $"DELETE FROM {tableName} WHERE ActivityUid = @ActivityUid";
            await connection.ExecuteAsync(deleteSql, new { ActivityUid = activityUID }, transaction);

            // Insert new years
            if (years.Count > 0)
            {
                var insertSql = $@"
                    INSERT INTO {tableName} (Uid, ActivityUid, Year)
                    VALUES (@Uid, @ActivityUid, @Year)";

                foreach (var year in years)
                {
                    var uid = await _uidGenerationService.GenerateUIDAsync(checkType, activityUID);
                    await connection.ExecuteAsync(insertSql, new
                    {
                        Uid = uid,
                        ActivityUid = activityUID,
                        Year = year
                    }, transaction);
                }
            }

            transaction.Commit();
            LoggingService.LogInfo($"{checkType} check years updated successfully for activity: {activityUID}", "ClientDatabaseService");
            return true;
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            LoggingService.LogError($"Error updating {checkType} check years: {ex.Message}", "ClientDatabaseService");
            throw;
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Database connection error for {checkType} update: {ex.Message}", "ClientDatabaseService");
        return false;
    }
}
````
</augment_code_snippet>

---

## Integration Points

### Service Dependencies

UFU2's business processes rely on a comprehensive service architecture:

```mermaid
graph TB
    subgraph "Core Services"
        SL[ServiceLocator] --> CDS[ClientDatabaseService]
        SL --> UGS[UIDGenerationService]
        SL --> CVS[ClientValidationService]
        SL --> FCBRS[FileCheckBusinessRuleService]
        SL --> DS[DatabaseService]
        SL --> EM[ErrorManager]
        SL --> LS[LoggingService]
    end
    
    subgraph "Business Process Integration"
        CDS --> UGS
        CDS --> FCBRS
        CDS --> DS
        CVS --> FCBRS
        UGS --> DS
        FCBRS --> DS
    end
    
    subgraph "ViewModels"
        NCVM[NewClientViewModel] --> CDS
        NCVM --> CVS
        NCVM --> EM
        NCVM --> LS
    end
    
    subgraph "Error Handling"
        EM --> LS
        CDS --> EM
        UGS --> EM
        CVS --> EM
        FCBRS --> EM
    end
```

### ServiceLocator Integration Pattern

All business processes follow UFU2's established ServiceLocator pattern:

```csharp
public class BusinessProcessViewModel : BaseViewModel
{
    private readonly ClientDatabaseService _clientDatabaseService;
    private readonly ClientValidationService _clientValidationService;
    private readonly FileCheckBusinessRuleService _fileCheckBusinessRuleService;
    
    public BusinessProcessViewModel()
    {
        // Access services through ServiceLocator
        _clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
        _clientValidationService = ServiceLocator.GetService<ClientValidationService>();
        _fileCheckBusinessRuleService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
        
        // Initialize commands
        SaveClientCommand = new RelayCommand(async () => await SaveClientAsync());
        ValidateFileChecksCommand = new RelayCommand<string>(async (activityType) => await ValidateFileChecksAsync(activityType));
    }
    
    private async Task SaveClientAsync()
    {
        try
        {
            // Validate client data
            var validationResult = _clientValidationService.ValidateClientCreation(ClientData);
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("\n", validationResult.Errors.SelectMany(e => e.Value));
                ErrorManager.ShowUserWarningToast(errorMessage, "بيانات غير صحيحة", "BusinessProcessViewModel");
                return;
            }
            
            // Save client with complete workflow
            var clientUID = await _clientDatabaseService.CreateClientAsync(ClientData);
            
            // Show success message
            ErrorManager.ShowUserSuccessToast($"تم حفظ بيانات العميل بنجاح\nمعرف العميل: {clientUID}", "تم الحفظ");
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل");
        }
    }
}
```

---

## Arabic Business Rules

### Client Registration Rules (قواعد تسجيل العملاء)

#### 1. معلومات العميل الأساسية (Basic Client Information)
- **الاسم بالفرنسية (NameFr)**: مطلوب - يُستخدم لتوليد المعرف الفريد
- **الاسم بالعربية (NameAr)**: اختياري - يدعم النص من اليمين إلى اليسار
- **تاريخ الميلاد (BirthDate)**: اختياري - بصيغة DD/MM/YYYY
- **مكان الميلاد (BirthPlace)**: اختياري
- **الجنس (Gender)**: 0 = ذكر، 1 = أنثى
- **العنوان (Address)**: اختياري
- **رقم الهوية الوطنية (NationalId)**: اختياري - يجب أن يكون 8 أرقام على الأقل

#### 2. قواعد توليد المعرف الفريد (UID Generation Rules)
- **النمط**: {الحرف الأول}{الرقم التسلسلي:D2}
- **مثال**: "أحمد" → "A01"، "بشير" → "B01"
- **التسلسل**: يبدأ من 01 لكل حرف ويزيد تلقائياً
- **الفرادة**: كل معرف فريد في النظام

### Activity Management Rules (قواعد إدارة الأنشطة)

#### 1. أنواع الأنشطة (Activity Types)
- **النشاط التجاري الرئيسي (MainCommercial)**: يتطلب كود النشاط (6 أرقام)
- **النشاط التجاري الثانوي (SecondaryCommercial)**: يتطلب كود النشاط (6 أرقام)
- **النشاط الحرفي (Craft)**: يتطلب وصف النشاط (نص حر)
- **النشاط المهني (Professional)**: يتطلب وصف النشاط (نص حر)

#### 2. متطلبات فحص الملفات (File Check Requirements)
- **الأنشطة التجارية**: CAS، NIF، NIS، RC، DEX
- **الأنشطة الحرفية**: CAS، NIF، NIS، ART، DEX
- **الأنشطة المهنية**: CAS، NIF، NIS، AGR، DEX

### Document Verification Rules (قواعد التحقق من الوثائق)

#### 1. أنواع فحص الملفات (File Check Types)
- **CAS**: شهادة الانتماء الاجتماعي - مطلوبة لجميع الأنشطة
- **NIF**: رقم التعريف الجبائي - مطلوب لجميع الأنشطة
- **NIS**: رقم التعريف الإحصائي - مطلوب لجميع الأنشطة
- **DEX**: تصريح الوجود - مطلوب لجميع الأنشطة
- **RC**: السجل التجاري - مطلوب للأنشطة التجارية فقط
- **ART**: سجل الحرف - مطلوب للأنشطة الحرفية فقط
- **AGR**: الاعتماد المهني - مطلوب للأنشطة المهنية فقط

#### 2. قواعد التحقق (Validation Rules)
- **الاكتمال**: جميع الملفات المطلوبة يجب أن تكون مكتملة
- **التطابق**: نوع فحص الملف يجب أن يتطابق مع نوع النشاط
- **التاريخ**: تاريخ الفحص يُسجل عند الاكتمال

### Payment Processing Rules (قواعد معالجة المدفوعات)

#### 1. نظام G12 (G12 System)
- **الغرض**: تتبع دفع الضرائب الحكومية
- **النطاق**: التحقق من الدفع السنوي
- **السنوات**: من سنة بداية النشاط إلى السنة الحالية

#### 2. نظام BIS (BIS System)
- **الغرض**: تتبع رسوم رخصة العمل
- **النطاق**: التحقق من دفع الرخصة السنوية
- **السنوات**: من سنة بداية النشاط إلى السنة الحالية

#### 3. حساب السنوات (Year Calculation)
- **البداية**: تاريخ بداية النشاط (DD/MM/YYYY)
- **النهاية**: السنة الحالية
- **التلقائي**: يتم ملء جميع السنوات تلقائياً
- **التخصيص**: يمكن تعديل السنوات حسب الحاجة

---

## Error Handling and Validation Patterns

### Comprehensive Error Handling

All business processes implement consistent error handling with Arabic user messages:

```csharp
public class BusinessProcessService
{
    private readonly ErrorManager _errorManager;
    private readonly LoggingService _loggingService;

    public async Task<bool> ExecuteBusinessProcessAsync<T>(
        Func<Task<T>> businessOperation,
        string operationName,
        string arabicErrorMessage)
    {
        return await _errorManager.ExecuteWithErrorHandlingAsync(async () =>
        {
            _loggingService.LogInfo($"Starting {operationName}", "BusinessProcessService");

            var result = await businessOperation();

            _loggingService.LogInfo($"Completed {operationName} successfully", "BusinessProcessService");
            return result;

        }, "BusinessProcessService", operationName, arabicErrorMessage, LogLevel.Error);
    }
}
```

### Validation Message Patterns

UFU2 uses standardized Arabic validation messages:

```csharp
public static class ValidationMessages
{
    // Client validation messages
    public const string ClientNameRequired = "اسم العميل مطلوب";
    public const string ClientNameTooLong = "اسم العميل يجب أن يكون أقل من 100 حرف";
    public const string InvalidNationalId = "رقم الهوية الوطنية غير صحيح";

    // Activity validation messages
    public const string ActivityTypeRequired = "نوع النشاط مطلوب";
    public const string ActivityCodeRequired = "كود النشاط مطلوب للأنشطة التجارية";
    public const string ActivityDescriptionRequired = "وصف النشاط مطلوب للأنشطة الحرفية والمهنية";
    public const string InvalidActivityCode = "كود النشاط يجب أن يكون 6 أرقام";

    // File check validation messages
    public const string FileCheckTypeInvalid = "نوع فحص الملف غير صحيح لهذا النوع من النشاط";
    public const string RequiredFileCheckMissing = "فحص الملف المطلوب مفقود";
    public const string FileCheckIncomplete = "فحص الملف غير مكتمل";

    // Payment validation messages
    public const string InvalidPaymentYear = "سنة الدفع غير صحيحة";
    public const string PaymentYearOutOfRange = "سنة الدفع خارج النطاق المسموح";
}
```

---

## Performance Optimization Patterns

### Database Transaction Management

All business processes use optimized transaction patterns:

```csharp
public async Task<string> CreateCompleteClientAsync(ClientCreationData clientData)
{
    using var connection = _databaseService.CreateConnection();
    await connection.OpenAsync();

    using var transaction = connection.BeginTransaction();
    try
    {
        // Step 1: Generate Client UID
        var clientUID = await _uidGenerationService.GenerateClientUIDAsync(clientData.NameFr);

        // Step 2: Insert client record
        await InsertClientAsync(connection, transaction, clientUID, clientData);

        // Step 3: Process activities in batch
        var activityTasks = clientData.Activities.Select(async activity =>
        {
            var activityUID = await _uidGenerationService.GenerateActivityUIDAsync(clientUID);
            await InsertActivityAsync(connection, transaction, activityUID, activity);
            await _fileCheckBusinessRuleService.EnsureRequiredFileCheckStatesAsync(activityUID, activity.ActivityType);
        });

        await Task.WhenAll(activityTasks);

        // Step 4: Commit all changes
        transaction.Commit();

        LoggingService.LogInfo($"Client created successfully: {clientUID}", "ClientDatabaseService");
        return clientUID;
    }
    catch (Exception ex)
    {
        transaction.Rollback();
        LoggingService.LogError($"Transaction rolled back: {ex.Message}", "ClientDatabaseService");
        throw;
    }
}
```

### Async/Await Best Practices

UFU2 business processes follow async best practices:

```csharp
public class OptimizedBusinessProcessViewModel : BaseViewModel
{
    public async Task SaveClientWithProgressAsync()
    {
        try
        {
            IsLoading = true;
            ProgressMessage = "جاري التحقق من البيانات...";

            // Step 1: Validation (fast operation)
            var validationResult = await _clientValidationService.ValidateClientCreationAsync(ClientData);
            if (!validationResult.IsValid)
            {
                ShowValidationErrors(validationResult.Errors);
                return;
            }

            ProgressMessage = "جاري حفظ بيانات العميل...";

            // Step 2: Database operations (with progress tracking)
            var clientUID = await _clientDatabaseService.CreateClientAsync(ClientData,
                progress => ProgressMessage = progress);

            ProgressMessage = "جاري إنشاء الأنشطة...";

            // Step 3: Activity creation (parallel processing)
            await ProcessActivitiesAsync(clientUID, ClientData.Activities);

            // Step 4: Success notification
            ErrorManager.ShowUserSuccessToast($"تم حفظ بيانات العميل بنجاح\nمعرف العميل: {clientUID}");
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل");
        }
        finally
        {
            IsLoading = false;
            ProgressMessage = string.Empty;
        }
    }
}
```

---

## Testing and Quality Assurance

### Business Rule Validation Testing

UFU2 business processes include comprehensive validation testing patterns:

```csharp
// Example: File Check Business Rule Validation
public void ValidateFileCheckBusinessRules()
{
    var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();

    // Test Commercial Activity File Checks
    var commercialFileChecks = new Dictionary<string, bool>
    {
        { "CAS", true },
        { "NIF", true },
        { "NIS", true },
        { "RC", true },
        { "DEX", true }
    };

    var result = fileCheckService.ValidateFileCheckBusinessRules(
        "MainCommercial", commercialFileChecks, enforceCompletion: true);

    if (!result.IsValid)
    {
        LoggingService.LogError($"Commercial file check validation failed: {string.Join(", ", result.Errors)}",
            "BusinessRuleValidation");
    }
}
```

### Data Integrity Verification

```csharp
public async Task<bool> VerifyDataIntegrityAsync(string clientUID)
{
    try
    {
        // Verify client exists
        var client = await _clientDatabaseService.GetClientByUIDAsync(clientUID);
        if (client == null)
        {
            LoggingService.LogError($"Client not found: {clientUID}", "DataIntegrityCheck");
            return false;
        }

        // Verify all activities have required file checks
        var activities = await _clientDatabaseService.GetActivitiesByClientUIDAsync(clientUID);
        foreach (var activity in activities)
        {
            var requiredFileChecks = FileCheckTypeRules.GetRequiredFileCheckTypes(activity.ActivityType);
            var existingFileChecks = await _clientDatabaseService.GetFileCheckStatesByActivityUIDAsync(activity.UID);

            var missingFileChecks = requiredFileChecks.Where(required =>
                !existingFileChecks.Any(existing => existing.FileCheckType == required)).ToList();

            if (missingFileChecks.Any())
            {
                LoggingService.LogWarning($"Missing file checks for activity {activity.UID}: {string.Join(", ", missingFileChecks)}",
                    "DataIntegrityCheck");

                // Auto-fix: Create missing file check states
                await _fileCheckBusinessRuleService.EnsureRequiredFileCheckStatesAsync(activity.UID, activity.ActivityType);
            }
        }

        LoggingService.LogInfo($"Data integrity verified for client: {clientUID}", "DataIntegrityCheck");
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Data integrity check failed for client {clientUID}: {ex.Message}", "DataIntegrityCheck");
        return false;
    }
}
```

---

## Best Practices Summary

### Do's ✅

#### Client Registration
- **Always validate** client data before database operations
- **Use transactions** for multi-table operations
- **Generate UIDs** through UIDGenerationService
- **Handle Arabic names** properly with RTL support
- **Provide Arabic error messages** for all user-facing errors

#### Activity Management
- **Validate activity types** against business rules
- **Ensure file check requirements** are met
- **Use appropriate fields** (ActivityCode vs ActivityDescription)
- **Create required file check states** automatically
- **Handle activity type changes** properly

#### Document Management
- **Enforce file check business rules** at application level
- **Validate file check types** against activity types
- **Track completion status** for all required documents
- **Provide Arabic descriptions** for file check types
- **Maintain audit trail** for document verification

#### Payment Processing
- **Calculate payment years** based on activity start dates
- **Validate year ranges** (2000-2100)
- **Handle both G12 and BIS** payment systems
- **Provide user-friendly** year selection interfaces
- **Maintain payment history** for audit purposes

### Don'ts ❌

#### General
- **Don't bypass validation** for any business process
- **Don't use English error messages** for users
- **Don't ignore transaction rollback** on errors
- **Don't create orphaned records** in database
- **Don't hardcode business rules** in UI code

#### Client Registration
- **Don't allow duplicate UIDs** in the system
- **Don't skip UID generation** for any client
- **Don't ignore validation errors** during save
- **Don't create clients** without required information
- **Don't forget to log** important operations

#### Activity Management
- **Don't mix ActivityCode and ActivityDescription** usage
- **Don't allow invalid file check types** for activity types
- **Don't skip file check state creation** for new activities
- **Don't ignore activity type constraints** in validation
- **Don't allow incomplete** required file checks

#### Document Management
- **Don't allow invalid file check combinations** per activity type
- **Don't skip business rule validation** for file checks
- **Don't ignore completion requirements** when enforced
- **Don't allow manual override** of business rules
- **Don't forget to update** file check states on activity changes

#### Payment Processing
- **Don't allow invalid year ranges** in payment tracking
- **Don't ignore activity start dates** in year calculation
- **Don't mix G12 and BIS** payment year logic
- **Don't allow duplicate years** per activity
- **Don't skip validation** of payment year data

This comprehensive documentation provides developers with complete understanding of UFU2's business processes while maintaining consistency with established architectural patterns and Arabic business requirements.
