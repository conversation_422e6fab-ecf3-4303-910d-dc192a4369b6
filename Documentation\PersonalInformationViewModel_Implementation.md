# PersonalInformationViewModel Implementation Guide

## Overview

The `PersonalInformationViewModel` is a specialized MVVM component extracted from the larger `NewClientViewModel` to handle client personal information management in the UFU2 application. This component follows the single responsibility principle by focusing exclusively on managing basic client details including names, birth information, gender, address, national ID, and profile image. It provides comprehensive data validation, change detection for editing scenarios, and seamless integration with the UFU2 architecture.

## What the Code Does

The PersonalInformationViewModel manages the following core functionalities:

### Core Personal Data Management
- **Bilingual Name Support**: Handles both French (Latin) and Arabic names with proper validation
- **Birth Information**: Manages birth date and birth place with flexible date format support
- **Identity Management**: Tracks gender, address, and national ID information
- **Profile Image Handling**: Manages client profile images with format preservation

### Data Validation and Integrity
- **Required Field Validation**: Ensures NameFr (French name) is provided as it's mandatory
- **Change Detection**: Compares current data against original client data for editing scenarios
- **Data Normalization**: Handles null values and whitespace trimming for consistent data storage

### Integration Features
- **Client Data Loading**: Loads existing client data from DuplicateClientData objects
- **Memory Management**: Proper resource disposal and image reference cleanup
- **Error Handling**: Comprehensive error handling with Arabic localized messages

## How It's Used

### Basic Usage Pattern

```csharp
// Initialize the ViewModel
var personalInfoVM = new PersonalInformationViewModel();

// Set basic personal information
personalInfoVM.NameFr = "Ahmed Ben Ali";
personalInfoVM.NameAr = "أحمد بن علي";
personalInfoVM.BirthDate = "15/03/1985";
personalInfoVM.BirthPlace = "الجزائر";
personalInfoVM.Gender = 0; // Male
personalInfoVM.Address = "شارع الاستقلال، الجزائر العاصمة";
personalInfoVM.NationalId = "1234567890123456";

// Validate the data
if (personalInfoVM.IsValid())
{
    LoggingService.LogInfo("Personal information is valid", "PersonalInfo");
}
else
{
    LoggingService.LogWarning("Personal information validation failed", "PersonalInfo");
}
```

### Loading Existing Client Data

```csharp
// Load existing client data for editing
public void LoadExistingClientData(PersonalInformationViewModel personalInfoVM, DuplicateClientData clientData)
{
    try
    {
        // Load data from existing client
        personalInfoVM.LoadFromClientData(clientData);
        
        // Verify data was loaded correctly
        Debug.Assert(personalInfoVM.NameFr == clientData.NameFr);
        Debug.Assert(personalInfoVM.NameAr == clientData.NameAr);
        Debug.Assert(personalInfoVM.Gender == clientData.Gender);
        
        LoggingService.LogInfo($"Loaded personal information for client: {clientData.ClientUid}", "PersonalInfo");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error loading client data: {ex.Message}", "PersonalInfo");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء تحميل بيانات العميل",
            "خطأ في التحميل",
            LogLevel.Error,
            "PersonalInfo");
    }
}
```

### Change Detection for Editing Scenarios

```csharp
// Detect changes for existing client editing
public bool CheckForPersonalInfoChanges(PersonalInformationViewModel personalInfoVM, DuplicateClientData originalData)
{
    try
    {
        // Load original data
        personalInfoVM.LoadFromClientData(originalData);
        
        // Make some changes
        personalInfoVM.Address = "عنوان جديد - حي النصر";
        personalInfoVM.NameAr = "أحمد بن علي المحدث";
        
        // Check if changes were made
        bool hasChanges = personalInfoVM.HasChanges(originalData);
        
        if (hasChanges)
        {
            LoggingService.LogInfo("Personal information changes detected", "PersonalInfo");
            
            // Show confirmation dialog for changes
            var confirmationResult = ShowChangeConfirmationDialog();
            return confirmationResult;
        }
        else
        {
            LoggingService.LogInfo("No personal information changes detected", "PersonalInfo");
            return false;
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error checking for changes: {ex.Message}", "PersonalInfo");
        return false;
    }
}

private bool ShowChangeConfirmationDialog()
{
    // Implementation would show a confirmation dialog
    // For example purposes, return true
    return true;
}
```

### Profile Image Management

```csharp
// Handle profile image operations
public async Task<bool> ManageProfileImageAsync(PersonalInformationViewModel personalInfoVM)
{
    try
    {
        // Set a profile image
        var imageSource = LoadImageFromFile("path/to/profile/image.jpg");
        personalInfoVM.ProfileImage = imageSource;
        personalInfoVM.ProfileImageOriginalExtension = ".jpg";
        
        // Execute the image management command
        if (personalInfoVM.OpenImageManagementCommand.CanExecute(null))
        {
            personalInfoVM.OpenImageManagementCommand.Execute(null);
        }
        
        // Validate image was set
        if (personalInfoVM.ProfileImage != null)
        {
            LoggingService.LogInfo("Profile image set successfully", "PersonalInfo");
            return true;
        }
        
        return false;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error managing profile image: {ex.Message}", "PersonalInfo");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء إدارة صورة الملف الشخصي",
            "خطأ في الصورة",
            LogLevel.Error,
            "PersonalInfo");
        return false;
    }
}

private BitmapSource LoadImageFromFile(string imagePath)
{
    // Implementation would load image from file
    // This is a placeholder for the actual implementation
    var bitmap = new BitmapImage();
    bitmap.BeginInit();
    bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
    bitmap.EndInit();
    return bitmap;
}
```

### Data Validation and Error Handling

```csharp
// Comprehensive validation with error handling
public async Task<bool> ValidateAndProcessPersonalInfoAsync(PersonalInformationViewModel personalInfoVM)
{
    try
    {
        // Basic validation
        if (!personalInfoVM.IsValid())
        {
            LoggingService.LogWarning("Basic personal information validation failed", "PersonalInfo");
            ErrorManager.HandleErrorToast(new ValidationException("Personal info invalid"),
                "يرجى التحقق من البيانات الشخصية. الاسم باللاتينية مطلوب.",
                "خطأ في التحقق",
                LogLevel.Warning,
                "PersonalInfo");
            return false;
        }
        
        // Advanced validation
        if (!ValidateAdvancedPersonalInfo(personalInfoVM))
        {
            return false;
        }
        
        // Process the data
        await ProcessValidPersonalInfoAsync(personalInfoVM);
        
        LoggingService.LogInfo("Personal information validated and processed successfully", "PersonalInfo");
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error validating personal information: {ex.Message}", "PersonalInfo");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء التحقق من البيانات الشخصية",
            "خطأ في التحقق",
            LogLevel.Error,
            "PersonalInfo");
        return false;
    }
}

private bool ValidateAdvancedPersonalInfo(PersonalInformationViewModel personalInfoVM)
{
    // Validate birth date format if provided
    if (!string.IsNullOrWhiteSpace(personalInfoVM.BirthDate))
    {
        if (!IsValidDateFormat(personalInfoVM.BirthDate))
        {
            LoggingService.LogWarning($"Invalid birth date format: {personalInfoVM.BirthDate}", "PersonalInfo");
            ErrorManager.HandleErrorToast(new ValidationException("Invalid date format"),
                "تنسيق تاريخ الميلاد غير صحيح. يرجى استخدام تنسيق DD/MM/YYYY",
                "خطأ في التاريخ",
                LogLevel.Warning,
                "PersonalInfo");
            return false;
        }
    }
    
    // Validate national ID format if provided
    if (!string.IsNullOrWhiteSpace(personalInfoVM.NationalId))
    {
        if (!IsValidNationalIdFormat(personalInfoVM.NationalId))
        {
            LoggingService.LogWarning($"Invalid national ID format: {personalInfoVM.NationalId}", "PersonalInfo");
            ErrorManager.HandleErrorToast(new ValidationException("Invalid national ID"),
                "تنسيق رقم البطاقة الوطنية غير صحيح",
                "خطأ في رقم البطاقة",
                LogLevel.Warning,
                "PersonalInfo");
            return false;
        }
    }
    
    // Validate gender value
    if (personalInfoVM.Gender < 0 || personalInfoVM.Gender > 1)
    {
        LoggingService.LogWarning($"Invalid gender value: {personalInfoVM.Gender}", "PersonalInfo");
        ErrorManager.HandleErrorToast(new ValidationException("Invalid gender"),
            "قيمة الجنس غير صحيحة",
            "خطأ في الجنس",
            LogLevel.Warning,
            "PersonalInfo");
        return false;
    }
    
    return true;
}

private bool IsValidDateFormat(string dateString)
{
    // Support both complete dates (DD/MM/YYYY) and partial dates (xx/xx/YYYY)
    var datePattern = @"^(\d{2}|xx)/(\d{2}|xx)/\d{4}$";
    return System.Text.RegularExpressions.Regex.IsMatch(dateString, datePattern);
}

private bool IsValidNationalIdFormat(string nationalId)
{
    // Algerian national ID is typically 18 digits
    return nationalId.Length == 18 && nationalId.All(char.IsDigit);
}

private async Task ProcessValidPersonalInfoAsync(PersonalInformationViewModel personalInfoVM)
{
    // Process the validated personal information
    // This could involve saving to database, generating reports, etc.
    await Task.Delay(100); // Simulate processing
    LoggingService.LogInfo("Personal information processed", "PersonalInfo");
}
```

### Integration with NewClientViewModel

```csharp
// Example of how PersonalInformationViewModel integrates with NewClientViewModel
public class NewClientViewModel : BaseViewModel
{
    public PersonalInformationViewModel PersonalInfo { get; }
    
    public NewClientViewModel()
    {
        PersonalInfo = new PersonalInformationViewModel();
        
        // Subscribe to property changes
        PersonalInfo.PropertyChanged += OnPersonalInfoPropertyChanged;
    }
    
    private void OnPersonalInfoPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // React to personal info changes
        switch (e.PropertyName)
        {
            case nameof(PersonalInformationViewModel.NameFr):
                // Trigger duplicate client detection
                _ = CheckForDuplicateClientsAsync(PersonalInfo.NameFr);
                // Update save button state
                UpdateCanSave();
                break;
                
            case nameof(PersonalInformationViewModel.Gender):
                // Update profile image default based on gender
                UpdateDefaultProfileImage();
                break;
                
            case nameof(PersonalInformationViewModel.ProfileImage):
                // Handle profile image changes
                OnProfileImageChanged();
                break;
        }
    }
    
    private async Task CheckForDuplicateClientsAsync(string nameFr)
    {
        // Implementation for duplicate detection
        LoggingService.LogInfo($"Checking for duplicates: {nameFr}", "DuplicateDetection");
    }
    
    private void UpdateDefaultProfileImage()
    {
        // Update default profile image based on gender
        LoggingService.LogInfo($"Updating default profile image for gender: {PersonalInfo.Gender}", "ProfileImage");
    }
    
    private void OnProfileImageChanged()
    {
        // Handle profile image changes
        LoggingService.LogInfo("Profile image changed", "ProfileImage");
    }
    
    private void UpdateCanSave()
    {
        // Update the CanSave property based on validation
        CanSave = PersonalInfo.IsValid() && /* other validations */;
    }
}
```

### Advanced Usage with Complete Personal Info Setup

```csharp
public async Task<bool> SetupCompletePersonalInfoAsync(PersonalInformationViewModel personalInfoVM, PersonalInfoSetupData setupData)
{
    try
    {
        // Set all personal information fields
        personalInfoVM.NameFr = setupData.NameFr;
        personalInfoVM.NameAr = setupData.NameAr;
        personalInfoVM.BirthDate = setupData.BirthDate;
        personalInfoVM.BirthPlace = setupData.BirthPlace;
        personalInfoVM.Gender = setupData.Gender;
        personalInfoVM.Address = setupData.Address;
        personalInfoVM.NationalId = setupData.NationalId;
        
        // Set profile image if provided
        if (setupData.ProfileImagePath != null)
        {
            var profileImage = await LoadImageAsync(setupData.ProfileImagePath);
            personalInfoVM.ProfileImage = profileImage;
            personalInfoVM.ProfileImageOriginalExtension = Path.GetExtension(setupData.ProfileImagePath);
        }
        
        // Validate the complete setup
        if (!personalInfoVM.IsValid())
        {
            LoggingService.LogWarning("Complete personal info setup validation failed", "PersonalInfo");
            return false;
        }
        
        // Additional validation
        if (!ValidateAdvancedPersonalInfo(personalInfoVM))
        {
            return false;
        }
        
        LoggingService.LogInfo($"Complete personal info setup successful for {setupData.NameFr}", "PersonalInfo");
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error in complete personal info setup: {ex.Message}", "PersonalInfo");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء إعداد البيانات الشخصية",
            "خطأ في الإعداد",
            LogLevel.Error,
            "PersonalInfo");
        return false;
    }
}

private async Task<BitmapSource> LoadImageAsync(string imagePath)
{
    return await Task.Run(() =>
    {
        var bitmap = new BitmapImage();
        bitmap.BeginInit();
        bitmap.UriSource = new Uri(imagePath, UriKind.Absolute);
        bitmap.CacheOption = BitmapCacheOption.OnLoad;
        bitmap.EndInit();
        bitmap.Freeze(); // Make it cross-thread accessible
        return bitmap;
    });
}

// Supporting data class
public class PersonalInfoSetupData
{
    public string NameFr { get; set; } = string.Empty;
    public string NameAr { get; set; } = string.Empty;
    public string BirthDate { get; set; } = string.Empty;
    public string BirthPlace { get; set; } = string.Empty;
    public int Gender { get; set; }
    public string Address { get; set; } = string.Empty;
    public string NationalId { get; set; } = string.Empty;
    public string? ProfileImagePath { get; set; }
}
```

### XAML Data Binding Example

```xml
<UserControl x:Class="UFU2.Views.NewClient.NPersonalView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Text="المعلومات الشخصية"
                   Style="{StaticResource SubtitleStyle}"
                   HorizontalAlignment="Left"/>
        
        <!-- Personal Information Form -->
        <Grid Grid.Row="1" Margin="12,3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="127"/> <!-- Profile Picture -->
                <ColumnDefinition Width="*"/>   <!-- Form Fields -->
            </Grid.ColumnDefinitions>
            
            <!-- Profile Picture Card -->
            <materialDesign:Card Grid.Column="0"
                                Height="145"
                                Style="{StaticResource ContentCardStyle}">
                <Button Command="{Binding PersonalInfo.OpenImageManagementCommand}"
                        Style="{StaticResource ImageButtonStyle}">
                    <Image Source="{Binding PersonalInfo.ProfileImage}"
                           Stretch="UniformToFill"/>
                </Button>
            </materialDesign:Card>
            
            <!-- Form Fields Card -->
            <materialDesign:Card Grid.Column="1"
                                Height="145"
                                Margin="5,0,0,0"
                                Style="{StaticResource ContentCardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- First Row -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- French Name (Required) -->
                        <TextBox Grid.Column="0"
                                Text="{Binding PersonalInfo.NameFr, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                materialDesign:HintAssist.Hint="الاسم و اللقب(باللاتينية) *"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                        
                        <!-- Arabic Name -->
                        <TextBox Grid.Column="1"
                                Text="{Binding PersonalInfo.NameAr, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="الاسم واللقب (بالعربية)"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                        
                        <!-- Birth Date -->
                        <TextBox Grid.Column="2"
                                MinWidth="81"
                                Text="{Binding PersonalInfo.BirthDate, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="تاريخ الازدياد"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                        
                        <!-- Birth Place -->
                        <TextBox Grid.Column="3"
                                MinWidth="81"
                                Text="{Binding PersonalInfo.BirthPlace, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="مكان ازدياد"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                        
                        <!-- Gender -->
                        <ComboBox Grid.Column="4"
                                 MinWidth="54"
                                 SelectedIndex="{Binding PersonalInfo.Gender, UpdateSourceTrigger=PropertyChanged}"
                                 materialDesign:HintAssist.Hint="الجنس"
                                 Style="{StaticResource UnderlineComboBoxStyle}">
                            <ComboBoxItem Content="ذكر"/>
                            <ComboBoxItem Content="أنثى"/>
                        </ComboBox>
                    </Grid>
                    
                    <!-- Second Row -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3.5*"/>
                            <ColumnDefinition Width="1.8*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Address -->
                        <TextBox Grid.Column="0"
                                Text="{Binding PersonalInfo.Address, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="العنـــــــوان"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                        
                        <!-- National ID -->
                        <TextBox Grid.Column="1"
                                Text="{Binding PersonalInfo.NationalId, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="رقم بطاقة التعريف الوطنية"
                                Style="{StaticResource UnderlineTextBoxStyle}"/>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
```

## Integration with UFU2 Architecture

### MVVM Pattern Compliance

The PersonalInformationViewModel exemplifies UFU2's MVVM architecture:

```csharp
// Inherits from BaseViewModel for smart batching
public class PersonalInformationViewModel : BaseViewModel
{
    // Uses SetProperty for optimized property notifications
    public string NameFr
    {
        get => _nameFr;
        set => SetProperty(ref _nameFr, value);
    }
    
    // Command pattern implementation
    public ICommand OpenImageManagementCommand { get; }
    
    // Proper resource disposal
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            ProfileImage = null;
            LoggingService.LogDebug("PersonalInformationViewModel disposed", "PersonalInformationViewModel");
        }
        base.Dispose(disposing);
    }
}
```

### Error Handling Integration

```csharp
// Uses UFU2's ErrorManager for consistent error handling
private void OpenImageManagement()
{
    try
    {
        LoggingService.LogInfo("Image management dialog requested", "PersonalInformationViewModel");
        // Implementation logic here
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error opening image management dialog: {ex.Message}", "PersonalInformationViewModel");
        ErrorManager.HandleErrorToast(ex, 
            "حدث خطأ أثناء فتح نافذة إدارة الصور", 
            "خطأ في إدارة الصور", 
            LogLevel.Error, 
            "PersonalInformationViewModel");
    }
}
```

### Logging Integration

```csharp
// Comprehensive logging throughout the component
public void LoadFromClientData(DuplicateClientData clientData)
{
    if (clientData == null)
    {
        LoggingService.LogWarning("Cannot load personal information from null client data", "PersonalInformationViewModel");
        return;
    }

    try
    {
        // Load data logic
        LoggingService.LogInfo($"Personal information loaded for client: {clientData.ClientUid}", "PersonalInformationViewModel");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error loading personal information from client data: {ex.Message}", "PersonalInformationViewModel");
    }
}
```

## Performance Considerations

### Memory Management

The ViewModel implements proper resource disposal:

```csharp
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        // Clear image references to help with memory management
        ProfileImage = null;
        LoggingService.LogDebug("PersonalInformationViewModel disposed", "PersonalInformationViewModel");
    }
    base.Dispose(disposing);
}
```

### Smart Property Batching

Inherits BaseViewModel's smart batching system for optimal UI performance:

- **Normal Priority**: Standard property changes batched at 16ms intervals (60 FPS)
- **High Priority**: Frequent changes batched at 8ms intervals (120 FPS)
- **Critical Priority**: Immediate notifications bypass batching

### Efficient Change Detection

The `HasChanges` method implements efficient comparison logic:

```csharp
public bool HasChanges(DuplicateClientData? originalData)
{
    if (originalData == null)
        return true; // Treat as changes for new clients

    // Efficient string comparison with null normalization
    var currentNameAr = string.IsNullOrWhiteSpace(NameAr) ? null : NameAr.Trim();
    var originalNameAr = string.IsNullOrWhiteSpace(originalData.NameAr) ? null : originalData.NameAr.Trim();
    
    return !string.Equals(currentNameAr, originalNameAr, StringComparison.Ordinal);
    // ... other comparisons
}
```

### Image Handling Optimization

Profile image handling is optimized for memory usage:

- **Lazy Loading**: Images loaded only when needed
- **Format Preservation**: Original image format maintained via `ProfileImageOriginalExtension`
- **Memory Cleanup**: Image references cleared during disposal

## Component Architecture Diagram

```mermaid
classDiagram
    class PersonalInformationViewModel {
        -string _nameFr
        -string _nameAr
        -string _birthDate
        -string _birthPlace
        -int _gender
        -string _address
        -string _nationalId
        -BitmapSource _profileImage
        -string _profileImageOriginalExtension
        
        +string NameFr
        +string NameAr
        +string BirthDate
        +string BirthPlace
        +int Gender
        +string Address
        +string NationalId
        +BitmapSource ProfileImage
        +string ProfileImageOriginalExtension
        +ICommand OpenImageManagementCommand
        
        +bool IsValid()
        +void Clear()
        +void LoadFromClientData(DuplicateClientData)
        +bool HasChanges(DuplicateClientData)
        -void OpenImageManagement()
        +void Dispose(bool)
    }
    
    class BaseViewModel {
        <<abstract>>
        +PropertyPriority enum
        +UIState enum
        +BatchingStrategy enum
        #void SetProperty(ref T, T, string, PropertyPriority)
        #virtual void OnPropertyChanged(string, PropertyPriority)
        +abstract void Dispose(bool)
    }
    
    class DuplicateClientData {
        +string ClientUid
        +string NameFr
        +string NameAr
        +string BirthDate
        +string BirthPlace
        +int Gender
        +string Address
        +string NationalId
        +DuplicateClientData Clone()
    }
    
    class RelayCommand {
        +bool CanExecute(object)
        +void Execute(object)
        +event EventHandler CanExecuteChanged
    }
    
    class LoggingService {
        <<static>>
        +void LogDebug(string, string)
        +void LogInfo(string, string)
        +void LogWarning(string, string)
        +void LogError(string, string)
    }
    
    class ErrorManager {
        <<static>>
        +void HandleErrorToast(Exception, string, string, LogLevel, string)
    }
    
    PersonalInformationViewModel --|> BaseViewModel : inherits
    PersonalInformationViewModel --> DuplicateClientData : uses
    PersonalInformationViewModel --> RelayCommand : contains
    PersonalInformationViewModel --> LoggingService : uses
    PersonalInformationViewModel --> ErrorManager : uses
    
    note for PersonalInformationViewModel "Extracted from NewClientViewModel\nfor better separation of concerns\nManages client personal information\nwith validation and change detection"
    note for BaseViewModel "Provides smart property batching\nand optimized UI performance"
```

## Data Flow Diagram

```mermaid
flowchart TD
    A[User Input] --> B[PersonalInformationViewModel]
    B --> C{Validation}
    C -->|Valid| D[SetProperty]
    C -->|Invalid| E[Show Error Message]
    
    D --> F[BaseViewModel Smart Batching]
    F --> G[PropertyChanged Notification]
    G --> H[UI Update]
    
    I[Load Existing Data] --> J[LoadFromClientData]
    J --> K[Set Properties]
    K --> L[PropertyChanged Events]
    
    M[Change Detection] --> N[HasChanges Method]
    N --> O{Compare Fields}
    O -->|Changes Found| P[Return True]
    O -->|No Changes| Q[Return False]
    
    R[Image Management] --> S[OpenImageManagementCommand]
    S --> T[Execute Command]
    T --> U[Open Image Dialog]
    
    V[Clear Data] --> W[Clear Method]
    W --> X[Reset All Properties]
    X --> Y[PropertyChanged Events]
    
    Z[Dispose] --> AA[Dispose Method]
    AA --> BB[Clear Image References]
    BB --> CC[Base Dispose]
    
    style B fill:#e1f5fe
    style F fill:#f3e5f5
    style N fill:#fff3e0
    style S fill:#e8f5e8
```

## State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> Initialized : Constructor
    
    Initialized --> DataEntry : User Input
    DataEntry --> Validating : Property Changed
    Validating --> Valid : Validation Passed
    Validating --> Invalid : Validation Failed
    
    Valid --> DataEntry : Continue Input
    Invalid --> DataEntry : Fix Errors
    
    Initialized --> LoadingData : LoadFromClientData()
    LoadingData --> DataLoaded : Data Loaded
    DataLoaded --> DataEntry : Ready for Editing
    
    DataEntry --> CheckingChanges : HasChanges()
    CheckingChanges --> ChangesDetected : Changes Found
    CheckingChanges --> NoChanges : No Changes
    
    ChangesDetected --> DataEntry : Continue Editing
    NoChanges --> DataEntry : Continue Editing
    
    DataEntry --> ImageManagement : OpenImageManagementCommand
    ImageManagement --> DataEntry : Image Selected/Cancelled
    
    DataEntry --> Clearing : Clear()
    Clearing --> Initialized : Data Cleared
    
    DataEntry --> Disposing : Dispose()
    Disposing --> [*] : Resources Released
    
    Valid --> Disposing : Dispose()
    Invalid --> Disposing : Dispose()
    DataLoaded --> Disposing : Dispose()
```

## Validation Flow Diagram

```mermaid
flowchart TD
    A[Property Change] --> B{Is NameFr?}
    B -->|Yes| C[Check Required Field]
    B -->|No| D[Standard Validation]
    
    C --> E{Is Empty?}
    E -->|Yes| F[Validation Failed]
    E -->|No| G[Validation Passed]
    
    D --> H{Is BirthDate?}
    H -->|Yes| I[Check Date Format]
    H -->|No| J{Is NationalId?}
    
    I --> K{Valid Format?}
    K -->|Yes| G
    K -->|No| F
    
    J -->|Yes| L[Check ID Format]
    J -->|No| M{Is Gender?}
    
    L --> N{Valid ID?}
    N -->|Yes| G
    N -->|No| F
    
    M -->|Yes| O[Check Gender Range]
    M -->|No| G
    
    O --> P{Valid Range?}
    P -->|Yes| G
    P -->|No| F
    
    F --> Q[Log Warning]
    Q --> R[Show Error Message]
    
    G --> S[Log Success]
    S --> T[Update UI]
    
    style F fill:#ffebee
    style G fill:#e8f5e8
    style Q fill:#fff3e0
    style S fill:#f1f8e9
```

## Integration Points

### With NewClientViewModel

```mermaid
sequenceDiagram
    participant NCV as NewClientViewModel
    participant PIV as PersonalInformationViewModel
    participant UI as UI Layer
    participant Val as Validation

    NCV->>PIV: Initialize Component
    PIV->>NCV: PropertyChanged Events
    
    UI->>PIV: User Input (NameFr)
    PIV->>PIV: SetProperty
    PIV->>NCV: PropertyChanged(NameFr)
    NCV->>Val: Trigger Duplicate Check
    NCV->>NCV: Update CanSave
    
    UI->>PIV: Load Existing Client
    PIV->>PIV: LoadFromClientData
    PIV->>NCV: Multiple PropertyChanged
    NCV->>NCV: Update UI State
    
    UI->>PIV: Check Changes
    PIV->>PIV: HasChanges
    PIV->>NCV: Return Change Status
    NCV->>UI: Update Save Button
```

### With Database Services

```mermaid
sequenceDiagram
    participant PIV as PersonalInformationViewModel
    participant CDS as ClientDatabaseService
    participant DB as Database
    participant Audit as AuditService

    PIV->>CDS: Save Personal Info
    CDS->>DB: Begin Transaction
    CDS->>DB: Insert/Update Client
    CDS->>Audit: Log Changes
    Audit->>DB: Insert Audit Record
    CDS->>DB: Commit Transaction
    DB->>PIV: Success Response
```

## Conclusion

The PersonalInformationViewModel represents a well-architected component that successfully extracts personal information management from the larger NewClientViewModel. It provides comprehensive data validation, change detection, and seamless integration with the UFU2 architecture while maintaining excellent performance through smart property batching and efficient memory management.

### Key Strengths

- **Single Responsibility**: Focused solely on personal information management
- **Validation**: Comprehensive validation with Arabic error messages
- **Change Detection**: Efficient comparison for editing scenarios
- **Integration**: Seamless integration with UFU2 MVVM architecture
- **Performance**: Optimized property notifications and memory management
- **Maintainability**: Clean, well-documented code with proper error handling

### Usage Recommendations

1. **Validation**: Always call `IsValid()` before processing personal information
2. **Change Detection**: Use `HasChanges()` for editing scenarios to detect modifications
3. **Memory Management**: Ensure proper disposal when component is no longer needed
4. **Error Handling**: Leverage built-in error handling with Arabic localization
5. **Integration**: Subscribe to PropertyChanged events for reactive UI updates

This component serves as an excellent example of the composition pattern in MVVM architecture, providing focused functionality while maintaining clean separation of concerns and excellent integration capabilities.