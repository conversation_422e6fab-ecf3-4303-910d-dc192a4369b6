﻿#pragma checksum "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0EE65B136139DF2F6C98B11D6EE2C541B1F2C38B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.MahApps;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UFU2.Views.NewClient {
    
    
    /// <summary>
    /// NFileCheckView
    /// </summary>
    public partial class NFileCheckView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CasChip;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox NifChip;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox NisChip;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RcChip;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ArtChip;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AgrChip;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DexChip;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddNotes;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock G12Text;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BISText;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button YearSelectorButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UFU2;V1.0.0.0;component/views/newclient/nfilecheckview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CasChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.NifChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.NisChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.RcChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.ArtChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.AgrChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.DexChip = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.AddNotes = ((System.Windows.Controls.Button)(target));
            
            #line 139 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
            this.AddNotes.Click += new System.Windows.RoutedEventHandler(this.AddNotes_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.G12Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.BISText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.YearSelectorButton = ((System.Windows.Controls.Button)(target));
            
            #line 215 "..\..\..\..\..\Views\NewClient\NFileCheckView.xaml"
            this.YearSelectorButton.Click += new System.Windows.RoutedEventHandler(this.YearSelectorButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

