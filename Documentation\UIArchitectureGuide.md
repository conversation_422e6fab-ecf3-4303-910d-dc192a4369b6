# UFU2 UI Architecture Guide

## Overview

This guide provides comprehensive documentation for UFU2's UI architecture, covering MVVM patterns, MaterialDesign integration, Arabic RTL layout implementation, and component development standards. UFU2 follows established architectural patterns to ensure consistency, maintainability, and proper Arabic localization support.

## Architecture Foundations

UFU2's UI architecture is built on four core pillars:

- **MVVM Pattern**: Model-View-ViewModel with BaseViewModel inheritance
- **MaterialDesign Integration**: Consistent theming with DynamicResource bindings
- **Arabic RTL Support**: Right-to-left layout with proper text handling
- **Service Integration**: ServiceLocator dependency injection throughout UI components

---

## MVVM Pattern Implementation

### BaseViewModel Inheritance

All ViewModels in UFU2 must inherit from `BaseViewModel` which provides essential MVVM infrastructure:

```csharp
/// <summary>
/// Example ViewModel following UFU2 MVVM patterns
/// </summary>
public class ClientManagementViewModel : BaseViewModel
{
    #region Private Fields
    private bool _isLoading;
    private string _firstName = string.Empty;
    private readonly ClientDatabaseService _clientService;
    #endregion

    #region Properties
    /// <summary>
    /// Loading state property with proper change notification
    /// </summary>
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    /// <summary>
    /// Client first name with validation integration
    /// </summary>
    public string FirstName
    {
        get => _firstName;
        set
        {
            if (SetProperty(ref _firstName, value))
            {
                // Trigger validation and command state updates
                ValidateProperty(value, nameof(FirstName));
                SaveCommand.RaiseCanExecuteChanged();
            }
        }
    }
    #endregion

    #region Commands
    /// <summary>
    /// Save command with proper RelayCommand implementation
    /// </summary>
    public RelayCommand SaveCommand { get; private set; }
    #endregion

    #region Constructor
    /// <summary>
    /// Constructor with ServiceLocator dependency injection
    /// </summary>
    public ClientManagementViewModel()
    {
        // Initialize services through ServiceLocator
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        
        // Initialize commands with proper naming for debugging
        SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveClient");
        
        LoggingService.LogDebug("ClientManagementViewModel initialized", "ClientManagementViewModel");
    }
    #endregion

    #region Command Implementations
    private async void ExecuteSave(object? parameter)
    {
        try
        {
            IsLoading = true;
            
            var clientData = new ClientCreationData
            {
                FirstName = FirstName,
                // ... other properties
            };
            
            var clientId = await _clientService.CreateClientAsync(clientData);
            
            // Show Arabic success message
            ErrorManager.ShowUserSuccessToast("تم حفظ بيانات العميل بنجاح", "نجح الحفظ", "ClientManagement");
            
            LoggingService.LogInfo($"Client saved successfully: {clientId}", "ClientManagementViewModel");
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل", "خطأ في قاعدة البيانات", 
                                   LogLevel.Error, "ClientManagement");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private bool CanExecuteSave(object? parameter)
    {
        return !IsLoading && !string.IsNullOrWhiteSpace(FirstName);
    }
    #endregion
}
```

### RelayCommand Usage Patterns

UFU2 uses a standardized RelayCommand implementation with logging integration:

```csharp
// Standard command initialization in ViewModel constructor
SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveClient");
DeleteCommand = new RelayCommand(ExecuteDelete, CanExecuteDelete, "DeleteClient");
CancelCommand = new RelayCommand(ExecuteCancel, CanExecuteCancel, "CancelOperation");

// Parameterized command example
SwitchTabCommand = new RelayCommand(ExecuteSwitchTab, CanExecuteSwitchTab, "SwitchTab");

// Command execution with proper error handling
private async void ExecuteSwitchTab(object? parameter)
{
    try
    {
        if (parameter is string tabName)
        {
            SelectedTab = tabName;
            LoggingService.LogDebug($"Switched to tab: {tabName}", "ViewModel");
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error switching tab: {ex.Message}", "ViewModel");
        ErrorManager.HandleError(ex, "خطأ في تبديل التبويب", "خطأ في الواجهة", LogLevel.Warning, "UI");
    }
}
```

---

## MaterialDesign Component Integration

### DynamicResource Theming

UFU2 uses DynamicResource bindings to support Light/Dark theme switching:

```xml
<!-- Button with proper MaterialDesign theming -->
<Button
    Style="{StaticResource PrimaryButtonStyle}"
    Background="{DynamicResource ButtonBackgroundPrimary}"
    Foreground="{DynamicResource ButtonForegroundPrimary}"
    BorderBrush="{DynamicResource ButtonBorderPrimary}"
    materialDesign:ElevationAssist.Elevation="Dp3"
    materialDesign:ButtonAssist.CornerRadius="2"
    Command="{Binding SaveCommand}"
    Content="حفظ"
    ToolTip="حفظ بيانات العميل" />

<!-- Card with MaterialDesign styling -->
<materialDesign:Card
    Background="{DynamicResource CardBackgroundBase}"
    Foreground="{DynamicResource CardForegroundBase}"
    materialDesign:ElevationAssist.Elevation="Dp4"
    Margin="{StaticResource MediumMargin}"
    Padding="{StaticResource MediumPadding}">
    
    <StackPanel Orientation="Vertical">
        <TextBlock
            Text="معلومات العميل"
            Style="{StaticResource HeadingTextStyle}"
            Foreground="{DynamicResource HeadingForegroundBase}" />
        
        <!-- Content here -->
    </StackPanel>
</materialDesign:Card>

<!-- Input controls with MaterialDesign styling -->
<materialDesign:PackIcon
    Kind="Person"
    Width="24"
    Height="24"
    Foreground="{DynamicResource IconForegroundBase}"
    VerticalAlignment="Center" />

<TextBox
    Style="{StaticResource MaterialDesignOutlinedTextBox}"
    materialDesign:HintAssist.Hint="الاسم الأول"
    materialDesign:HintAssist.IsFloating="True"
    materialDesign:TextFieldAssist.HasClearButton="True"
    Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged}"
    FlowDirection="RightToLeft"
    TextAlignment="Right" />
```

### Dialog Integration

UFU2 uses MaterialDesign DialogHost for consistent modal behavior:

```xml
<!-- Dialog host in main window -->
<materialDesign:DialogHost
    x:Name="MainDialogHost"
    Identifier="MainDialog"
    DialogTheme="Inherit">
    
    <!-- Main content -->
    <Grid>
        <!-- Application content -->
    </Grid>
</materialDesign:DialogHost>
```

```csharp
// Opening dialogs from ViewModels
private async void ExecuteOpenDialog(object? parameter)
{
    try
    {
        var dialogViewModel = new ClientEditDialogViewModel(clientId);
        var dialogView = new ClientEditDialog { DataContext = dialogViewModel };
        
        var result = await DialogHost.Show(dialogView, "MainDialog");
        
        if (result is bool success && success)
        {
            // Handle successful dialog completion
            await RefreshClientDataAsync();
            ErrorManager.ShowUserSuccessToast("تم تحديث بيانات العميل بنجاح", "تم التحديث", "ClientManagement");
        }
    }
    catch (Exception ex)
    {
        ErrorManager.HandleError(ex, "خطأ في فتح نافذة التحرير", "خطأ في الواجهة", LogLevel.Error, "DialogManagement");
    }
}
```

---

## Arabic RTL Layout Implementation

### XAML RTL Configuration

All UFU2 views must support Arabic RTL layout:

```xml
<!-- UserControl with RTL support -->
<UserControl
    x:Class="UFU2.Views.ClientManagementView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    FlowDirection="RightToLeft"
    AutomationProperties.Name="إدارة العملاء"
    AutomationProperties.HelpText="نموذج إدارة بيانات العملاء">

    <Grid>
        <!-- RTL-aware layout -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        
        <!-- Content aligned for RTL -->
        <StackPanel Grid.Column="0" Orientation="Vertical">
            <TextBlock
                Text="معلومات العميل"
                TextAlignment="Right"
                FlowDirection="RightToLeft"
                Style="{StaticResource HeadingTextStyle}" />
            
            <!-- Form fields with proper RTL alignment -->
            <Grid Margin="{StaticResource MediumMargin}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <!-- Label on the right for RTL -->
                <TextBlock
                    Grid.Column="0"
                    Text="الاسم:"
                    VerticalAlignment="Center"
                    Margin="0,0,10,0"
                    TextAlignment="Right" />
                
                <!-- Input on the left for RTL -->
                <TextBox
                    Grid.Column="1"
                    Text="{Binding FirstName}"
                    FlowDirection="RightToLeft"
                    TextAlignment="Right"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}" />
            </Grid>
        </StackPanel>
        
        <!-- Action buttons aligned for RTL -->
        <StackPanel Grid.Column="1" Orientation="Vertical" Margin="10,0,0,0">
            <Button Content="حفظ" Command="{Binding SaveCommand}" />
            <Button Content="إلغاء" Command="{Binding CancelCommand}" />
        </StackPanel>
    </Grid>
</UserControl>
```

### Arabic Text Handling in ViewModels

```csharp
/// <summary>
/// Proper Arabic text validation and handling
/// </summary>
public class ArabicTextViewModel : BaseViewModel
{
    private string _arabicText = string.Empty;
    
    public string ArabicText
    {
        get => _arabicText;
        set
        {
            if (SetProperty(ref _arabicText, value))
            {
                ValidateArabicText(value);
            }
        }
    }
    
    private void ValidateArabicText(string text)
    {
        ClearValidationErrors(nameof(ArabicText));
        
        if (string.IsNullOrWhiteSpace(text))
        {
            AddValidationError(nameof(ArabicText), "هذا الحقل مطلوب");
            return;
        }
        
        // Validate Arabic characters (basic check)
        if (!IsValidArabicText(text))
        {
            AddValidationError(nameof(ArabicText), "يجب إدخال نص عربي صحيح");
        }
    }
    
    private bool IsValidArabicText(string text)
    {
        // Basic Arabic text validation
        return text.All(c => char.IsWhiteSpace(c) || 
                           (c >= '\u0600' && c <= '\u06FF') || // Arabic block
                           (c >= '\u0750' && c <= '\u077F') || // Arabic Supplement
                           (c >= '\uFB50' && c <= '\uFDFF') || // Arabic Presentation Forms-A
                           (c >= '\uFE70' && c <= '\uFEFF'));  // Arabic Presentation Forms-B
    }
}
```

---

## UserControl Creation and Reuse Patterns

### Reusable Component Structure

UFU2 follows a standardized pattern for creating reusable UserControls:

```csharp
/// <summary>
/// Example reusable UserControl following UFU2 patterns
/// </summary>
public partial class ClientInfoCard : UserControl
{
    #region Dependency Properties
    
    /// <summary>
    /// Client data dependency property for binding
    /// </summary>
    public static readonly DependencyProperty ClientDataProperty =
        DependencyProperty.Register(
            nameof(ClientData),
            typeof(ClientModel),
            typeof(ClientInfoCard),
            new PropertyMetadata(null, OnClientDataChanged));
    
    public ClientModel ClientData
    {
        get => (ClientModel)GetValue(ClientDataProperty);
        set => SetValue(ClientDataProperty, value);
    }
    
    /// <summary>
    /// Command for edit action
    /// </summary>
    public static readonly DependencyProperty EditCommandProperty =
        DependencyProperty.Register(
            nameof(EditCommand),
            typeof(ICommand),
            typeof(ClientInfoCard),
            new PropertyMetadata(null));
    
    public ICommand EditCommand
    {
        get => (ICommand)GetValue(EditCommandProperty);
        set => SetValue(EditCommandProperty, value);
    }
    
    #endregion
    
    #region Constructor
    
    public ClientInfoCard()
    {
        InitializeComponent();
        LoggingService.LogDebug("ClientInfoCard initialized", "ClientInfoCard");
    }
    
    #endregion
    
    #region Property Change Handlers
    
    private static void OnClientDataChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ClientInfoCard control)
        {
            control.UpdateDisplay();
        }
    }
    
    private void UpdateDisplay()
    {
        // Update UI based on ClientData
        if (ClientData != null)
        {
            // Bind data to UI elements
            LoggingService.LogDebug($"Updated display for client: {ClientData.UID}", "ClientInfoCard");
        }
    }
    
    #endregion
}
```

### UserControl XAML Template

```xml
<UserControl
    x:Class="UFU2.Views.UserControls.ClientInfoCard"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    FlowDirection="RightToLeft"
    AutomationProperties.Name="بطاقة معلومات العميل">
    
    <materialDesign:Card
        Background="{DynamicResource CardBackgroundBase}"
        Foreground="{DynamicResource CardForegroundBase}"
        materialDesign:ElevationAssist.Elevation="Dp2"
        Margin="{StaticResource SmallMargin}"
        Padding="{StaticResource MediumPadding}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <TextBlock
                Grid.Row="0"
                Text="{Binding ClientData.FullName, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Style="{StaticResource SubheadingTextStyle}"
                TextAlignment="Right" />
            
            <!-- Content -->
            <StackPanel Grid.Row="1" Orientation="Vertical" Margin="0,10">
                <TextBlock Text="{Binding ClientData.UID, StringFormat='معرف العميل: {0}', RelativeSource={RelativeSource AncestorType=UserControl}}" />
                <TextBlock Text="{Binding ClientData.PhoneNumber, StringFormat='الهاتف: {0}', RelativeSource={RelativeSource AncestorType=UserControl}}" />
            </StackPanel>
            
            <!-- Actions -->
            <Button
                Grid.Row="2"
                Content="تحرير"
                Command="{Binding EditCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                CommandParameter="{Binding ClientData, RelativeSource={RelativeSource AncestorType=UserControl}}"
                Style="{StaticResource SecondaryButtonStyle}"
                HorizontalAlignment="Left" />
        </Grid>
    </materialDesign:Card>
</UserControl>
```

---

## View-ViewModel Binding Examples

### Data Binding Patterns

UFU2 uses comprehensive data binding with validation support:

```xml
<!-- Two-way binding with validation -->
<TextBox
    Text="{Binding FirstName, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
    Style="{StaticResource MaterialDesignOutlinedTextBox}"
    materialDesign:HintAssist.Hint="الاسم الأول"
    materialDesign:HintAssist.IsFloating="True"
    FlowDirection="RightToLeft"
    TextAlignment="Right">

    <!-- Validation error template -->
    <TextBox.Style>
        <Style BasedOn="{StaticResource MaterialDesignOutlinedTextBox}" TargetType="TextBox">
            <Style.Triggers>
                <Trigger Property="Validation.HasError" Value="True">
                    <Setter Property="BorderBrush" Value="{DynamicResource ValidationErrorBrush}" />
                    <Setter Property="ToolTip" Value="{Binding RelativeSource={RelativeSource Self}, Path=(Validation.Errors)[0].ErrorContent}" />
                </Trigger>
            </Style.Triggers>
        </Style>
    </TextBox.Style>
</TextBox>

<!-- Command binding with parameter -->
<Button
    Content="حذف"
    Command="{Binding DeleteCommand}"
    CommandParameter="{Binding SelectedItem}"
    Style="{StaticResource DangerButtonStyle}">

    <!-- Button enabled state based on selection -->
    <Button.Style>
        <Style BasedOn="{StaticResource DangerButtonStyle}" TargetType="Button">
            <Style.Triggers>
                <DataTrigger Binding="{Binding SelectedItem}" Value="{x:Null}">
                    <Setter Property="IsEnabled" Value="False" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Button.Style>
</Button>

<!-- Collection binding with ItemTemplate -->
<ListBox
    ItemsSource="{Binding Clients}"
    SelectedItem="{Binding SelectedClient}"
    FlowDirection="RightToLeft"
    ScrollViewer.HorizontalScrollBarVisibility="Disabled">

    <ListBox.ItemTemplate>
        <DataTemplate>
            <userControls:ClientInfoCard
                ClientData="{Binding}"
                EditCommand="{Binding DataContext.EditClientCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                Margin="{StaticResource SmallMargin}" />
        </DataTemplate>
    </ListBox.ItemTemplate>
</ListBox>
```

### Validation Display Patterns

```csharp
/// <summary>
/// ViewModel with IDataErrorInfo validation support
/// </summary>
public class ValidatedViewModel : BaseViewModel, IDataErrorInfo
{
    private readonly Dictionary<string, string> _validationErrors = new();

    public string Error => string.Empty;

    public string this[string columnName]
    {
        get
        {
            _validationErrors.TryGetValue(columnName, out var error);
            return error ?? string.Empty;
        }
    }

    protected void AddValidationError(string propertyName, string errorMessage)
    {
        _validationErrors[propertyName] = errorMessage;
        OnPropertyChanged(nameof(Error));
    }

    protected void ClearValidationErrors(string propertyName)
    {
        _validationErrors.Remove(propertyName);
        OnPropertyChanged(nameof(Error));
    }

    protected bool HasValidationErrors => _validationErrors.Any();
}
```

---

## Theme-Aware Component Development

### Creating Theme-Responsive Components

```csharp
/// <summary>
/// Component that responds to theme changes
/// </summary>
public partial class ThemeAwareComponent : UserControl
{
    public ThemeAwareComponent()
    {
        InitializeComponent();

        // Subscribe to theme changes
        ThemeService.ThemeChanged += OnThemeChanged;

        // Set initial theme-based properties
        UpdateThemeProperties();
    }

    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        try
        {
            LoggingService.LogDebug($"Theme changed from {e.PreviousTheme} to {e.NewTheme}", "ThemeAwareComponent");
            UpdateThemeProperties();
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error handling theme change: {ex.Message}", "ThemeAwareComponent");
        }
    }

    private void UpdateThemeProperties()
    {
        // Update theme-specific properties
        var isDarkTheme = ThemeManager.CurrentTheme == ApplicationTheme.Dark;

        // Example: Update icon based on theme
        if (ThemeIcon != null)
        {
            ThemeIcon.Kind = isDarkTheme ? PackIconKind.Brightness4 : PackIconKind.Brightness7;
        }
    }

    protected override void OnUnloaded(RoutedEventArgs e)
    {
        // Unsubscribe from theme changes to prevent memory leaks
        ThemeService.ThemeChanged -= OnThemeChanged;
        base.OnUnloaded(e);
    }
}
```

### DynamicResource Usage Patterns

```xml
<!-- Theme-aware styling with DynamicResource -->
<Style x:Key="ThemeAwareCardStyle" TargetType="materialDesign:Card">
    <Setter Property="Background" Value="{DynamicResource CardBackgroundBase}" />
    <Setter Property="Foreground" Value="{DynamicResource CardForegroundBase}" />
    <Setter Property="BorderBrush" Value="{DynamicResource CardBorderBase}" />
    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2" />

    <!-- Theme-specific triggers -->
    <Style.Triggers>
        <DataTrigger Binding="{Binding Source={x:Static services:ThemeManager.CurrentTheme}}" Value="Dark">
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4" />
        </DataTrigger>
    </Style.Triggers>
</Style>

<!-- Icons that change with theme -->
<materialDesign:PackIcon
    Kind="{Binding Source={x:Static services:ThemeManager.CurrentTheme}, Converter={StaticResource ThemeToIconConverter}}"
    Foreground="{DynamicResource IconForegroundBase}"
    Width="24"
    Height="24" />
```

---

## ServiceLocator Integration in UI

### ViewModel Service Access

```csharp
/// <summary>
/// Proper service access pattern in ViewModels
/// </summary>
public class ServiceIntegratedViewModel : BaseViewModel
{
    #region Services
    private readonly ClientDatabaseService _clientService;
    private readonly ValidationService _validationService;
    private readonly ErrorManager _errorManager;
    #endregion

    public ServiceIntegratedViewModel()
    {
        // Initialize services through ServiceLocator
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _validationService = ServiceLocator.GetService<ValidationService>();

        // Verify service availability
        if (_clientService == null)
        {
            LoggingService.LogError("ClientDatabaseService not available", "ServiceIntegratedViewModel");
            throw new InvalidOperationException("Required services not available");
        }

        InitializeCommands();
    }

    private void InitializeCommands()
    {
        LoadDataCommand = new RelayCommand(ExecuteLoadData, CanExecuteLoadData, "LoadData");
        SaveDataCommand = new RelayCommand(ExecuteSaveData, CanExecuteSaveData, "SaveData");
    }

    private async void ExecuteLoadData(object? parameter)
    {
        try
        {
            IsLoading = true;

            // Use service with proper error handling
            var clients = await _clientService.GetAllClientsAsync();

            // Update UI properties
            Clients.Clear();
            foreach (var client in clients)
            {
                Clients.Add(client);
            }

            LoggingService.LogInfo($"Loaded {clients.Count} clients", "ServiceIntegratedViewModel");
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في تحميل بيانات العملاء", "خطأ في قاعدة البيانات",
                                   LogLevel.Error, "DataLoading");
        }
        finally
        {
            IsLoading = false;
        }
    }
}
```

---

## Error Display Patterns

### Arabic Error Message Display

```csharp
/// <summary>
/// Standardized error display patterns for UFU2
/// </summary>
public class ErrorDisplayViewModel : BaseViewModel
{
    private string _errorMessage = string.Empty;
    private bool _hasError;

    public string ErrorMessage
    {
        get => _errorMessage;
        set => SetProperty(ref _errorMessage, value);
    }

    public bool HasError
    {
        get => _hasError;
        set => SetProperty(ref _hasError, value);
    }

    /// <summary>
    /// Display validation error with Arabic message
    /// </summary>
    private void ShowValidationError(string arabicMessage)
    {
        ErrorMessage = arabicMessage;
        HasError = true;

        // Also show toast notification
        ErrorManager.ShowUserErrorToast(arabicMessage, "خطأ في التحقق", "Validation");

        LoggingService.LogWarning($"Validation error: {arabicMessage}", "ErrorDisplayViewModel");
    }

    /// <summary>
    /// Clear error state
    /// </summary>
    private void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    /// <summary>
    /// Handle service errors with proper Arabic messaging
    /// </summary>
    private void HandleServiceError(Exception ex, string context)
    {
        var arabicMessage = GetArabicErrorMessage(ex, context);

        ErrorManager.HandleError(ex, arabicMessage, "خطأ في النظام", LogLevel.Error, context);

        ShowValidationError(arabicMessage);
    }

    private string GetArabicErrorMessage(Exception ex, string context)
    {
        return context switch
        {
            "DatabaseConnection" => "خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى.",
            "ValidationFailed" => "البيانات المدخلة غير صحيحة. يرجى التحقق من البيانات.",
            "SaveOperation" => "حدث خطأ أثناء حفظ البيانات. يرجى المحاولة مرة أخرى.",
            "LoadOperation" => "حدث خطأ أثناء تحميل البيانات. يرجى إعادة تحميل الصفحة.",
            _ => "حدث خطأ غير متوقع. يرجى الاتصال بالدعم الفني."
        };
    }
}
```

### Error Display XAML

```xml
<!-- Error display panel -->
<Border
    Background="{DynamicResource ErrorBackgroundBrush}"
    BorderBrush="{DynamicResource ErrorBorderBrush}"
    BorderThickness="1"
    CornerRadius="4"
    Padding="{StaticResource MediumPadding}"
    Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">

    <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
        <materialDesign:PackIcon
            Kind="AlertCircle"
            Width="20"
            Height="20"
            Foreground="{DynamicResource ErrorForegroundBrush}"
            VerticalAlignment="Center"
            Margin="0,0,8,0" />

        <TextBlock
            Text="{Binding ErrorMessage}"
            Foreground="{DynamicResource ErrorForegroundBrush}"
            TextWrapping="Wrap"
            VerticalAlignment="Center"
            FlowDirection="RightToLeft"
            TextAlignment="Right" />
    </StackPanel>
</Border>

<!-- Success message display -->
<Border
    Background="{DynamicResource SuccessBackgroundBrush}"
    BorderBrush="{DynamicResource SuccessBorderBrush}"
    BorderThickness="1"
    CornerRadius="4"
    Padding="{StaticResource MediumPadding}"
    Visibility="{Binding HasSuccess, Converter={StaticResource BooleanToVisibilityConverter}}">

    <StackPanel Orientation="Horizontal" FlowDirection="RightToLeft">
        <materialDesign:PackIcon
            Kind="CheckCircle"
            Width="20"
            Height="20"
            Foreground="{DynamicResource SuccessForegroundBrush}"
            VerticalAlignment="Center"
            Margin="0,0,8,0" />

        <TextBlock
            Text="{Binding SuccessMessage}"
            Foreground="{DynamicResource SuccessForegroundBrush}"
            TextWrapping="Wrap"
            VerticalAlignment="Center"
            FlowDirection="RightToLeft"
            TextAlignment="Right" />
    </StackPanel>
</Border>
```

---

## UI Architecture Flow

### MVVM Data Flow Diagram

```mermaid
graph TB
    subgraph "View Layer"
        V[XAML Views]
        UC[UserControls]
        D[Dialogs]
    end

    subgraph "ViewModel Layer"
        BVM[BaseViewModel]
        VM[Concrete ViewModels]
        RC[RelayCommands]
        VAL[Validation Logic]
    end

    subgraph "Service Layer"
        SL[ServiceLocator]
        CDS[ClientDatabaseService]
        VS[ValidationService]
        EM[ErrorManager]
        LS[LoggingService]
    end

    subgraph "Model Layer"
        M[Data Models]
        E[Database Entities]
        VR[Validation Results]
    end

    %% View to ViewModel connections
    V -->|Data Binding| VM
    V -->|Command Binding| RC
    UC -->|Property Binding| VM
    D -->|Dialog Results| VM

    %% ViewModel connections
    VM --|Inherits from| BVM
    VM -->|Uses| RC
    VM -->|Implements| VAL
    BVM -->|Property Change| V

    %% Service connections
    VM -->|Dependency Injection| SL
    SL -->|Provides| CDS
    SL -->|Provides| VS
    SL -->|Provides| EM
    SL -->|Provides| LS

    %% Model connections
    VM -->|Works with| M
    CDS -->|Maps to| E
    VS -->|Returns| VR

    %% Error handling flow
    VM -->|Errors| EM
    EM -->|Arabic Messages| V
    LS -->|Logging| VM

    style V fill:#e1f5fe
    style VM fill:#f3e5f5
    style SL fill:#e8f5e8
    style M fill:#fff3e0
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant V as View (XAML)
    participant VM as ViewModel
    participant SL as ServiceLocator
    participant S as Service
    participant EM as ErrorManager

    U->>V: User Action (Click, Input)
    V->>VM: Command/Property Binding

    VM->>SL: GetService<T>()
    SL->>VM: Return Service Instance

    VM->>S: Execute Business Logic

    alt Success Path
        S->>VM: Return Success Result
        VM->>V: Update Properties
        V->>U: Display Success (Arabic)
        VM->>EM: ShowUserSuccessToast()
        EM->>U: Show Success Notification
    else Error Path
        S->>VM: Throw Exception
        VM->>EM: HandleError() with Arabic Message
        EM->>VM: Formatted Error Response
        VM->>V: Update Error Properties
        V->>U: Display Error (Arabic RTL)
    end

    VM->>VM: Log Operation
    VM->>V: PropertyChanged Notification
    V->>U: UI Update Complete
```

### Theme Integration Architecture

```mermaid
graph LR
    subgraph "Theme System"
        TM[ThemeManager]
        TS[ThemeService]
        LT[Light Theme]
        DT[Dark Theme]
    end

    subgraph "Resource System"
        DR[DynamicResource]
        RD[ResourceDictionary]
        CS[Custom Styles]
    end

    subgraph "UI Components"
        V[Views]
        UC[UserControls]
        B[Buttons]
        C[Cards]
        I[Icons]
    end

    %% Theme management flow
    TM -->|Manages| LT
    TM -->|Manages| DT
    TM -->|Notifies| TS
    TS -->|Theme Changed Event| V

    %% Resource binding flow
    TM -->|Updates| RD
    RD -->|Contains| DR
    DR -->|Binds to| CS

    %% Component theming flow
    CS -->|Styles| V
    CS -->|Styles| UC
    CS -->|Styles| B
    CS -->|Styles| C
    CS -->|Styles| I

    %% Dynamic updates
    V -->|Subscribes to| TS
    UC -->|Subscribes to| TS

    style TM fill:#e1f5fe
    style DR fill:#f3e5f5
    style V fill:#e8f5e8
```

---

## Best Practices and Guidelines

### ViewModel Development Guidelines

1. **Always inherit from BaseViewModel** for consistent property change notification
2. **Use ServiceLocator for dependency injection** - never create service instances directly
3. **Implement proper command patterns** with RelayCommand and descriptive names
4. **Handle errors with Arabic messages** using ErrorManager integration
5. **Log all significant operations** for debugging and monitoring
6. **Validate user input** with Arabic error messages and proper UI feedback

### XAML Development Guidelines

1. **Set FlowDirection="RightToLeft"** on all UserControls and Views
2. **Use DynamicResource for all theme-aware properties** (colors, brushes, styles)
3. **Implement proper accessibility** with AutomationProperties
4. **Follow MaterialDesign patterns** for consistent visual design
5. **Use proper text alignment** (Right for Arabic, appropriate for content)
6. **Implement validation display** with clear Arabic error messages

### Service Integration Guidelines

1. **Access services through ServiceLocator** in ViewModel constructors
2. **Check service availability** and handle null cases gracefully
3. **Use async/await patterns** for all service calls
4. **Implement proper error handling** with context-specific Arabic messages
5. **Log service operations** for debugging and performance monitoring
6. **Dispose resources properly** to prevent memory leaks

### Arabic RTL Guidelines

1. **Test all layouts with Arabic text** to ensure proper RTL flow
2. **Use appropriate text alignment** (Right for Arabic content)
3. **Consider icon positioning** in RTL layouts
4. **Validate Arabic text input** with proper character set checking
5. **Provide Arabic tooltips and help text** for all interactive elements
6. **Test with different Arabic fonts** to ensure compatibility

---

## Performance Considerations

### UI Performance Optimization

- **Use virtualization** for large data collections (VirtualizingStackPanel)
- **Implement proper data binding** with UpdateSourceTrigger optimization
- **Minimize property change notifications** by checking value changes
- **Use async operations** for heavy UI operations
- **Implement proper disposal** of event subscriptions and resources

### Memory Management

- **Unsubscribe from events** in UserControl unload handlers
- **Dispose ViewModels** that implement IDisposable
- **Use weak references** for long-lived event subscriptions
- **Monitor memory usage** in development and testing
- **Implement proper cleanup** in dialog and window closing

### Theme Performance

- **Cache theme resources** to avoid repeated lookups
- **Minimize theme change operations** by batching updates
- **Use efficient resource binding** with DynamicResource only when needed
- **Optimize custom styles** to reduce rendering overhead
- **Test theme switching performance** under various conditions

This comprehensive UI Architecture Guide provides the foundation for developing consistent, maintainable, and properly localized UI components in UFU2 while following established MVVM patterns and MaterialDesign principles.
