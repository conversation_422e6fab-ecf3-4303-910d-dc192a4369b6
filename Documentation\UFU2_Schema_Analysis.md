# UFU2 Database Schema Analysis - PascalCase Naming Convention

## Complete Table and Column Mapping

### 1. Clients Table
**Table Name**: `Clients` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `NameFr` (NOT NULL)
- `NameAr`
- `BirthDate`
- `BirthPlace`
- `Gender`
- `Address`
- `NationalId`
- `CreatedAt`
- `UpdatedAt`

### 2. PhoneNumbers Table
**Table Name**: `PhoneNumbers` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ClientUid` (Foreign Key)
- `PhoneNumber`
- `PhoneType`
- `IsPrimary`

### 3. Activities Table
**Table Name**: `Activities` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ClientUid` (Foreign Key)
- `ActivityType`
- `ActivityStatus`
- `ActivityStartDate`
- `CommercialRegister`
- `ActivityLocation`
- `NifNumber`
- `NisNumber`
- `ArtNumber`
- `CpiDaira`
- `CpiWilaya`
- `ActivityUpdateDate`
- `ActivityUpdateNote`
- `CreatedAt`
- `UpdatedAt`

### 4. CommercialActivityCodes Table
**Table Name**: `CommercialActivityCodes` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ActivityUid` (Foreign Key)
- `ActivityCode`

### 5. ProfessionNames Table
**Table Name**: `ProfessionNames` (PascalCase)
**Columns**:
- `ActivityUid` (Primary Key, Foreign Key)
- `ActivityDescription`

### 6. FileCheckStates Table
**Table Name**: `FileCheckStates` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ActivityUid` (Foreign Key)
- `FileCheckType`
- `IsChecked`
- `CheckedDate`

### 7. Notes Table
**Table Name**: `Notes` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ActivityUid` (Foreign Key)
- `Content`
- `Priority`

### 8. G12Check Table
**Table Name**: `G12Check` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ActivityUid` (Foreign Key)
- `Year`

### 9. BisCheck Table
**Table Name**: `BisCheck` (PascalCase)
**Columns**:
- `Uid` (Primary Key)
- `ActivityUid` (Foreign Key)
- `Year`

### 10. UidSequences Table
**Table Name**: `UidSequences` (PascalCase)
**Columns**:
- `EntityType` (Primary Key Part 1)
- `Prefix` (Primary Key Part 2)
- `LastSequence`
- `UpdatedAt`

## Common Snake_Case to PascalCase Mappings

### Table Names
- `clients` → `Clients`
- `phone_numbers` → `PhoneNumbers`
- `activities` → `Activities`
- `commercial_activity_codes` → `CommercialActivityCodes`
- `profession_names` → `ProfessionNames`
- `file_check_states` → `FileCheckStates`
- `notes` → `Notes`
- `g12_check` → `G12Check`
- `bis_check` → `BisCheck`
- `uid_sequences` → `UidSequences`

### Column Names
- `uid` → `Uid`
- `name_fr` → `NameFr`
- `name_ar` → `NameAr`
- `birth_date` → `BirthDate`
- `birth_place` → `BirthPlace`
- `national_id` → `NationalId`
- `created_at` → `CreatedAt`
- `updated_at` → `UpdatedAt`
- `client_uid` → `ClientUid`
- `phone_number` → `PhoneNumber`
- `phone_type` → `PhoneType`
- `is_primary` → `IsPrimary`
- `activity_uid` → `ActivityUid`
- `activity_type` → `ActivityType`
- `activity_status` → `ActivityStatus`
- `activity_start_date` → `ActivityStartDate`
- `commercial_register` → `CommercialRegister`
- `activity_location` → `ActivityLocation`
- `nif_number` → `NifNumber`
- `nis_number` → `NisNumber`
- `art_number` → `ArtNumber`
- `cpi_daira` → `CpiDaira`
- `cpi_wilaya` → `CpiWilaya`
- `activity_update_date` → `ActivityUpdateDate`
- `activity_update_note` → `ActivityUpdateNote`
- `activity_code` → `ActivityCode`
- `activity_description` → `ActivityDescription`
- `file_check_type` → `FileCheckType`
- `is_checked` → `IsChecked`
- `checked_date` → `CheckedDate`
- `entity_type` → `EntityType`
- `last_sequence` → `LastSequence`

## Files Requiring Updates

### Database Services
- `Services/ClientDatabaseService.cs`
- `Services/DatabaseValidationService.cs`
- `Services/DatabaseSchemaValidator.cs`
- `Services/UIDGenerationService.cs` (already partially fixed)

### Entity Models
- `Models/DatabaseEntities.cs`

### ViewModels (if any direct queries)
- Check all ViewModels for direct database queries

### Validation and Migration Services
- Any database migration or validation code

## Priority Update Order
1. **Core Entity Models** - Fix property mappings first
2. **Database Services** - Update all SQL queries
3. **Validation Services** - Fix schema validation logic
4. **Error Handling** - Add schema mismatch detection
5. **Testing** - Validate all operations work correctly
