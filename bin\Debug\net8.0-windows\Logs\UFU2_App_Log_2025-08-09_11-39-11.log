=== UFU2 Application Session Started at 2025-08-09 11:39:11 ===
[2025-08-09 11:39:11.433]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 11:39:11.444]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 11:39:11.453]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 11:39:11.471]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 11:39:11.868]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 11:39:11.876]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 11:39:11.896]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 11:39:11.925]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 11:39:11.997]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 11:39:12.007]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 11:39:12.020]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 11:39:12.027]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 11:39:12.043]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 11:39:12.052]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 11:39:12.057]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 11:39:12.064]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 11:39:12.068]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 11:39:12.073]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 11:39:12.104]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 11:39:12.109]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 11:39:12.117]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 11:39:12.122]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 11:39:12.129]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 11:39:12.137]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 11:39:12.146]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 11:39:12.153]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 11:39:12.159]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 11:39:12.166]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 11:39:12.172]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 11:39:12.177]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 11:39:12.183]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 11:39:12.189]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 11:39:12.195]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 11:39:12.204]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 11:39:12.218]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 11:39:12.233]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 11:39:12.239]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 11:39:12.244]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 11:39:12.264]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 117.09MB working set
[2025-08-09 11:39:12.269]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 11:39:12.274]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 11:39:12.281]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 11:39:12.287]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 11:39:12.292]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 11:39:12.307]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 11:39:12.360]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 11:39:12.372]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 11:39:12.381]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 11:39:12.780]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 11:39:12.787]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 11:39:12.792]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 11:39:12.798]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 11:39:12.808]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_51489795_638903327528061600 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 11:39:12.813]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 11:39:12.820]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:12.825]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 11:39:12.843]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 11:39:12.852]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 11:39:12.858]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 11:39:12.863]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 11:39:12.869]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 11:39:12.877]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 11:39:12.890]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 11:39:12.896]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 11:39:12.902]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 11:39:12.908]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 11:39:12.917]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 11:39:12.923]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 11:39:12.931]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 11:39:12.938]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 11:39:12.945]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 11:39:12.954]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 11:39:12.962]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 11:39:12.974]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 11:39:13.016]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 11:39:13.026]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 11:39:13.037]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 11:39:13.045]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 11:39:13.056]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 11:39:13.066]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 11:39:13.073]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 11:39:13.080]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 11:39:13.086]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 11:39:13.104]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 11:39:13.139]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 11:39:13.157]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 11:39:13.178]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 11:39:13.200]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 11:39:13.234]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 11:39:13.256]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 11:39:13.269]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 11:39:13.274]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 11:39:13.279]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 11:39:13.284]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 11:39:13.291]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 11:39:13.297]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 11:39:13.302]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 11:39:13.307]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 11:39:13.313]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 11:39:13.319]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 11:39:13.328]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 11:39:13.334]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 11:39:13.339]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 11:39:13.345]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 11:39:13.352]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 11:39:13.357]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 11:39:13.363]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 11:39:13.370]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 11:39:13.378]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 11:39:13.383]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 11:39:13.392]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 11:39:13.398]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 11:39:13.404]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 11:39:13.410]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 11:39:13.422]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 11:39:13.428]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 11:39:13.434]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 11:39:13.444]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 11:39:13.450]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 1144ms
[2025-08-09 11:39:13.456]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 11:39:13.466]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 11:39:13.523]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 11:39:13.547]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 11:39:13.571]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 11:39:13.605]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 11:39:13.638]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 11:39:13.857]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 11:39:13.889]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 11:39:13.898]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 11:39:13.907]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 11:39:13.919]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 11:39:13.930]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 11:39:13.940]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 11:39:13.955]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 11:39:13.967]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 11:39:13.988]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:13.989]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:13.989]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:13.998]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 11:39:14.004]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:14.009]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:14.017]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 11:39:14.019]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:39:14.024]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:39:13.989]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:14.030]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 11:39:14.052]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 11:39:14.075]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 11:39:14.086]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 11:39:14.099]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 11:39:14.119]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 11:39:14.125]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 11:39:14.134]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:39:14.140]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 11:39:14.162]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 11:39:14.169]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 11:39:14.181]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 11:39:14.188]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:14.195]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 11:39:14.229]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 11:39:14.235]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 11:39:14.241]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 11:39:14.247]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 11:39:14.254]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 11:39:14.260]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 11:39:14.267]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 11:39:14.276]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 11:39:14.289]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 11:39:14.296]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 11:39:14.303]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 11:39:14.309]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 11:39:14.320]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 11:39:14.326]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 11:39:14.338]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 11:39:14.348]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 11:39:14.354]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 11:39:14.372]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 11:39:14.400]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 11:39:14.406]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 11:39:14.414]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 11:39:14.420]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 11:39:14.428]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 11:39:14.440]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 11:39:14.447]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 11:39:14.454]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 11:39:14.460]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 11:39:14.469]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 11:39:14.476]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:14.482]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 11:39:14.495]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 11:39:14.503]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:14.513]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 11:39:14.521]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 11:39:14.527]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 11:39:14.534]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 11:39:14.540]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 11:39:14.546]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 11:39:14.552]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:39:14.558]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 11:39:14.567]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 11:39:14.573]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 11:39:14.581]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 11:39:14.587]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:14.593]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 11:39:14.601]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 11:39:14.608]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 11:39:14.614]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 11:39:14.620]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 11:39:14.630]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 11:39:14.636]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 11:39:14.644]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 11:39:14.651]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 11:39:14.657]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 11:39:14.664]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 11:39:14.670]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 11:39:14.677]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 11:39:14.683]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 11:39:14.691]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 11:39:14.699]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 11:39:14.706]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 11:39:14.716]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 11:39:14.722]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 11:39:14.728]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:14.736]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 11:39:14.742]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 11:39:14.748]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:14.755]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 11:39:14.762]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 11:39:14.770]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 11:39:14.777]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 11:39:14.784]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 11:39:14.791]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 11:39:14.800]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 11:39:14.805]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 11:39:14.813]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 11:39:14.820]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 11:39:14.826]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 11:39:14.833]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:14.839]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 11:39:14.848]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 11:39:14.854]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 11:39:14.861]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 11:39:14.868]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 11:39:14.876]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 11:39:14.885]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 11:39:14.892]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 11:39:14.899]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 11:39:14.906]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 11:39:14.916]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 11:39:14.925]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 11:39:14.932]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 11:39:14.939]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 11:39:14.945]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 11:39:14.951]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 11:39:14.957]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 11:39:14.963]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:14.969]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 11:39:14.975]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:14.983]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 11:39:14.989]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 11:39:14.998]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 11:39:15.005]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 11:39:15.012]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 11:39:15.019]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 11:39:15.025]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 11:39:15.035]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 11:39:15.041]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 11:39:15.049]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 11:39:15.056]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 11:39:15.063]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 11:39:15.069]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 11:39:15.076]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 11:39:15.083]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 11:39:15.089]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 11:39:15.095]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 11:39:15.101]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 11:39:15.110]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 11:39:15.118]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 11:39:15.126]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 11:39:15.143]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 11:39:15.156]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 11:39:15.164]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 11:39:15.171]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 11:39:15.178]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 11:39:15.188]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 11:39:15.203]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 11:39:15.288]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 11:39:15.305]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 11:39:15.321]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 11:39:15.344]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 11:39:15.381]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 11:39:15.394]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 11:39:15.404]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 11:39:15.417]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 11:39:15.435]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 11:39:15.449]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 11:39:15.461]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 11:39:15.473]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 11:39:15.485]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 11:39:15.493]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 11:39:15.502]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 11:39:15.509]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 11:39:15.516]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 11:39:15.523]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 11:39:15.529]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 11:39:15.536]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 11:39:15.542]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 11:39:15.550]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 11:39:15.560]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:15.580]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 11:39:15.586]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 11:39:15.593]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 11:39:15.600]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 11:39:15.607]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 11:39:15.614]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 11:39:15.621]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 11:39:15.630]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 11:39:15.639]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 11:39:15.647]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 11:39:15.655]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 11:39:15.661]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 11:39:15.668]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 11:39:15.681]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:15.688]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 11:39:15.694]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:15.704]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:15.718]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:15.727]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 11:39:15.734]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:15.742]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:15.752]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 11:39:15.777]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 11:39:15.884]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 11:39:15.898]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:15.936]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 11:39:15.943]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 11:39:15.953]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:15.980]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 11:39:15.988]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 11:39:16.006]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 11:39:16.031]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 11:39:16.039]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 11:39:16.056]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 11:39:16.423]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 11:39:16.454]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 11:39:16.463]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 11:39:16.524]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:16.614]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 11:39:16.621]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:16.627]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 11:39:16.638]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 116ms
[2025-08-09 11:39:16.793]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:39:16.872]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 11:39:16.912]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 11:39:16.922]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 11:39:17.026]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 11:39:17.038]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 11:39:17.045]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 11:39:17.052]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 11:39:17.061]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 11:39:17.069]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:17.080]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 11:39:17.100]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 11:39:17.106]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 11:39:17.113]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 11:39:17.121]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 11:39:17.127]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 11:39:17.134]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 11:39:17.140]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 11:39:17.146]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 11:39:17.152]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 11:39:17.159]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 11:39:17.166]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 11:39:17.173]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 11:39:17.179]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 11:39:17.186]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 11:39:17.193]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 11:39:17.201]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 11:39:17.208]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 11:39:17.218]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 11:39:17.227]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 11:39:17.236]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 11:39:17.269]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.286]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 11:39:17.293]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.300]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.311]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 11:39:17.318]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.324]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.332]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 11:39:17.338]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.345]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.351]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 11:39:17.357]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.365]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.372]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 11:39:17.378]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.385]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.391]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 11:39:17.398]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.404]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 11:39:17.412]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 185ms
[2025-08-09 11:39:17.423]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 11:39:17.455]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 11:39:17.465]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 44ms
[2025-08-09 11:39:17.491]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 11:39:17.509]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 11:39:17.516]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 26ms
[2025-08-09 11:39:17.523]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 11:39:17.532]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:17.540]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 11:39:17.548]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:17.556]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 11:39:17.562]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:17.569]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 11:39:17.574]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 11:39:17.584]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 11:39:17.600]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 11:39:17.606]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 84ms
[2025-08-09 11:39:17.614]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 11:39:17.622]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.633]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 11:39:17.639]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.648]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.656]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 11:39:17.664]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.670]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.677]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 11:39:17.683]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.690]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.697]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 11:39:17.703]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.710]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.717]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 11:39:17.724]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.732]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.739]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 11:39:17.745]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.752]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.758]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 11:39:17.764]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.769]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.775]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 11:39:17.783]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.790]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.797]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 11:39:17.803]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.809]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 11:39:17.817]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 11:39:17.822]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 11:39:17.828]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 11:39:17.834]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 221ms
[2025-08-09 11:39:17.840]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 11:39:17.847]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 11:39:17.853]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 11:39:17.858]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 11:39:17.866]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 11:39:17.871]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 11:39:17.877]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 11:39:17.884]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 11:39:17.891]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 11:39:17.898]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 11:39:17.903]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 11:39:17.910]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 11:39:17.917]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 11:39:17.923]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 11:39:17.931]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 11:39:17.937]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 11:39:17.946]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 11:39:18.100]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 11:39:18.106]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 11:39:18.114]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 11:39:18.121]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 11:39:18.134]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 11:39:18.141]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:18.150]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 11:39:18.156]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:18.165]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 11:39:18.171]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:18.267]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 11:39:18.273]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 11:39:19.103]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 11:39:19.118]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:19.125]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:19.132]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:19.139]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:19.147]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:19.153]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:20.030]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 11:39:23.049]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.060]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.075]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.083]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.089]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.096]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:23.133]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.208]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.243]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.252]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.275]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing MaximizeRestoreCommand
[2025-08-09 11:39:23.290]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.297]  	[DEBUG]		[CustomWindowChromeViewModel]	Current window state: Normal, Target operation: maximize to Maximized (Title: UFU Client Management)
[2025-08-09 11:39:23.306]  	[DEBUG]		[WindowStateManager]	Maximizing window: UFU Client Management (Current state: Normal)
[2025-08-09 11:39:23.324]  	[DEBUG]		[WindowChromeService]	Window state changed to: Maximized for window: UFU Client Management
[2025-08-09 11:39:23.335]  	[DEBUG]		[WindowChromeService]	Window maximized, border adjustments handled by template
[2025-08-09 11:39:23.351]  	[DEBUG]		[MainWindow]	MainWindow state changed to: Maximized, synchronizing ViewModel
[2025-08-09 11:39:23.357]  	[DEBUG]		[CustomWindowChromeViewModel]	IsMaximized changed to: True
[2025-08-09 11:39:23.392]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state changed to: Maximized
[2025-08-09 11:39:23.410]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 11:39:23.419]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Maximized
[2025-08-09 11:39:23.427]  	[DEBUG]		[MainWindow]	ViewModel synchronized with window state: Maximized
[2025-08-09 11:39:23.493]  	[DEBUG]		[WindowStateManager]	Window maximized: UFU Client Management (Normal -> Maximized)
[2025-08-09 11:39:23.519]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 11:39:23.525]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Maximized
[2025-08-09 11:39:23.532]  	[DEBUG]		[CustomWindowChromeViewModel]	MaximizeRestoreCommand executed successfully: maximize operation completed for window: UFU Client Management (Final state: Maximized)
[2025-08-09 11:39:23.630]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 11:39:23.651]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.660]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.669]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.680]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:23.688]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:23.706]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:23.772]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 413.6305ms
[2025-08-09 11:39:23.885]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:24.112]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 2, Batched: 2, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentWindowState(1), IsMaximized(1)
[2025-08-09 11:39:25.919]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2562.0728ms
[2025-08-09 11:39:25.935]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 11:39:29.902]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:29.958]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:29.965]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:29.971]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:29.977]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:29.984]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:29.990]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:30.039]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 6682.1814ms
[2025-08-09 11:39:30.065]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 11:39:34.686]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:39:34.708]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:34.716]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:34.724]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:34.732]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:34.738]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:34.746]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:34.798]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 11:39:34.827]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 11:39:34.870]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 11:39:34.914]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 11:39:34.920]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_16339495_638903327749204884 (BaseViewModel) for NPersonalViewModel
[2025-08-09 11:39:34.926]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 11:39:34.934]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:34.958]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 11:39:35.040]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 11:39:35.105]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_30342446_638903327751056753 (BaseViewModel) for NewClientViewModel
[2025-08-09 11:39:35.113]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 11:39:35.120]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:35.126]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_4646565_638903327751269787 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 11:39:35.134]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 11:39:35.139]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:35.146]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 11:39:35.153]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_41819089_638903327751530929 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 11:39:35.159]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 11:39:35.168]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:35.174]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 11:39:35.180]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 11:39:35.187]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_40827483_638903327751878343 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 11:39:35.193]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 11:39:35.200]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:35.207]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 11:39:35.217]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 11:39:35.225]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 11:39:35.235]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 11:39:35.248]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_31903028_638903327752483194 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 11:39:35.259]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 11:39:35.282]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:39:35.300]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 11:39:35.311]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 11:39:35.322]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 11:39:35.331]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 11:39:35.340]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 11:39:35.353]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 11:39:35.361]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 11:39:35.369]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 11:39:35.376]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 11:39:35.383]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 11:39:35.390]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 11:39:35.398]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 11:39:35.404]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 11:39:35.413]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 541ms (Success: True)
[2025-08-09 11:39:35.419]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 594ms (Success: True)
[2025-08-09 11:39:35.421]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 11:39:35.431]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 11:39:35.432]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 11:39:35.428]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x627 (Height-based width calculation)
[2025-08-09 11:39:35.438]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 11:39:35.443]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 23ms
[2025-08-09 11:39:35.453]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 34ms
[2025-08-09 11:39:35.481]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 11:39:35.620]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 11:39:36.265]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 11:39:36.275]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 11:39:36.454]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 1767.56ms
[2025-08-09 11:39:36.484]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:39:36.520]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1494.2047ms
[2025-08-09 11:39:36.529]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.536]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1431.1174ms
[2025-08-09 11:39:36.542]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.551]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1424.1028ms
[2025-08-09 11:39:36.557]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.567]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1413.9035ms
[2025-08-09 11:39:36.572]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.583]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1395.7595ms
[2025-08-09 11:39:36.589]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.612]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1364.1263ms
[2025-08-09 11:39:36.618]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:39:36.730]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.738]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:36.754]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.760]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:36.769]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.776]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:36.789]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.800]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.807]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.819]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.825]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.834]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.841]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:39:36.903]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.909]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:36.918]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.923]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:39:36.930]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:39:36.936]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:39:37.493]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 2, Time since interaction: 2806.1733ms
[2025-08-09 11:39:37.554]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.585]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2559.7161ms
[2025-08-09 11:39:37.591]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.618]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2512.8334ms
[2025-08-09 11:39:37.626]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.648]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2521.242ms
[2025-08-09 11:39:37.653]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.680]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2527.4898ms
[2025-08-09 11:39:37.686]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.878]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2691.1038ms
[2025-08-09 11:39:37.885]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:37.898]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2650.0659ms
[2025-08-09 11:39:37.904]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 11:39:39.942]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 11:40:17.203]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 318.9MB, System: 30.0%, Pressure: Normal
[2025-08-09 11:40:51.378]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 11:40:51.913]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 11:40:52.430]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 11:40:53.571]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 150.0% increase in response time
[2025-08-09 11:40:54.135]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 150.0% increase in response time
[2025-08-09 11:40:54.693]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 150.0% increase in response time
[2025-08-09 11:40:55.240]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 150.0% increase in response time
[2025-08-09 11:40:59.853]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.861]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.868]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.875]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.881]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.889]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.899]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 11:40:59.926]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:40:59.932]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:40:59.939]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:40:59.945]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:40:59.952]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 11:40:59.958]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:40:59.964]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 11:40:59.971]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 11:40:59.976]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 11:40:59.983]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 11:40:59.990]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_17467122_638903328599904681 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 11:40:59.996]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 11:41:00.001]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 11:41:00.008]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 11:41:00.016]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 11:41:00.028]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 11:41:00.041]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 11:41:00.048]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 11:41:00.054]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 11:41:00.118]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 11:41:00.148]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 11:41:00.427]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 565.9081ms
[2025-08-09 11:41:00.433]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.441]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 572.8629ms
[2025-08-09 11:41:00.448]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.454]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 579.8653ms
[2025-08-09 11:41:00.462]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.469]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 587.7029ms
[2025-08-09 11:41:00.476]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.482]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:00.488]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:41:00.494]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:00.500]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:41:00.507]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:00.512]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:41:00.522]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 633.257ms
[2025-08-09 11:41:00.528]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.538]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 638.2174ms
[2025-08-09 11:41:00.544]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:00.550]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 697.0411ms
[2025-08-09 11:41:00.558]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 11:41:01.003]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 974.6407ms
[2025-08-09 11:41:01.011]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 11:41:01.092]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:01.102]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:41:01.111]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:01.118]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 11:41:01.125]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 11:41:01.131]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 11:41:01.162]  	[DEBUG]		[ConfirmationWindowViewModel]	Primary action executed: الخروج
[2025-08-09 11:41:01.170]  	[INFO]		[ConfirmationWindow]	Close requested with result: True
[2025-08-09 11:41:01.179]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 11:41:01.196]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 11:41:01.206]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:01.212]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 11:41:01.218]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 11:41:01.225]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:01.231]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:41:01.238]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:41:01.245]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_17467122_638903328599904681
[2025-08-09 11:41:01.251]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:01.257]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 11:41:01.264]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 11:41:01.270]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:01.276]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:41:01.283]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 11:41:01.288]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 11:41:01.319]  	[DEBUG]		[MainWindow]	Cleaning up custom window chrome
[2025-08-09 11:41:01.325]  	[DEBUG]		[MainWindow]	Unsubscribed from window StateChanged event
[2025-08-09 11:41:01.333]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 11:41:01.341]  	[DEBUG]		[MainWindow]	WindowChromeService disposed
[2025-08-09 11:41:01.347]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 11:41:01.354]  	[DEBUG]		[CustomWindowChromeViewModel]	All batched notifications flushed
[2025-08-09 11:41:01.360]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:01.368]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 11:41:01.374]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 11:41:01.381]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:01.388]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:41:01.393]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:41:01.400]  	[DEBUG]		[ResourceManager]	Unregistered resource: CustomWindowChromeViewModel_51489795_638903327528061600
[2025-08-09 11:41:01.406]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: CustomWindowChromeViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:01.412]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: CustomWindowChromeViewModel (0 handlers)
[2025-08-09 11:41:01.418]  	[DEBUG]		[CustomWindowChromeViewModel]	Memory management cleanup completed
[2025-08-09 11:41:01.427]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:01.437]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:41:01.443]  	[INFO]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel disposed
[2025-08-09 11:41:01.450]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel disposed
[2025-08-09 11:41:01.456]  	[DEBUG]		[MainWindow]	Custom window chrome cleanup completed
[2025-08-09 11:41:01.463]  	[DEBUG]		[MainWindow]	Cleaning up keyboard support
[2025-08-09 11:41:01.469]  	[DEBUG]		[MainWindow]	Keyboard support cleanup completed
[2025-08-09 11:41:01.476]  	[INFO]		[MainWindow]	Performing application cleanup
[2025-08-09 11:41:01.486]  	[INFO]		[ToastService]	Closing all desktop toast notifications
[2025-08-09 11:41:01.497]  	[DEBUG]		[ToastService]	Toast window closed
[2025-08-09 11:41:01.504]  	[INFO]		[ToastService]	All toast notifications closed
[2025-08-09 11:41:01.513]  	[INFO]		[MainWindow]	Toast notifications closed
[2025-08-09 11:41:01.521]  	[INFO]		[MainWindow]	Application cleanup completed
[2025-08-09 11:41:01.546]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.553]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.594]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.606]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.644]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.652]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 11:41:01.670]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 11:41:01.681]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 11:41:01.695]  	[INFO]		[App]	UFU2 Application shutting down
[2025-08-09 11:41:01.701]  	[DEBUG]		[App]	Application runtime: 0.0s
[2025-08-09 11:41:01.708]  	[DEBUG]		[App]	Performance metrics - Debug log calls: 0, Total logging overhead: 0ms
[2025-08-09 11:41:01.716]  	[DEBUG]		[ServiceLocator]	Disposing registered services
[2025-08-09 11:41:01.723]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 11:41:01.733]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 11:41:01.739]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 11:41:01.745]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 11:41:01.752]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler disposed
[2025-08-09 11:41:01.758]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryPressureHandler
[2025-08-09 11:41:01.764]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService disposed
[2025-08-09 11:41:01.770]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheMonitoringService
[2025-08-09 11:41:01.777]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService disposed
[2025-08-09 11:41:01.783]  	[DEBUG]		[ServiceLocator]	Disposed service: CacheCoordinatorService
[2025-08-09 11:41:01.789]  	[DEBUG]		[ServiceLocator]	Disposed service: DuplicateClientDetectionService
[2025-08-09 11:41:01.796]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService disposed
[2025-08-09 11:41:01.802]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientFolderManagementService
[2025-08-09 11:41:01.809]  	[INFO]		[FileCheckBusinessRuleService]	FileCheckBusinessRuleService disposed. Final cache stats - Hit ratio: 0.0%, Total lookups: 8
[2025-08-09 11:41:01.816]  	[DEBUG]		[ServiceLocator]	Disposed service: FileCheckBusinessRuleService
[2025-08-09 11:41:01.822]  	[DEBUG]		[ServiceLocator]	Disposed service: WordFrequencySearchService
[2025-08-09 11:41:01.829]  	[INFO]		[CpiLocationService]	CpiLocationService disposed. Final cache stats - Wilaya hit ratio: 50.0%, Daira hit ratio: 0.0%, Search hit ratio: 0.0%
[2025-08-09 11:41:01.835]  	[DEBUG]		[ServiceLocator]	Disposed service: CpiLocationService
[2025-08-09 11:41:01.846]  	[INFO]		[CraftTypeBaseService]	CraftTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 0.0%
[2025-08-09 11:41:01.852]  	[DEBUG]		[ServiceLocator]	Disposed service: CraftTypeBaseService
[2025-08-09 11:41:01.860]  	[INFO]		[ActivityTypeBaseService]	ActivityTypeBaseService disposed. Final cache stats - Search hit ratio: 0.0%, Data hit ratio: 66.7%
[2025-08-09 11:41:01.867]  	[DEBUG]		[ServiceLocator]	Disposed service: ActivityTypeBaseService
[2025-08-09 11:41:01.873]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService disposed
[2025-08-09 11:41:01.880]  	[DEBUG]		[DatabaseService]	DatabaseService and connection pool disposed
[2025-08-09 11:41:01.886]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService disposed
[2025-08-09 11:41:01.893]  	[DEBUG]		[ServiceLocator]	Disposed service: EnhancedDatabaseService
[2025-08-09 11:41:01.901]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabasePerformanceMonitoringService
[2025-08-09 11:41:01.913]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService disposed
[2025-08-09 11:41:01.921]  	[DEBUG]		[ServiceLocator]	Disposed service: ClientDatabaseService
[2025-08-09 11:41:01.929]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService disposed
[2025-08-09 11:41:01.947]  	[DEBUG]		[ServiceLocator]	Disposed service: ArchiveDatabaseService
[2025-08-09 11:41:01.955]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService disposed
[2025-08-09 11:41:01.962]  	[DEBUG]		[ServiceLocator]	Disposed service: UIDGenerationService
[2025-08-09 11:41:01.980]  	[DEBUG]		[ServiceLocator]	Disposed service: DatabaseService
[2025-08-09 11:41:01.988]  	[INFO]		[MemoryLeakDetectionService]	Disposing MemoryLeakDetectionService
[2025-08-09 11:41:02.022]  	[INFO]		[ResourceManager]	Generated memory leak report: 6 alive resources, 0 dead resources
[2025-08-09 11:41:02.043]  	[INFO]		[MemoryLeakDetectionService]	Memory leak detection completed. Potential leaks: 2
[2025-08-09 11:41:02.049]  	[INFO]		[MemoryLeakDetectionService]	Final memory leak report: 2 potential leaks detected
[2025-08-09 11:41:02.055]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService disposed. Final stats: 1 snapshots, 0 leaks detected, 0 alerts generated
[2025-08-09 11:41:02.062]  	[DEBUG]		[ServiceLocator]	Disposed service: MemoryLeakDetectionService
[2025-08-09 11:41:02.073]  	[INFO]		[WeakEventManager]	Disposing WeakEventManager and cleaning up all event subscriptions
[2025-08-09 11:41:02.081]  	[INFO]		[WeakEventManager]	Forced cleanup of weak event references completed
[2025-08-09 11:41:02.087]  	[INFO]		[WeakEventManager]	WeakEventManager disposed. Final stats: 0 subscriptions, 0 unsubscriptions, 1 cleanups
[2025-08-09 11:41:02.093]  	[DEBUG]		[ServiceLocator]	Disposed service: WeakEventManager
[2025-08-09 11:41:02.100]  	[INFO]		[ResourceManager]	Disposing ResourceManager and cleaning up all tracked resources
[2025-08-09 11:41:02.107]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 11:41:02.115]  	[DEBUG]		[NotesManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 11:41:02.124]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 11:41:02.132]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 11:41:02.138]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.144]  	[DEBUG]		[NotesManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 11:41:02.149]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.155]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.161]  	[DEBUG]		[NotesManagementViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 11:41:02.167]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.172]  	[DEBUG]		[NotesManagementViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 11:41:02.178]  	[DEBUG]		[NewClientViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 11:41:02.185]  	[DEBUG]		[NewClientViewModel]	Processed 1 High priority property notifications
[2025-08-09 11:41:02.191]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.196]  	[DEBUG]		[NotesManagementViewModel]	Processed 1 High priority property notifications
[2025-08-09 11:41:02.202]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.208]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.214]  	[DEBUG]		[NotesManagementViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 11:41:02.220]  	[DEBUG]		[NotesManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.225]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.234]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 11:41:02.239]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.246]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.252]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.258]  	[DEBUG]		[ResourceManager]	Unregistered resource: NotesManagementViewModel_31903028_638903327752483194
[2025-08-09 11:41:02.264]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NotesManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.270]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NotesManagementViewModel (0 handlers)
[2025-08-09 11:41:02.285]  	[DEBUG]		[NotesManagementViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.294]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.300]  	[INFO]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.307]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:41:02.314]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.319]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:41:02.325]  	[DEBUG]		[PersonalInformationViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.331]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.337]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 11:41:02.346]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.353]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.359]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:41:02.366]  	[DEBUG]		[ResourceManager]	Unregistered resource: PersonalInformationViewModel_4646565_638903327751269787
[2025-08-09 11:41:02.372]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: PersonalInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.378]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: PersonalInformationViewModel (0 handlers)
[2025-08-09 11:41:02.384]  	[DEBUG]		[PersonalInformationViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.391]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.397]  	[INFO]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:41:02.404]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:41:02.411]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.417]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:41:02.423]  	[DEBUG]		[ContactInformationViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.429]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.435]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 11:41:02.441]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.447]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.455]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:41:02.461]  	[DEBUG]		[ResourceManager]	Unregistered resource: ContactInformationViewModel_41819089_638903327751530929
[2025-08-09 11:41:02.467]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ContactInformationViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.473]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ContactInformationViewModel (0 handlers)
[2025-08-09 11:41:02.479]  	[DEBUG]		[ContactInformationViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.494]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.500]  	[INFO]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:41:02.506]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:41:02.513]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.519]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:41:02.524]  	[DEBUG]		[ActivityManagementViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.530]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.536]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 11:41:02.541]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.547]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.552]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:41:02.559]  	[DEBUG]		[ResourceManager]	Unregistered resource: ActivityManagementViewModel_40827483_638903327751878343
[2025-08-09 11:41:02.565]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ActivityManagementViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.571]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ActivityManagementViewModel (0 handlers)
[2025-08-09 11:41:02.577]  	[DEBUG]		[ActivityManagementViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.582]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.588]  	[INFO]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:41:02.594]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.599]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:41:02.605]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.610]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel disposed
[2025-08-09 11:41:02.617]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel disposed
[2025-08-09 11:41:02.625]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel disposed
[2025-08-09 11:41:02.631]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel disposed
[2025-08-09 11:41:02.636]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:41:02.642]  	[DEBUG]		[NewClientViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.648]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.654]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 11:41:02.659]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.665]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.670]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:41:02.676]  	[DEBUG]		[ResourceManager]	Unregistered resource: NewClientViewModel_30342446_638903327751056753
[2025-08-09 11:41:02.681]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NewClientViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.689]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NewClientViewModel (0 handlers)
[2025-08-09 11:41:02.695]  	[DEBUG]		[NewClientViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.702]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.714]  	[INFO]		[NewClientViewModel]	NewClientViewModel disposed
[2025-08-09 11:41:02.722]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.729]  	[DEBUG]		[NPersonalViewModel]	All batched notifications flushed
[2025-08-09 11:41:02.735]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.741]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 11:41:02.746]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.752]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.757]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 11:41:02.762]  	[DEBUG]		[ResourceManager]	Unregistered resource: NPersonalViewModel_16339495_638903327749204884
[2025-08-09 11:41:02.768]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: NPersonalViewModel (1 resources, 0 event subscriptions)
[2025-08-09 11:41:02.773]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: NPersonalViewModel (0 handlers)
[2025-08-09 11:41:02.779]  	[DEBUG]		[NPersonalViewModel]	Memory management cleanup completed
[2025-08-09 11:41:02.784]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 11:41:02.789]  	[INFO]		[NPersonalViewModel]	NPersonalViewModel disposed
[2025-08-09 11:41:02.796]  	[INFO]		[ResourceManager]	ResourceManager disposed. Final stats: 8 tracked, 11 disposed, 1 cleanups
[2025-08-09 11:41:02.802]  	[DEBUG]		[ServiceLocator]	Disposed service: ResourceManager
[2025-08-09 11:41:02.808]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService disposed
[2025-08-09 11:41:02.814]  	[DEBUG]		[ServiceLocator]	Disposed service: PerformanceDashboardService
[2025-08-09 11:41:02.820]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService disposed
[2025-08-09 11:41:02.827]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewLoadingMonitoringService
[2025-08-09 11:41:02.834]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService disposed
[2025-08-09 11:41:02.839]  	[DEBUG]		[ServiceLocator]	Disposed service: ViewMemoryOptimizationService
[2025-08-09 11:41:02.897]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService disposed
[2025-08-09 11:41:02.907]  	[DEBUG]		[ServiceLocator]	Disposed service: BackgroundViewInitializationService
[2025-08-09 11:41:02.919]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring stopped
[2025-08-09 11:41:02.925]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService disposed successfully
[2025-08-09 11:41:02.933]  	[DEBUG]		[ServiceLocator]	Disposed service: UIResponsivenessMonitoringService
[2025-08-09 11:41:02.944]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService disposed successfully
[2025-08-09 11:41:02.950]  	[DEBUG]		[ServiceLocator]	Disposed service: DispatcherOptimizationService
[2025-08-09 11:41:02.956]  	[DEBUG]		[WindowChromeService]	WindowChromeService disposed
[2025-08-09 11:41:02.962]  	[DEBUG]		[ServiceLocator]	Disposed service: WindowChromeService
[2025-08-09 11:41:02.970]  	[INFO]		[ValidationService]	ValidationService disposed. Final cache stats - Hit ratio: 0.0%, Total validations: 2
[2025-08-09 11:41:02.977]  	[DEBUG]		[ServiceLocator]	Disposed service: ValidationService
[2025-08-09 11:41:02.984]  	[DEBUG]		[ServiceLocator]	Service disposal completed
[2025-08-09 11:41:02.991]  	[INFO]		[App]	UFU2 Application shutdown completed
=== UFU2 Application Session Ended at 2025-08-09 11:41:02 ===
