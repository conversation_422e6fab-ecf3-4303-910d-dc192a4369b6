# UFU2 Theme Management Guide

## Overview

This guide provides comprehensive documentation for UFU2's theme management system, covering MaterialDesign theme implementation, Light/Dark mode switching, DynamicResource usage patterns, and Arabic RTL theme considerations. The theme system is built on top of MaterialDesign for WPF and integrates seamlessly with UFU2's MVVM architecture and ServiceLocator pattern.

## Table of Contents

1. [Theme Architecture Overview](#theme-architecture-overview)
2. [MaterialDesign Theme Implementation](#materialdesign-theme-implementation)
3. [DynamicResource Usage Patterns](#dynamicresource-usage-patterns)
4. [Custom Style Creation Guidelines](#custom-style-creation-guidelines)
5. [Theme Switching Mechanisms](#theme-switching-mechanisms)
6. [Arabic RTL Theme Considerations](#arabic-rtl-theme-considerations)
7. [Component Theme Integration](#component-theme-integration)
8. [Performance Considerations](#performance-considerations)
9. [ServiceLocator Integration](#servicelocator-integration)

---

## Theme Architecture Overview

UFU2's theme system is built around a centralized `ThemeManager` service that manages dynamic switching between Light and Dark themes while maintaining MaterialDesign compliance and Arabic RTL support.

### Core Components

```mermaid
graph TB
    subgraph "Theme Management Architecture"
        TM[ThemeManager] --> MDT[MaterialDesign BundledTheme]
        TM --> DT[DarkTheme.xaml]
        TM --> LT[LightTheme.xaml]
        
        TM --> SL[ServiceLocator]
        TM --> EM[ErrorManager]
        TM --> LS[LoggingService]
        
        subgraph "UI Components"
            VM[ViewModels] --> TM
            UC[UserControls] --> DR[DynamicResource]
            WC[Window Chrome] --> DR
        end
        
        subgraph "Resource Flow"
            DR --> DT
            DR --> LT
            MDT --> DR
        end
    end
```

### Key Features

- **Dynamic Theme Switching**: Real-time switching between Light and Dark themes
- **MaterialDesign Integration**: Full compatibility with MaterialDesign for WPF
- **Arabic RTL Support**: Proper text flow and layout adjustments for Arabic interface
- **Custom Color System**: UFU2-specific color palette with semantic naming
- **Performance Optimized**: Efficient resource loading and UI updates

---

## MaterialDesign Theme Implementation

### Theme Initialization

The ThemeManager is initialized during application startup in `App.xaml.cs`:

<augment_code_snippet path="App.xaml.cs" mode="EXCERPT">
````csharp
// Initialize ThemeManager with Dark theme as default
var themeInitialized = await ThemeManager.InitializeAsync(ApplicationTheme.Dark);

if (!themeInitialized)
{
    LoggingService.LogWarning("ThemeManager initialization failed, continuing with default theme", "App");
}
else
{
    LoggingService.LogDebug("ThemeManager initialized successfully", "App");
}
````
</augment_code_snippet>

### BundledTheme Configuration

The MaterialDesign BundledTheme is configured in `App.xaml`:

<augment_code_snippet path="App.xaml" mode="EXCERPT">
````xml
<!--  MaterialDesign theme - will be managed dynamically by ThemeManager  -->
<materialDesign:BundledTheme
    x:Name="MaterialDesignBundledTheme"
    BaseTheme="Dark"
    PrimaryColor="DeepPurple"
    SecondaryColor="Purple" />
<ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign3.Defaults.xaml" />
````
</augment_code_snippet>

### Custom Color Integration

The ThemeManager applies UFU2's custom colors to MaterialDesign components:

<augment_code_snippet path="Services/ThemeManager.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Updates MaterialDesign theme with custom colors from UFU2 theme files
/// </summary>
private static void ApplyCustomMaterialDesignColors(ApplicationTheme theme)
{
    // Get the appropriate theme dictionary
    var themeDict = theme == ApplicationTheme.Dark ? _darkThemeDict : _lightThemeDict;
    
    // Apply custom primary and secondary colors based on theme
    if (theme == ApplicationTheme.Dark)
    {
        // Dark Theme: Use warm orange colors
        var primaryColor = GetColorFromTheme(themeDict, "PrimaryColor");
        var secondaryColor = GetColorFromTheme(themeDict, "SecondaryColor");
        
        if (primaryColor.HasValue)
        {
            _materialDesignTheme.PrimaryColor = GetClosestMaterialDesignColor(primaryColor.Value, true);
        }
    }
}
````
</augment_code_snippet>

---

## DynamicResource Usage Patterns

### Basic DynamicResource Binding

Use DynamicResource for all theme-aware properties to enable real-time theme switching:

```xml
<!-- Correct: Theme-aware background -->
<Border Background="{DynamicResource SurfaceBrush}">
    <TextBlock Foreground="{DynamicResource TextFillColorPrimaryBrush}" 
               Text="Theme-aware content" />
</Border>

<!-- Incorrect: Static color that won't change with theme -->
<Border Background="Gray">
    <TextBlock Foreground="White" Text="Static content" />
</Border>
```

### Window Chrome Theme Integration

Custom window chrome uses DynamicResource for theme-aware styling:

<augment_code_snippet path="Resources/Styles/CustomWindowChromeStyle.xaml" mode="EXCERPT">
````xml
<Setter Property="Background" Value="{DynamicResource TitleBarBackground}" />
<Setter Property="BorderBrush" Value="{DynamicResource WindowBorderBrush}" />
<Setter Property="BorderThickness" Value="1" />
````
</augment_code_snippet>

### Button Theme Integration

Buttons use DynamicResource for hover and press states:

<augment_code_snippet path="Resources/Styles/ButtonsStyles.xaml" mode="EXCERPT">
````xml
<Style.Triggers>
    <Trigger Property="IsMouseOver" Value="True">
        <Setter Property="Background" Value="{DynamicResource WindowButtonHoverBackgroud}" />
    </Trigger>
    <Trigger Property="IsPressed" Value="True">
        <Setter Property="Background" Value="{DynamicResource WindowButtonPressBackgroud}" />
    </Trigger>
</Style.Triggers>
````
</augment_code_snippet>

### Color Resource Naming Convention

UFU2 uses semantic color naming based on purpose, not appearance:

```xml
<!-- Surface Colors -->
<SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource Surface}" />
<SolidColorBrush x:Key="SurfaceDimBrush" Color="{StaticResource SurfaceDim}" />
<SolidColorBrush x:Key="SurfaceBrightBrush" Color="{StaticResource SurfaceBright}" />

<!-- Text Colors -->
<SolidColorBrush x:Key="TextFillColorPrimaryBrush" Color="{StaticResource TextFillColorPrimary}" />
<SolidColorBrush x:Key="TextFillColorSecondaryBrush" Color="{StaticResource TextFillColorSecondary}" />

<!-- Status Colors -->
<SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource Success}" />
<SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource Error}" />
<SolidColorBrush x:Key="WarningBrush" Color="{StaticResource Warning}" />
```

---

## Custom Style Creation Guidelines

### Creating Theme-Aware Styles

When creating custom styles, always use DynamicResource bindings and follow UFU2's naming conventions:

```xml
<Style x:Key="CustomCardStyle" TargetType="Border">
    <Setter Property="Background" Value="{DynamicResource SurfaceBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource SurfaceDimBrush}" />
    <Setter Property="BorderThickness" Value="1" />
    <Setter Property="CornerRadius" Value="8" />
    <Setter Property="Padding" Value="16" />
    
    <!-- MaterialDesign elevation effect -->
    <Setter Property="Effect">
        <Setter.Value>
            <DropShadowEffect Color="{DynamicResource ShadowColor}" 
                              BlurRadius="8" 
                              ShadowDepth="2" 
                              Opacity="0.3" />
        </Setter.Value>
    </Setter>
</Style>
```

### UserControl Theme Integration

UserControls should use DynamicResource and provide theme-aware default styles:

<augment_code_snippet path="Views/UserControls/SaveCancelButtonsControl.xaml.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Sets the default styles if they haven't been explicitly set.
/// </summary>
public override void OnApplyTemplate()
{
    base.OnApplyTemplate();
    
    // Set default styles if not already set
    if (SaveButtonStyle == null)
    {
        SaveButtonStyle = (Style)Application.Current.FindResource("PrimaryButtonStyle");
    }
    
    if (CancelButtonStyle == null)
    {
        CancelButtonStyle = (Style)Application.Current.FindResource("SecondaryButtonStyle");
    }
}
````
</augment_code_snippet>

### Style Inheritance Patterns

Build upon existing MaterialDesign styles while adding UFU2-specific customizations:

```xml
<!-- Extend MaterialDesign button style -->
<Style x:Key="UFU2PrimaryButtonStyle" 
       BasedOn="{StaticResource MaterialDesignRaisedButton}" 
       TargetType="Button">
    <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
    <Setter Property="Foreground" Value="{DynamicResource OnPrimaryBrush}" />
    <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8" />
    <Setter Property="Height" Value="40" />
    <Setter Property="Padding" Value="24,8" />
</Style>
```

---

## Theme Switching Mechanisms

### Programmatic Theme Switching

Use the ThemeManager service to switch themes programmatically:

```csharp
public class SettingsViewModel : BaseViewModel
{
    private readonly ThemeManager _themeManager;
    
    public SettingsViewModel()
    {
        // ThemeManager is static, no ServiceLocator needed
        SwitchThemeCommand = new RelayCommand<ApplicationTheme>(async (theme) => await SwitchThemeAsync(theme));
    }
    
    public RelayCommand<ApplicationTheme> SwitchThemeCommand { get; }
    
    private async Task SwitchThemeAsync(ApplicationTheme theme)
    {
        try
        {
            var success = await ThemeManager.SwitchThemeAsync(theme);
            if (success)
            {
                ErrorManager.ShowUserSuccessToast("تم تغيير المظهر بنجاح");
                LoggingService.LogInfo($"Theme switched to {theme}", "SettingsViewModel");
            }
            else
            {
                ErrorManager.ShowUserErrorToast("فشل في تغيير المظهر", "خطأ في النظام", "ThemeSwitch");
            }
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في تغيير المظهر");
        }
    }
}
```

### Theme Change Event Handling

ViewModels can respond to theme changes by subscribing to the ThemeChanged event:

<augment_code_snippet path="ViewModels/CustomWindowChromeViewModel.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Handles theme change events from ThemeManager
/// </summary>
private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
{
    try
    {
        LoggingService.LogDebug($"Theme changed from {e.PreviousTheme} to {e.NewTheme}", "CustomWindowChromeViewModel");
        
        // Update theme properties on UI thread
        Application.Current.Dispatcher.Invoke(() =>
        {
            UpdateThemeProperties(e.NewTheme);
        });
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error handling theme change: {ex.Message}", "CustomWindowChromeViewModel");
    }
}
````
</augment_code_snippet>

### UI Theme Toggle Implementation

Create theme toggle buttons with proper Arabic tooltips:

```xml
<ToggleButton x:Name="ThemeToggleButton"
              Style="{DynamicResource MaterialDesignActionToggleButton}"
              ToolTip="تبديل المظهر الفاتح/الداكن"
              IsChecked="{Binding IsDarkTheme, Mode=TwoWay}"
              Command="{Binding ToggleThemeCommand}">
    <materialDesign:PackIcon Kind="{Binding ThemeIcon}" />
</ToggleButton>
```

---

## Arabic RTL Theme Considerations

### Text Flow and Alignment

Ensure proper text alignment for Arabic RTL layouts in both themes:

```xml
<!-- RTL-aware text alignment -->
<TextBlock Text="{Binding ArabicText}"
           TextAlignment="Right"
           FlowDirection="RightToLeft"
           Foreground="{DynamicResource TextFillColorPrimaryBrush}" />

<!-- RTL-aware layout with proper margins -->
<StackPanel Orientation="Horizontal" 
            FlowDirection="RightToLeft">
    <materialDesign:PackIcon Kind="Account" 
                             Margin="0,0,8,0"
                             Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
    <TextBlock Text="معلومات العميل" 
               Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
</StackPanel>
```

### Icon Positioning for RTL

Adjust icon positioning and transformations for RTL layouts:

<augment_code_snippet path="Resources/Styles/ButtonsStyles.xaml" mode="EXCERPT">
````xml
<materialDesign:PackIcon Kind="WindowMinimize">
    <materialDesign:PackIcon.RenderTransform>
        <ScaleTransform ScaleX="-1" />
    </materialDesign:PackIcon.RenderTransform>
</materialDesign:PackIcon>
````
</augment_code_snippet>

### Window Chrome RTL Support

Custom window chrome includes proper RTL support with Arabic tooltips:

<augment_code_snippet path="Resources/Styles/CustomWindowChromeStyle.xaml" mode="EXCERPT">
````xml
<Grid x:Name="TitleBar"
      behaviors:TitleBarBehavior.EnableDoubleClickMaximize="True"
      behaviors:TitleBarBehavior.EnableDrag="True"
      AutomationProperties.HelpText="اسحب لتحريك النافذة، انقر مرتين لتكبير أو استعادة النافذة"
      AutomationProperties.Name="شريط عنوان النافذة"
      Background="{DynamicResource TitleBarBackground}">
````
</augment_code_snippet>

### RTL Color Considerations

Some colors may need adjustment for RTL layouts to maintain proper visual hierarchy:

```xml
<!-- Dark theme RTL considerations -->
<Color x:Key="RTL_TextEmphasis">#FFFFFF</Color>
<Color x:Key="RTL_TextSecondary">#C5FFFFFF</Color>

<!-- Light theme RTL considerations -->
<Color x:Key="RTL_TextEmphasis">#000000</Color>
<Color x:Key="RTL_TextSecondary">#87000000</Color>
```

---

## Component Theme Integration

### MaterialDesign Component Theming

UFU2 components integrate with MaterialDesign theming while maintaining custom styling:

```xml
<!-- Card with MaterialDesign and UFU2 theming -->
<materialDesign:Card Background="{DynamicResource SurfaceBrush}"
                     Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                     materialDesign:ElevationAssist.Elevation="Dp4">
    <StackPanel Margin="16">
        <TextBlock Text="عنوان البطاقة"
                   Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                   Foreground="{DynamicResource TextFillColorPrimaryBrush}" />
        <TextBlock Text="محتوى البطاقة"
                   Style="{DynamicResource MaterialDesignBody2TextBlock}"
                   Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
    </StackPanel>
</materialDesign:Card>
```

### Input Controls Theme Integration

Input controls use both MaterialDesign and UFU2 theming:

```xml
<materialDesign:PackIcon Kind="Account" 
                         Foreground="{DynamicResource TextFillColorSecondaryBrush}" />
<TextBox materialDesign:HintAssist.Hint="اسم العميل"
         materialDesign:HintAssist.IsFloating="True"
         Background="{DynamicResource SurfaceBrush}"
         Foreground="{DynamicResource TextFillColorPrimaryBrush}"
         BorderBrush="{DynamicResource PrimaryBrush}" />
```

### Dialog Theme Integration

Dialogs inherit theme properties and maintain consistency:

```csharp
public class ThemedDialogViewModel : BaseViewModel
{
    public ThemedDialogViewModel()
    {
        // Subscribe to theme changes for dialog updates
        ThemeManager.ThemeChanged += OnThemeChanged;
    }
    
    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        // Update dialog-specific theme properties
        Application.Current.Dispatcher.Invoke(() =>
        {
            OnPropertyChanged(nameof(DialogBackground));
            OnPropertyChanged(nameof(DialogForeground));
        });
    }
    
    public Brush DialogBackground => 
        Application.Current.FindResource("SurfaceBrush") as Brush ?? Brushes.White;
    
    public Brush DialogForeground => 
        Application.Current.FindResource("TextFillColorPrimaryBrush") as Brush ?? Brushes.Black;
}
```

---

## Performance Considerations

### Resource Loading Optimization

The ThemeManager optimizes resource loading by caching theme dictionaries:

<augment_code_snippet path="Services/ThemeManager.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Loads and caches theme resource dictionaries for efficient switching
/// </summary>
private static void LoadThemeResourceDictionaries()
{
    try
    {
        // Load and cache Dark theme dictionary
        _darkThemeDict = new ResourceDictionary
        {
            Source = new Uri("/Resources/Themes/DarkTheme.xaml", UriKind.Relative)
        };
        
        // Load and cache Light theme dictionary  
        _lightThemeDict = new ResourceDictionary
        {
            Source = new Uri("/Resources/Themes/LightTheme.xaml", UriKind.Relative)
        };
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error loading theme dictionaries: {ex.Message}", "ThemeManager");
    }
}
````
</augment_code_snippet>

### UI Update Optimization

Theme switching uses efficient UI updates with proper threading:

<augment_code_snippet path="Services/ThemeManager.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Forces a refresh of all UI elements to apply theme changes
/// </summary>
public static void RefreshUI()
{
    try
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            // Force refresh of all windows
            foreach (Window window in Application.Current.Windows)
            {
                window.InvalidateVisual();
                window.UpdateLayout();
            }
        });
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error refreshing UI: {ex.Message}", "ThemeManager");
    }
}
````
</augment_code_snippet>

### Memory Management

Theme resources are managed efficiently to prevent memory leaks:

```csharp
// Proper resource cleanup in ViewModels
public class ThemeAwareViewModel : BaseViewModel, IDisposable
{
    public ThemeAwareViewModel()
    {
        ThemeManager.ThemeChanged += OnThemeChanged;
    }
    
    protected override void OnDispose()
    {
        // Unsubscribe from theme events to prevent memory leaks
        ThemeManager.ThemeChanged -= OnThemeChanged;
        base.OnDispose();
    }
    
    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        // Handle theme change
    }
}
```

---

## ServiceLocator Integration

### Theme Service Access

While ThemeManager is static, other theme-related services can be accessed through ServiceLocator:

```csharp
public class ThemeAwareViewModel : BaseViewModel
{
    private readonly LoggingService _loggingService;
    private readonly ErrorManager _errorManager;
    
    public ThemeAwareViewModel()
    {
        // Access services through ServiceLocator
        _loggingService = ServiceLocator.GetService<LoggingService>();
        _errorManager = ServiceLocator.GetService<ErrorManager>();
        
        // Initialize theme-aware properties
        InitializeThemeProperties();
    }
    
    private void InitializeThemeProperties()
    {
        try
        {
            // Get current theme and apply properties
            var currentTheme = ThemeManager.CurrentTheme;
            ApplyThemeProperties(currentTheme);
            
            _loggingService.LogDebug($"Theme properties initialized for {currentTheme}", "ThemeAwareViewModel");
        }
        catch (Exception ex)
        {
            _errorManager.HandleError(ex, "خطأ في تهيئة خصائص المظهر");
        }
    }
}
```

### Service Integration Pattern

Follow UFU2's established patterns for service integration:

```csharp
public class CustomThemeService
{
    private readonly LoggingService _loggingService;
    private readonly ErrorManager _errorManager;
    
    public CustomThemeService()
    {
        _loggingService = ServiceLocator.GetService<LoggingService>();
        _errorManager = ServiceLocator.GetService<ErrorManager>();
    }
    
    public async Task<bool> ApplyCustomThemeAsync(string themeName)
    {
        return await _errorManager.ExecuteWithErrorHandlingAsync(async () =>
        {
            _loggingService.LogInfo($"Applying custom theme: {themeName}", "CustomThemeService");
            
            // Custom theme application logic
            await ThemeManager.SwitchThemeAsync(ApplicationTheme.Dark);
            
            _loggingService.LogInfo($"Custom theme applied successfully: {themeName}", "CustomThemeService");
            
        }, "CustomThemeService", "ApplyTheme", $"خطأ في تطبيق المظهر {themeName}", LogLevel.Error);
    }
}
```

---

## Best Practices Summary

### Do's ✅

- **Always use DynamicResource** for theme-aware properties
- **Follow semantic naming** for colors (Surface, Primary, etc.)
- **Subscribe to ThemeChanged events** for dynamic updates
- **Use proper Arabic RTL layouts** with FlowDirection
- **Integrate with MaterialDesign** components properly
- **Cache theme resources** for performance
- **Use ServiceLocator** for service dependencies
- **Handle theme errors gracefully** with Arabic messages

### Don'ts ❌

- **Don't use static colors** that won't change with themes
- **Don't hardcode appearance-based names** (BlueColor, etc.)
- **Don't forget to unsubscribe** from theme events
- **Don't ignore RTL considerations** for Arabic interface
- **Don't bypass MaterialDesign** theming system
- **Don't create memory leaks** with event subscriptions
- **Don't access services directly** without ServiceLocator
- **Don't show English error messages** to users

This comprehensive guide enables developers to create consistent, theme-aware components that integrate seamlessly with UFU2's established patterns for MaterialDesign styling, Arabic localization, and MVVM architecture.
