﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F0E3DBE7956858BBF20D4C44FF4D6018C95E9B73"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.MahApps;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using UFU2.Common.Converters;
using UFU2.Models;
using UFU2.Views.UserControls;


namespace UFU2.Views.Dialogs {
    
    
    /// <summary>
    /// AddNotesDialog
    /// </summary>
    public partial class AddNotesDialog : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card MainCard;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NoteContentTextBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PopupBox FlagSelectionPopupBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GreenFlagButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OrangeFlagButton;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RedFlagButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UFU2;component/views/dialogs/addnotesdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 2:
            this.NoteContentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.FlagSelectionPopupBox = ((MaterialDesignThemes.Wpf.PopupBox)(target));
            return;
            case 4:
            this.GreenFlagButton = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
            this.GreenFlagButton.Click += new System.Windows.RoutedEventHandler(this.GreenFlagButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.OrangeFlagButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
            this.OrangeFlagButton.Click += new System.Windows.RoutedEventHandler(this.OrangeFlagButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RedFlagButton = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\..\..\Views\Dialogs\AddNotesDialog.xaml"
            this.RedFlagButton.Click += new System.Windows.RoutedEventHandler(this.RedFlagButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

