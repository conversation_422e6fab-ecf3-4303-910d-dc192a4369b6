# الأسئلة الشائعة - تنسيقات التاريخ المرنة
# FAQ - Flexible Date Formats

---

## الأسئلة العامة | General Questions

### ❓ ما هي تنسيقات التاريخ المدعومة في UFU2؟
**What date formats are supported in UFU2?**

**الجواب:** يدعم UFU2 ثلاثة تنسيقات رئيسية:
- `dd/MM/yyyy` للتواريخ الكاملة (مثل: 15/03/2023)
- `xx/xx/yyyy` للتواريخ الجزئية مع السنة المعروفة (مثل: xx/xx/2023)
- `xx/xx/xxxx` للتواريخ غير المعروفة (مثل: xx/xx/xxxx)

**Answer:** UFU2 supports three main formats:
- `dd/MM/yyyy` for complete dates (e.g., 15/03/2023)
- `xx/xx/yyyy` for partial dates with known year (e.g., xx/xx/2023)
- `xx/xx/xxxx` for unknown dates (e.g., xx/xx/xxxx)

### ❓ لماذا تم إضافة هذه التنسيقات المرنة؟
**Why were these flexible formats added?**

**الجواب:** لتلبية احتياجات العمل الواقعية حيث قد تكون بعض معلومات التاريخ غير متوفرة أو ناقصة، خاصة في:
- الوثائق القديمة
- حالات العملاء الذين لا يتذكرون التاريخ الدقيق
- الأنشطة التجارية التي بدأت في فترات غير محددة

**Answer:** To meet real-world business needs where some date information may be unavailable or incomplete, especially for:
- Old documents
- Clients who don't remember exact dates
- Business activities that started in unspecified periods

---

## أسئلة الاستخدام | Usage Questions

### ❓ كيف أدخل تاريخاً جزئياً؟
**How do I enter a partial date?**

**الجواب:** يمكنك إدخال التاريخ الجزئي بعدة طرق:
- اكتب `xx2023` للحصول على `xx/xx/2023`
- اكتب `xx032023` للحصول على `xx/03/2023`
- استخدم `x` أو `X` للأجزاء غير المعروفة

**Answer:** You can enter partial dates in several ways:
- Type `xx2023` to get `xx/xx/2023`
- Type `xx032023` to get `xx/03/2023`
- Use `x` or `X` for unknown parts

### ❓ هل يمكنني استخدام تنسيق مختلف مثل yyyy/MM/dd؟
**Can I use a different format like yyyy/MM/dd?**

**الجواب:** لا، النظام يدعم فقط التنسيقات الثلاثة المحددة. هذا يضمن الاتساق في قاعدة البيانات وسهولة المعالجة.

**Answer:** No, the system supports only the three specified formats. This ensures database consistency and processing ease.

### ❓ ماذا لو كنت أعرف الشهر واليوم لكن لا أعرف السنة؟
**What if I know the month and day but not the year?**

**الجواب:** في هذه الحالة، استخدم التنسيق `dd/MM/xxxx` (مثل: 15/03/xxxx). النظام سيقبل هذا التنسيق.

**Answer:** In this case, use the format `dd/MM/xxxx` (e.g., 15/03/xxxx). The system will accept this format.

---

## أسئلة التنسيق التلقائي | Auto-Formatting Questions

### ❓ لماذا لا يتم تنسيق التاريخ تلقائياً أحياناً؟
**Why doesn't the date format automatically sometimes?**

**الجواب:** التنسيق التلقائي يعمل في الحالات التالية:
- عند إدخال 8 أرقام متتالية (للتاريخ الكامل)
- عند إدخال `xx` متبوعة بأرقام
- عند فقدان التركيز على الحقل (LostFocus)

تأكد من استخدام الأحرف المسموحة فقط: 0-9, x, X, /

**Answer:** Auto-formatting works in these cases:
- When entering 8 consecutive numbers (for complete date)
- When entering `xx` followed by numbers
- When the field loses focus (LostFocus)

Ensure you use only allowed characters: 0-9, x, X, /

### ❓ هل يمكنني كتابة الشرطات المائلة يدوياً؟
**Can I type slashes manually?**

**الجواب:** نعم، يمكنك كتابة الشرطات المائلة، لكن النظام سيضيفها تلقائياً في معظم الحالات. الأفضل ترك النظام يتولى التنسيق.

**Answer:** Yes, you can type slashes, but the system will add them automatically in most cases. It's better to let the system handle formatting.

---

## أسئلة رسائل الخطأ | Error Message Questions

### ❓ ما معنى "تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/xxxx"؟
**What does "Date format must be DD/MM/YYYY or xx/xx/xxxx" mean?**

**الجواب:** هذه الرسالة تظهر عندما:
- تستخدم تنسيقاً غير مدعوم
- تدخل أحرفاً غير مسموحة
- التنسيق لا يتطابق مع الأنماط المقبولة

**الحل:** استخدم أحد التنسيقات الثلاثة المدعومة.

**Answer:** This message appears when:
- You use an unsupported format
- You enter disallowed characters
- The format doesn't match accepted patterns

**Solution:** Use one of the three supported formats.

### ❓ لماذا أحصل على "التاريخ المدخل غير صحيح"؟
**Why do I get "Invalid date entered"?**

**الجواب:** هذا يحدث عندما:
- اليوم خارج النطاق (أكبر من 31)
- الشهر خارج النطاق (أكبر من 12)
- التاريخ غير موجود (مثل 30/02/2023)

**الحل:** تأكد من صحة اليوم (1-31) والشهر (1-12).

**Answer:** This happens when:
- Day is out of range (greater than 31)
- Month is out of range (greater than 12)
- Date doesn't exist (like 30/02/2023)

**Solution:** Ensure valid day (1-31) and month (1-12).

---

## أسئلة المجالات المحددة | Field-Specific Questions

### ❓ في أي حقول يمكنني استخدام التنسيقات المرنة؟
**In which fields can I use flexible formats?**

**الجواب:** التنسيقات المرنة متاحة في:
- **تاريخ الميلاد** (نموذج المعلومات الشخصية)
- **تاريخ بداية النشاط** (جميع أنواع الأنشطة)
- **تاريخ تحديث النشاط** (نافذة تحديث الحالة)

**Answer:** Flexible formats are available in:
- **Birth Date** (Personal Information form)
- **Activity Start Date** (All activity types)
- **Activity Update Date** (Status Update dialog)

### ❓ هل تؤثر التنسيقات المرنة على التقارير؟
**Do flexible formats affect reports?**

**الجواب:** لا، التقارير تتعامل مع جميع التنسيقات بشكل صحيح. التواريخ غير المعروفة تظهر كما هي (xx/xx/xxxx) في التقارير.

**Answer:** No, reports handle all formats correctly. Unknown dates appear as-is (xx/xx/xxxx) in reports.

---

## أسئلة تقنية | Technical Questions

### ❓ ما هو نطاق السنوات المقبولة؟
**What is the accepted year range?**

**الجواب:** النظام يقبل السنوات من 1900 إلى 2100. هذا النطاق يغطي جميع الاحتياجات التجارية المعقولة.

**Answer:** The system accepts years from 1900 to 2100. This range covers all reasonable business needs.

### ❓ كيف يتم حفظ التواريخ في قاعدة البيانات؟
**How are dates stored in the database?**

**الجواب:** التواريخ تُحفظ كنص (TEXT) بالتنسيق الذي أدخله المستخدم. هذا يحافظ على المعلومات الأصلية ويدعم التنسيقات المرنة.

**Answer:** Dates are stored as text (TEXT) in the format entered by the user. This preserves original information and supports flexible formats.

### ❓ هل يمكن البحث في التواريخ الجزئية؟
**Can I search partial dates?**

**الجواب:** نعم، يمكن البحث في التواريخ الجزئية. مثلاً، البحث عن "2023" سيجد جميع التواريخ التي تحتوي على هذه السنة.

**Answer:** Yes, you can search partial dates. For example, searching for "2023" will find all dates containing this year.

---

## أسئلة الأداء | Performance Questions

### ❓ هل التنسيقات المرنة تؤثر على سرعة النظام؟
**Do flexible formats affect system speed?**

**الجواب:** لا، التأثير على الأداء ضئيل جداً. النظام محسّن للتعامل مع جميع التنسيقات بكفاءة.

**Answer:** No, the performance impact is minimal. The system is optimized to handle all formats efficiently.

### ❓ هل هناك حد أقصى لعدد التواريخ المرنة؟
**Is there a maximum limit for flexible dates?**

**الجواب:** لا، لا يوجد حد أقصى. يمكن استخدام التنسيقات المرنة في أي عدد من السجلات.

**Answer:** No, there's no maximum limit. Flexible formats can be used in any number of records.

---

## أسئلة الترقية والتحديث | Upgrade & Update Questions

### ❓ ماذا يحدث للتواريخ الموجودة عند الترقية؟
**What happens to existing dates during upgrade?**

**الجواب:** جميع التواريخ الموجودة تبقى كما هي. الميزات الجديدة تُطبق فقط على التواريخ الجديدة أو المُحدثة.

**Answer:** All existing dates remain unchanged. New features apply only to new or updated dates.

### ❓ هل يمكن تحويل التواريخ القديمة للتنسيق الجديد؟
**Can old dates be converted to the new format?**

**الجواب:** نعم، يمكن تعديل أي تاريخ موجود لاستخدام التنسيقات الجديدة من خلال تحرير السجل.

**Answer:** Yes, any existing date can be modified to use new formats by editing the record.

---

## الدعم والمساعدة | Support & Help

### ❓ من أتصل إذا واجهت مشكلة؟
**Who do I contact if I encounter an issue?**

**الجواب:** يمكنك التواصل مع فريق الدعم الفني:
- **الهاتف:** +213-XXX-XXXX
- **البريد الإلكتروني:** <EMAIL>
- **ساعات العمل:** الأحد - الخميس، 8:00 - 17:00

**Answer:** You can contact the technical support team:
- **Phone:** +213-XXX-XXXX
- **Email:** <EMAIL>
- **Business Hours:** Sunday - Thursday, 8:00 AM - 5:00 PM

### ❓ أين أجد المزيد من المعلومات؟
**Where can I find more information?**

**الجواب:** راجع الوثائق التالية:
- [دليل المستخدم الكامل](UserTraining-FlexibleDateFormats.md)
- [البطاقة المرجعية السريعة](QuickReference-DateFormats.md)
- [شرائح التدريب](Training-DateFormats-Slides.md)

**Answer:** Refer to these documents:
- [Complete User Guide](UserTraining-FlexibleDateFormats.md)
- [Quick Reference Card](QuickReference-DateFormats.md)
- [Training Slides](Training-DateFormats-Slides.md)

---

*الأسئلة الشائعة - نظام UFU2 | FAQ - UFU2 System*
*آخر تحديث: يناير 2025 | Last Updated: January 2025*
