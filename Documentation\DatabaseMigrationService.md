# DatabaseMigrationService Documentation

## Overview

The `DatabaseMigrationService` handles database schema versioning and migration for UFU2. It ensures safe schema updates, maintains data integrity during upgrades, and provides rollback capabilities for failed migrations.

## What the Code Does

The service manages:
- **Schema Version Tracking**: Maintains current database schema version
- **Migration Execution**: Applies schema changes safely with transaction wrapping
- **Rollback Support**: Provides recovery mechanisms for failed migrations
- **Initial Schema Creation**: Sets up complete database schema for new installations
- **Migration Validation**: Verifies schema integrity before and after migrations

## How It's Used

### Basic Migration Operations

```csharp
// Get service from ServiceLocator
var migrationService = ServiceLocator.GetService<DatabaseMigrationService>();

// Check if migrations are needed
bool needsMigration = await migrationService.NeedsMigrationAsync();

if (needsMigration)
{
    // Apply all pending migrations
    await migrationService.MigrateToLatestAsync();
}

// Get current schema version
int currentVersion = await migrationService.GetCurrentVersionAsync();
```

### Application Startup Integration

```csharp
public async Task InitializeApplicationAsync()
{
    try
    {
        var migrationService = ServiceLocator.GetService<DatabaseMigrationService>();
        
        // Ensure database is at latest version
        await migrationService.EnsureLatestSchemaAsync();
        
        LoggingService.LogInformation("Database migration completed successfully");
    }
    catch (DatabaseMigrationException ex)
    {
        ErrorManager.HandleError(ex, "خطأ في ترقية قاعدة البيانات");
        throw;
    }
}
```

### Manual Migration with Progress Tracking

```csharp
public async Task PerformMigrationWithProgressAsync()
{
    var migrationService = ServiceLocator.GetService<DatabaseMigrationService>();
    
    // Get pending migrations
    var pendingMigrations = await migrationService.GetPendingMigrationsAsync();
    
    foreach (var migration in pendingMigrations)
    {
        try
        {
            LoggingService.LogInformation($"Applying migration: {migration.Name}");
            
            await migrationService.ApplyMigrationAsync(migration);
            
            LoggingService.LogInformation($"Migration completed: {migration.Name}");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Migration failed: {migration.Name}", ex);
            
            // Attempt rollback
            await migrationService.RollbackMigrationAsync(migration);
            throw;
        }
    }
}
```

### Schema Validation

```csharp
public async Task ValidateSchemaIntegrityAsync()
{
    var migrationService = ServiceLocator.GetService<DatabaseMigrationService>();
    
    // Validate current schema
    var validationResult = await migrationService.ValidateSchemaAsync();
    
    if (!validationResult.IsValid)
    {
        foreach (var error in validationResult.Errors)
        {
            LoggingService.LogWarning($"Schema validation error: {error}");
        }
        
        // Optionally trigger schema repair
        await migrationService.RepairSchemaAsync();
    }
}
```

## Integration with UFU2 Architecture

The service integrates with UFU2's core patterns:

- **ServiceLocator Pattern**: Registered for dependency injection
- **Error Handling**: Uses `ErrorManager` with Arabic error messages
- **Logging**: Comprehensive logging through `LoggingService`
- **Database Access**: Built on `DatabaseService` with transaction support
- **Application Lifecycle**: Integrates with app startup process

## Performance Considerations

- **Transaction Wrapping**: All migrations wrapped in transactions for atomicity
- **Backup Creation**: Automatic backup before major schema changes
- **Incremental Updates**: Applies only necessary migrations, not full schema recreation
- **Validation Caching**: Caches schema validation results to avoid repeated checks
- **Connection Management**: Efficient database connection handling
- **Progress Tracking**: Provides feedback for long-running migrations

## Mermaid Diagram

```mermaid
graph TB
    subgraph "Application Startup"
        App[Application Start] --> Check[Check Migration Status]
        Check --> Decision{Needs Migration?}
        Decision -->|Yes| Migrate[Apply Migrations]
        Decision -->|No| Ready[Application Ready]
        Migrate --> Ready
    end
    
    subgraph "Migration Service Components"
        DMS[DatabaseMigrationService] --> VT[Version Tracking]
        DMS --> ME[Migration Executor]
        DMS --> RB[Rollback Handler]
        DMS --> SV[Schema Validator]
    end
    
    subgraph "Migration Process"
        ME --> BT[Begin Transaction]
        BT --> AM[Apply Migration]
        AM --> UV[Update Version]
        UV --> CT[Commit Transaction]
        
        AM -->|Error| RT[Rollback Transaction]
        RT --> EH[Error Handling]
    end
    
    subgraph "Database Layer"
        DMS --> DS[DatabaseService]
        DS --> SQLite[(SQLite Database)]
        SQLite --> ST[schema_version Table]
        SQLite --> Tables[Application Tables]
    end
    
    style DMS fill:#e1f5fe
    style SQLite fill:#f3e5f5
    style Ready fill:#e8f5e8
    style EH fill:#ffebee
```

## Migration Flow Diagram

```mermaid
sequenceDiagram
    participant App as Application
    participant DMS as DatabaseMigrationService
    participant DS as DatabaseService
    participant DB as SQLite Database
    participant LS as LoggingService
    participant EM as ErrorManager

    App->>DMS: EnsureLatestSchemaAsync()
    DMS->>DS: GetCurrentVersionAsync()
    DS->>DB: SELECT version FROM schema_version
    DB->>DS: Return current version
    DS->>DMS: Return version number
    
    DMS->>DMS: Compare with target version
    
    alt Migration Needed
        DMS->>LS: LogInformation("Starting migration")
        DMS->>DS: BeginTransactionAsync()
        
        loop For each pending migration
            DMS->>LS: LogInformation("Applying migration {name}")
            DMS->>DS: ExecuteAsync(migration SQL)
            DS->>DB: Execute migration commands
            DMS->>DS: UpdateVersionAsync(new version)
            DS->>DB: UPDATE schema_version
        end
        
        DMS->>DS: CommitTransactionAsync()
        DMS->>LS: LogInformation("Migration completed")
        DMS->>App: Success
        
    else Migration Error
        DMS->>DS: RollbackTransactionAsync()
        DMS->>LS: LogError("Migration failed")
        DMS->>EM: HandleError(ex, "خطأ في ترقية قاعدة البيانات")
        DMS->>App: Throw Exception
        
    else No Migration Needed
        DMS->>LS: LogInformation("Database up to date")
        DMS->>App: Success
    end
```

## Migration State Machine

```mermaid
stateDiagram-v2
    [*] --> Checking: Check Version
    Checking --> UpToDate: Version Current
    Checking --> PendingMigrations: Version Behind
    
    PendingMigrations --> Migrating: Start Migration
    Migrating --> ApplyingMigration: For Each Migration
    ApplyingMigration --> UpdatingVersion: Migration Success
    UpdatingVersion --> ApplyingMigration: More Migrations
    UpdatingVersion --> Completed: All Done
    
    ApplyingMigration --> RollingBack: Migration Error
    RollingBack --> Failed: Rollback Complete
    
    UpToDate --> [*]: Ready
    Completed --> [*]: Ready
    Failed --> [*]: Error State
```