# UFU2 Documentation Maintenance Rules for AI Agents

## Overview

This document establishes mandatory rules for AI agents working on the UFU2 project to ensure that all code changes automatically trigger corresponding documentation updates. These rules prevent documentation drift and maintain the integrity of UFU2's comprehensive documentation suite.

## Table of Contents

1. [Mandatory Documentation Updates](#mandatory-documentation-updates)
2. [Documentation Scope Requirements](#documentation-scope-requirements)
3. [Quality Standards](#quality-standards)
4. [Update Triggers](#update-triggers)
5. [Validation Requirements](#validation-requirements)
6. [Integration with Development Workflow](#integration-with-development-workflow)
7. [Documentation File Mapping](#documentation-file-mapping)
8. [Enforcement Procedures](#enforcement-procedures)

---

## Mandatory Documentation Updates

### Rule 1: Service Layer Changes

**MANDATORY**: Any changes to service classes MUST trigger documentation updates within the same session.

#### Triggers:
- New service classes created
- New public methods added to existing services
- Method signatures modified (parameters, return types, exceptions)
- Service registration changes in ServiceLocator
- Business logic modifications affecting service behavior

#### Required Actions:
```
1. Update APIReference.md with new/modified method documentation
2. Update ArchitectureOverview.md if new service dependencies are introduced
3. Update ServiceIntegrationGuide.md with usage patterns
4. Run security scan on all new code examples
5. Verify Arabic error messages for user-facing methods
```

### Rule 2: UI Component Changes

**MANDATORY**: Any UI-related changes MUST trigger UIArchitectureGuide.md updates.

#### Triggers:
- New ViewModels created
- New UserControls or custom controls added
- XAML styling patterns modified
- MaterialDesign component usage changes
- Arabic RTL layout modifications
- Data binding patterns updated

#### Required Actions:
```
1. Update UIArchitectureGuide.md with new component documentation
2. Add complete XAML and ViewModel code examples
3. Verify MaterialDesign DynamicResource usage
4. Test Arabic RTL layout compliance
5. Update component reuse patterns section
```

### Rule 3: Database Schema Changes

**MANDATORY**: Database modifications MUST trigger multiple documentation updates.

#### Triggers:
- Schema version increments
- New tables or columns added
- Index modifications
- Business rule changes affecting data validation
- UID generation pattern changes

#### Required Actions:
```
1. Update ArchitectureOverview.md database architecture section
2. Update DeploymentGuide.md migration procedures
3. Update APIReference.md for affected service methods
4. Update BusinessProcessGuide.md for business rule changes
5. Document performance impact in PerformanceGuide.md
```

### Rule 4: Performance Optimizations

**MANDATORY**: Performance changes MUST update PerformanceGuide.md with measurable results.

#### Triggers:
- Database query optimizations
- UI performance improvements
- Memory management enhancements
- Logging optimizations
- Image processing improvements

#### Required Actions:
```
1. Update PerformanceGuide.md with before/after metrics
2. Document implementation steps and code examples
3. Update performance baselines and targets
4. Add monitoring and profiling guidance
5. Update implementation priority rankings
```

---

## Documentation Scope Requirements

### Service Layer Changes → Documentation Mapping

| Change Type | Primary Documentation | Secondary Documentation |
|-------------|----------------------|------------------------|
| **New Service Class** | APIReference.md | ArchitectureOverview.md, ServiceIntegrationGuide.md |
| **Method Signature Change** | APIReference.md | Any guides using the method |
| **Business Logic Change** | APIReference.md, BusinessProcessGuide.md | ArchitectureOverview.md |
| **Error Handling Change** | APIReference.md | All guides with error examples |
| **Validation Rule Change** | APIReference.md, BusinessProcessGuide.md | UIArchitectureGuide.md |

### UI Layer Changes → Documentation Mapping

| Change Type | Primary Documentation | Secondary Documentation |
|-------------|----------------------|------------------------|
| **New ViewModel** | UIArchitectureGuide.md | ArchitectureOverview.md |
| **New UserControl** | UIArchitectureGuide.md | Component reuse guides |
| **XAML Pattern Change** | UIArchitectureGuide.md | ThemeManagementGuide.md |
| **MaterialDesign Update** | UIArchitectureGuide.md, ThemeManagementGuide.md | All UI examples |
| **RTL Layout Change** | UIArchitectureGuide.md | All Arabic UI examples |

### Infrastructure Changes → Documentation Mapping

| Change Type | Primary Documentation | Secondary Documentation |
|-------------|----------------------|------------------------|
| **Database Schema** | ArchitectureOverview.md, DeploymentGuide.md | APIReference.md, BusinessProcessGuide.md |
| **Configuration Change** | DeploymentGuide.md | ArchitectureOverview.md |
| **Build Process Change** | DeploymentGuide.md | README.md |
| **Performance Optimization** | PerformanceGuide.md | ArchitectureOverview.md |
| **Security Enhancement** | All affected documentation | Security validation required |

---

## Quality Standards

### Rule 5: Code Example Standards

**MANDATORY**: All code examples MUST meet UFU2 architectural standards.

#### Requirements:
```csharp
// ✅ REQUIRED: All ViewModels must inherit from BaseViewModel
public class ExampleViewModel : BaseViewModel
{
    // ✅ REQUIRED: ServiceLocator dependency injection
    private readonly ExampleService _exampleService;
    
    public ExampleViewModel()
    {
        _exampleService = ServiceLocator.GetService<ExampleService>();
    }
    
    // ✅ REQUIRED: RelayCommand usage for UI interactions
    public ICommand ExampleCommand { get; }
    
    // ✅ REQUIRED: Proper property change notification
    private string _exampleProperty;
    public string ExampleProperty
    {
        get => _exampleProperty;
        set => SetProperty(ref _exampleProperty, value);
    }
}
```

#### XAML Standards:
```xaml
<!-- ✅ REQUIRED: MaterialDesign styling with DynamicResource -->
<Button Content="مثال"
        Style="{DynamicResource MaterialDesignRaisedButton}"
        Background="{DynamicResource PrimaryBrush}"
        Command="{Binding ExampleCommand}" />

<!-- ✅ REQUIRED: Arabic RTL support -->
<UserControl FlowDirection="RightToLeft">
    <!-- ✅ REQUIRED: Arabic text with proper alignment -->
    <TextBlock Text="النص العربي" 
               HorizontalAlignment="Right" />
</UserControl>
```

### Rule 6: Arabic Localization Standards

**MANDATORY**: All user-facing content MUST include Arabic translations.

#### Requirements:
- Error messages: Arabic primary, English technical details in logs
- UI labels and hints: Arabic text in all examples
- Toast notifications: Arabic messages with proper RTL layout
- Documentation descriptions: Arabic context where applicable

#### Example:
```csharp
// ✅ REQUIRED: Arabic error messages for users
ErrorManager.ShowUserErrorToast("فشل في حفظ البيانات", "خطأ في الحفظ", "ServiceName");

// ✅ REQUIRED: Technical details in English for logs
LoggingService.LogError($"Database operation failed: {ex.Message}", "ServiceName");
```

### Rule 7: Security Validation Standards

**MANDATORY**: All code examples MUST pass security validation.

#### Requirements:
```
1. Run security_check_semgrep on all new code examples
2. Ensure parameterized queries for all database operations
3. Validate input sanitization patterns
4. Verify error information security (no sensitive data exposure)
5. Check for proper resource disposal patterns
```

#### Example:
```csharp
// ✅ REQUIRED: Parameterized queries only
var result = await connection.QueryAsync<Client>(
    "SELECT * FROM Clients WHERE UID = @UID", 
    new { UID = clientUID }
);

// ❌ FORBIDDEN: Dynamic SQL construction
// var sql = $"SELECT * FROM Clients WHERE UID = '{clientUID}'"; // NEVER DO THIS
```

---

## Update Triggers

### Rule 8: Immediate Update Triggers

**MANDATORY**: The following changes MUST trigger immediate documentation updates:

#### Service Layer Triggers:
```
✅ New service class created → Update APIReference.md + ArchitectureOverview.md
✅ Public method added → Update APIReference.md with complete documentation
✅ Method signature changed → Update APIReference.md + all usage examples
✅ Exception handling modified → Update APIReference.md error documentation
✅ Business rule changed → Update BusinessProcessGuide.md + APIReference.md
```

#### UI Layer Triggers:
```
✅ New ViewModel created → Update UIArchitectureGuide.md with complete example
✅ New UserControl added → Update UIArchitectureGuide.md reuse patterns
✅ XAML styling changed → Update UIArchitectureGuide.md + ThemeManagementGuide.md
✅ Data binding pattern modified → Update UIArchitectureGuide.md examples
✅ Arabic RTL layout changed → Update all affected UI documentation
```

#### Infrastructure Triggers:
```
✅ Database schema modified → Update ArchitectureOverview.md + DeploymentGuide.md
✅ Configuration option added → Update DeploymentGuide.md
✅ Build process changed → Update DeploymentGuide.md + README.md
✅ Performance optimization → Update PerformanceGuide.md with metrics
✅ Security enhancement → Update all affected documentation
```

### Rule 9: Cascading Update Requirements

**MANDATORY**: Changes that affect multiple components MUST trigger cascading updates.

#### Example: New Service with UI Integration
```
1. Create service class → Update APIReference.md
2. Add ViewModel integration → Update UIArchitectureGuide.md  
3. Update ServiceLocator → Update ArchitectureOverview.md
4. Add error handling → Update all error handling examples
5. Performance impact → Update PerformanceGuide.md if applicable
```

---

## Validation Requirements

### Rule 10: Documentation Accuracy Validation

**MANDATORY**: After any code changes, validate documentation accuracy.

#### Validation Steps:
```
1. Verify all code examples compile and run correctly
2. Check that method signatures match actual implementation
3. Validate that error messages are current and in Arabic
4. Ensure MaterialDesign patterns are up-to-date
5. Test Arabic RTL layout examples
6. Run security scans on all new code examples
7. Verify performance metrics are current and achievable
```

#### Automated Validation:
```csharp
// Example validation script structure
public class DocumentationValidator
{
    public async Task<ValidationResult> ValidateDocumentationAsync()
    {
        var results = new List<ValidationIssue>();
        
        // Validate API documentation matches actual services
        results.AddRange(await ValidateAPIReferenceAsync());
        
        // Validate UI examples compile
        results.AddRange(await ValidateUIExamplesAsync());
        
        // Validate security compliance
        results.AddRange(await ValidateSecurityComplianceAsync());
        
        // Validate Arabic localization
        results.AddRange(await ValidateArabicLocalizationAsync());
        
        return new ValidationResult(results);
    }
}
```

### Rule 11: Cross-Reference Validation

**MANDATORY**: Ensure cross-references between documentation files remain valid.

#### Validation Requirements:
```
1. Verify internal links work correctly
2. Check that referenced code examples exist
3. Ensure consistent terminology across all documents
4. Validate that architectural diagrams reflect current structure
5. Check that performance metrics are consistent across guides
```

---

## Integration with Development Workflow

### Rule 12: Documentation-First Development

**MANDATORY**: For significant changes, update documentation before or during implementation.

#### Workflow:
```
1. Plan Change → Update relevant documentation with planned changes
2. Implement Change → Verify documentation accuracy during implementation
3. Test Change → Include documentation validation in testing
4. Review Change → Ensure documentation review is part of code review
5. Deploy Change → Verify documentation is current before deployment
```

### Rule 13: Continuous Documentation Validation

**MANDATORY**: Implement continuous validation of documentation accuracy.

#### Requirements:
```
1. Automated validation scripts run with each significant change
2. Documentation accuracy metrics tracked over time
3. Regular audits of documentation completeness
4. Performance metrics validation against documented baselines
5. Security compliance verification for all examples
```

### Rule 14: Documentation Versioning

**MANDATORY**: Maintain documentation versioning aligned with code changes.

#### Requirements:
```
1. Document major architectural changes with version notes
2. Maintain backward compatibility notes for API changes
3. Track performance improvement history
4. Document migration paths for breaking changes
5. Maintain change logs for documentation updates
```

---

## Documentation File Mapping

### Rule 15: Specific File Update Requirements

**MANDATORY**: Use this mapping to determine which documentation files require updates for specific changes.

#### ArchitectureOverview.md Updates Required For:
```
✅ New service classes or major architectural components
✅ Changes to ServiceLocator dependency injection patterns
✅ Database schema modifications or migration procedures
✅ New technology stack components or version updates
✅ Security architecture changes or enhancements
✅ Performance architecture modifications
✅ Deployment architecture changes
✅ New architectural decision records (ADRs)
```

#### UIArchitectureGuide.md Updates Required For:
```
✅ New ViewModel classes or BaseViewModel modifications
✅ New UserControl components or reusable UI patterns
✅ MaterialDesign component usage changes
✅ Arabic RTL layout pattern modifications
✅ Data binding pattern changes or optimizations
✅ Command implementation pattern updates
✅ Validation and error handling UI pattern changes
✅ XAML resource management pattern modifications
```

#### APIReference.md Updates Required For:
```
✅ New service methods or classes
✅ Method signature changes (parameters, return types, exceptions)
✅ Error handling modifications affecting user-facing methods
✅ Business logic changes affecting service behavior
✅ Validation rule changes in service layer
✅ New integration patterns between services
✅ Performance optimizations affecting service methods
✅ Security enhancements in service implementations
```

#### DeploymentGuide.md Updates Required For:
```
✅ System requirement changes
✅ Installation procedure modifications
✅ Configuration file changes or new environment variables
✅ Database deployment procedure changes
✅ Build process or packaging modifications
✅ Security configuration changes
✅ Performance optimization deployment steps
✅ Troubleshooting procedure updates
```

#### PerformanceGuide.md Updates Required For:
```
✅ Performance optimization implementations
✅ Benchmark result updates with before/after metrics
✅ New performance monitoring capabilities
✅ Database performance tuning modifications
✅ UI performance enhancement implementations
✅ Memory management optimization changes
✅ Logging performance optimization updates
✅ Performance regression test results
```

#### ThemeManagementGuide.md Updates Required For:
```
✅ MaterialDesign theme integration changes
✅ Custom styling pattern modifications
✅ Color scheme or theme switching updates
✅ DynamicResource usage pattern changes
✅ Arabic RTL theme considerations
✅ Theme performance optimization changes
```

#### BusinessProcessGuide.md Updates Required For:
```
✅ Algerian business rule modifications
✅ File check business rule changes
✅ UID generation pattern updates
✅ Validation rule modifications
✅ Workflow process changes
✅ Arabic localization updates for business processes
```

#### ServiceIntegrationGuide.md Updates Required For:
```
✅ ServiceLocator registration pattern changes
✅ Cross-service communication pattern updates
✅ Error handling integration modifications
✅ Logging integration pattern changes
✅ Performance monitoring integration updates
✅ Security integration pattern modifications
```

#### ImageManagementGuide.md Updates Required For:
```
✅ Image processing algorithm changes
✅ WYSIWYG functionality modifications
✅ Coordinate transformation updates
✅ Performance optimization implementations
✅ UI interaction pattern changes
✅ Error handling improvements
```

### Rule 16: Documentation Dependency Matrix

**MANDATORY**: When updating primary documentation, check dependent documentation for required updates.

#### High-Impact Changes (Affect Multiple Documents):
```
ServiceLocator Changes:
├── ArchitectureOverview.md (Primary)
├── APIReference.md (Service registration examples)
├── UIArchitectureGuide.md (ViewModel dependency injection)
└── ServiceIntegrationGuide.md (Integration patterns)

BaseViewModel Changes:
├── UIArchitectureGuide.md (Primary)
├── ArchitectureOverview.md (MVVM architecture)
├── PerformanceGuide.md (PropertyChanged optimization)
└── All ViewModel examples across documentation

Database Schema Changes:
├── ArchitectureOverview.md (Database architecture)
├── DeploymentGuide.md (Migration procedures)
├── APIReference.md (Affected service methods)
├── BusinessProcessGuide.md (Business rule impacts)
└── PerformanceGuide.md (Performance implications)

MaterialDesign Updates:
├── UIArchitectureGuide.md (Primary)
├── ThemeManagementGuide.md (Theme integration)
├── ArchitectureOverview.md (UI framework section)
└── All XAML examples across documentation
```

---

## Enforcement Procedures

### Rule 17: Mandatory Validation Checklist

**MANDATORY**: Complete this checklist before considering any code change complete.

#### Pre-Implementation Checklist:
```
□ Identified all documentation files requiring updates
□ Planned documentation changes alongside code changes
□ Verified Arabic localization requirements
□ Checked security implications for documentation examples
□ Reviewed performance impact documentation needs
```

#### During Implementation Checklist:
```
□ Updated documentation concurrently with code changes
□ Verified code examples compile and run correctly
□ Tested Arabic RTL layout examples
□ Validated MaterialDesign pattern compliance
□ Ensured ServiceLocator pattern consistency
```

#### Post-Implementation Checklist:
```
□ Ran security scans on all new code examples
□ Verified all cross-references and links work
□ Tested performance metrics match documented baselines
□ Validated Arabic error messages are current
□ Confirmed architectural pattern compliance
□ Updated any affected diagrams or flowcharts
```

### Rule 18: Documentation Quality Gates

**MANDATORY**: Documentation must pass these quality gates before code changes are considered complete.

#### Quality Gate 1: Completeness
```
✅ All affected documentation files updated
✅ All new methods/classes documented with examples
✅ All error conditions documented with Arabic messages
✅ All integration patterns documented
✅ All performance impacts documented
```

#### Quality Gate 2: Accuracy
```
✅ Code examples compile without errors
✅ Method signatures match actual implementation
✅ Error messages match actual application behavior
✅ Performance metrics reflect current baselines
✅ Arabic translations are accurate and contextual
```

#### Quality Gate 3: Consistency
```
✅ Architectural patterns consistent across all examples
✅ Terminology consistent across all documentation
✅ Code style consistent with UFU2 standards
✅ Arabic RTL patterns consistent throughout
✅ MaterialDesign usage patterns consistent
```

#### Quality Gate 4: Security
```
✅ All code examples pass security validation
✅ No sensitive information exposed in examples
✅ Parameterized queries used consistently
✅ Input validation patterns demonstrated correctly
✅ Error handling doesn't leak sensitive information
```

### Rule 19: Escalation Procedures

**MANDATORY**: Follow these escalation procedures for documentation maintenance issues.

#### Level 1: Automated Validation Failures
```
Action: Fix immediately within same session
Examples:
- Security scan failures
- Compilation errors in code examples
- Broken cross-references
- Missing Arabic translations
```

#### Level 2: Architectural Inconsistencies
```
Action: Review and align with established patterns
Examples:
- ServiceLocator pattern violations
- MVVM pattern deviations
- MaterialDesign usage inconsistencies
- Performance pattern violations
```

#### Level 3: Major Documentation Gaps
```
Action: Comprehensive documentation review and update
Examples:
- Missing documentation for new major features
- Outdated architectural diagrams
- Inconsistent performance baselines
- Incomplete API coverage
```

### Rule 20: Continuous Improvement

**MANDATORY**: Implement continuous improvement for documentation maintenance.

#### Monthly Reviews:
```
□ Audit documentation completeness
□ Verify performance metrics accuracy
□ Check Arabic localization quality
□ Review security compliance
□ Update architectural diagrams if needed
```

#### Quarterly Assessments:
```
□ Comprehensive cross-reference validation
□ Performance baseline updates
□ Security pattern review
□ Arabic localization audit
□ Documentation usage analytics review
```

#### Annual Overhauls:
```
□ Complete documentation architecture review
□ Technology stack documentation updates
□ Performance optimization strategy review
□ Security compliance comprehensive audit
□ Arabic localization comprehensive review
```

---

## Summary

These documentation maintenance rules ensure that UFU2's comprehensive documentation suite remains current, accurate, and valuable as the codebase evolves. By following these mandatory rules, AI agents can maintain the high quality and consistency of UFU2's documentation while supporting the core business functions of client management, activity tracking, document management, and payment processing.

### Key Principles:
1. **Documentation is Code**: Treat documentation updates with the same rigor as code changes
2. **Arabic-First Localization**: All user-facing content must include proper Arabic translations
3. **Security by Default**: All code examples must pass security validation
4. **Performance Awareness**: Document performance impacts of all changes
5. **Architectural Consistency**: Maintain UFU2's established patterns across all examples

### Success Metrics:
- **Documentation Coverage**: 100% of public APIs documented
- **Example Accuracy**: 100% of code examples compile and run
- **Arabic Localization**: 100% of user-facing content translated
- **Security Compliance**: 100% of examples pass security validation
- **Performance Tracking**: All optimizations documented with metrics

By adhering to these rules, the UFU2 documentation will continue to serve as a comprehensive, accurate, and valuable resource for developers working on the project.
