# UFU2 Architecture Overview

## Table of Contents

1. [System Architecture Diagram](#system-architecture-diagram)
2. [Technology Stack](#technology-stack)
3. [Architectural Patterns](#architectural-patterns)
4. [Data Flow Documentation](#data-flow-documentation)
5. [Security Architecture](#security-architecture)
6. [Performance Architecture](#performance-architecture)
7. [Localization Architecture](#localization-architecture)
8. [Deployment Architecture](#deployment-architecture)

---

## System Architecture Diagram

### Complete System Architecture

```mermaid
graph TB
    subgraph "Presentation Layer (MVVM)"
        subgraph "Views (XAML)"
            V1[MainWindow]
            V2[NewClientView]
            V3[ImageManagementDialog]
            V4[UserControls]
        end
        
        subgraph "ViewModels"
            VM1[BaseViewModel]
            VM2[NewClientViewModel]
            VM3[ImageManagementViewModel]
            VM4[CustomWindowChromeViewModel]
        end
        
        subgraph "Commands"
            C1[RelayCommand]
            C2[RelayCommand&lt;T&gt;]
        end
    end
    
    subgraph "Service Layer"
        subgraph "Core Services"
            SL[ServiceLocator]
            LS[LoggingService]
            EM[ErrorManager]
            TM[ThemeManager]
        end
        
        subgraph "Business Services"
            CDS[ClientDatabaseService]
            UGS[UIDGenerationService]
            CVS[ClientValidationService]
            FCBRS[FileCheckBusinessRuleService]
        end
        
        subgraph "Infrastructure Services"
            DS[DatabaseService]
            DMS[DatabaseMigrationService]
            DPMS[DatabasePerformanceMonitoringService]
            VS[ValidationService]
        end
    end
    
    subgraph "Data Layer"
        subgraph "Database"
            DB[(SQLite Database)]
            SCHEMA[UFU2_Schema.sql]
        end
        
        subgraph "ORM"
            DAPPER[Dapper ORM]
        end
    end
    
    subgraph "UI Framework"
        subgraph "MaterialDesign"
            MD[MaterialDesign Themes]
            MDC[MaterialDesign Colors]
            BT[BundledTheme]
        end
        
        subgraph "WPF Framework"
            WPF[WPF .NET 8.0]
            XAML[XAML Resources]
        end
    end
    
    subgraph "Localization & Theming"
        subgraph "Themes"
            LT[LightTheme.xaml]
            DT[DarkTheme.xaml]
        end
        
        subgraph "RTL Support"
            RTL[Arabic RTL Layout]
            FLOW[FlowDirection Management]
        end
    end
    
    %% Connections
    V1 --> VM1
    V2 --> VM2
    V3 --> VM3
    V4 --> VM4
    
    VM1 --> C1
    VM2 --> C2
    VM3 --> C1
    
    VM1 --> SL
    VM2 --> SL
    VM3 --> SL
    
    SL --> CDS
    SL --> UGS
    SL --> CVS
    SL --> FCBRS
    SL --> DS
    SL --> VS
    
    CDS --> DS
    UGS --> DS
    CVS --> DS
    FCBRS --> DS
    
    DS --> DAPPER
    DAPPER --> DB
    
    DMS --> SCHEMA
    DMS --> DB
    
    TM --> MD
    TM --> LT
    TM --> DT
    
    VM1 --> EM
    VM2 --> LS
    VM3 --> EM
    
    V1 --> WPF
    V2 --> XAML
    V3 --> MD
    
    RTL --> FLOW
    FLOW --> V1
    FLOW --> V2
```

### Service Dependency Architecture

```mermaid
graph LR
    subgraph "Dependency Injection Container"
        SL[ServiceLocator]
    end
    
    subgraph "Core Infrastructure"
        LS[LoggingService]
        EM[ErrorManager]
        TM[ThemeManager]
    end
    
    subgraph "Database Layer"
        DS[DatabaseService]
        DMS[DatabaseMigrationService]
        DPMS[DatabasePerformanceMonitoringService]
    end
    
    subgraph "Business Logic"
        CDS[ClientDatabaseService]
        UGS[UIDGenerationService]
        CVS[ClientValidationService]
        FCBRS[FileCheckBusinessRuleService]
        VS[ValidationService]
    end
    
    subgraph "UI Services"
        TS[ToastService]
        WCS[WindowChromeService]
    end
    
    %% Service Registration
    SL -.-> DS
    SL -.-> CDS
    SL -.-> UGS
    SL -.-> CVS
    SL -.-> FCBRS
    SL -.-> VS
    SL -.-> TS
    
    %% Service Dependencies
    CDS --> DS
    UGS --> DS
    CVS --> DS
    FCBRS --> DS
    DMS --> DS
    DPMS --> DS
    
    CDS --> LS
    UGS --> LS
    CVS --> LS
    DS --> LS
    
    CDS --> EM
    UGS --> EM
    CVS --> EM
    DS --> EM
    
    WCS --> TM
    TS --> EM
```

---

## Technology Stack

### Core Technologies

| Technology | Version | Purpose | Integration Pattern |
|------------|---------|---------|-------------------|
| **.NET** | 8.0 | Runtime Framework | Windows Desktop App |
| **WPF** | .NET 8.0 | UI Framework | XAML-based Views |
| **C#** | 12.0 | Programming Language | Nullable reference types enabled |

### UI Framework Stack

| Package | Version | Purpose | Usage Pattern |
|---------|---------|---------|---------------|
| **MaterialDesignThemes** | 5.2.1 | UI Component Library | DynamicResource binding |
| **MaterialDesignColors** | 5.2.1 | Color System | Theme-aware styling |
| **MaterialDesignThemes.MahApps** | 5.2.1 | Extended Controls | Window chrome customization |

### Data Access Stack

| Package | Version | Purpose | Usage Pattern |
|---------|---------|---------|---------------|
| **Microsoft.Data.Sqlite** | 9.0.7 | SQLite Provider | Connection management |
| **Dapper** | 2.1.66 | Micro ORM | Parameterized queries |
| **Microsoft.Extensions.Caching.Memory** | 9.0.7 | In-Memory Caching | Performance optimization |

### Utility Libraries

| Package | Version | Purpose | Usage Pattern |
|---------|---------|---------|---------------|
| **Newtonsoft.Json** | 13.0.3 | JSON Serialization | Configuration and data exchange |

---

## Architectural Patterns

### MVVM Pattern Implementation

UFU2 implements a strict MVVM (Model-View-ViewModel) pattern with the following characteristics:

#### BaseViewModel Foundation
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    // Centralized property change notification
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    
    // Logging integration for debugging
    // Error handling for property change events
}
```

#### Command Pattern with RelayCommand
```csharp
public class RelayCommand : ICommand
{
    // Supports both parameterized and non-parameterized execution
    // Integrates with LoggingService for error tracking
    // Automatic CanExecute state management
}
```

#### ViewModel Service Integration
```csharp
public class ClientManagementViewModel : BaseViewModel
{
    private readonly ClientDatabaseService _clientService;
    
    public ClientManagementViewModel()
    {
        // ServiceLocator dependency injection
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        
        // Command initialization with error handling
        SaveCommand = new RelayCommand(async () => await SaveClientAsync());
    }
}
```

### ServiceLocator Dependency Injection

UFU2 uses a custom ServiceLocator pattern for dependency injection:

#### Service Registration Pattern
```csharp
// Application startup service registration
ServiceLocator.Initialize();
await ServiceLocator.InitializeDatabaseServicesAsync();

// Service registration
ServiceLocator.RegisterService<ClientDatabaseService>();
ServiceLocator.RegisterService<UIDGenerationService>();
```

#### Service Resolution Pattern
```csharp
// In ViewModels and Services
var clientService = ServiceLocator.GetService<ClientDatabaseService>();
var validationService = ServiceLocator.GetService<ValidationService>();
```

### Error Handling Architecture

#### Centralized Error Management
```csharp
public static class ErrorManager
{
    // Arabic user-friendly error messages
    public static void HandleError(Exception exception, string userMessage, 
        string title = "خطأ", LogLevel logLevel = LogLevel.Error, string source = "UFU2")
    
    // Toast notification integration
    public static void ShowUserErrorToast(string message, string title, string logSource)
    
    // Technical logging with user-friendly display
    public static void HandleErrorToast(Exception exception, string userMessage, string title)
}
```

#### Logging Integration
```csharp
public static class LoggingService
{
    // Session-based file logging
    // Multiple log levels (Debug, Info, Warning, Error)
    // Thread-safe operations
    // Console and file output
}
```

---

## Data Flow Documentation

### Client Creation Workflow

```mermaid
sequenceDiagram
    participant UI as NewClientView
    participant VM as NewClientViewModel
    participant CVS as ClientValidationService
    participant FCBRS as FileCheckBusinessRuleService
    participant CDS as ClientDatabaseService
    participant UGS as UIDGenerationService
    participant DS as DatabaseService

    UI->>VM: SaveClient Command
    VM->>CVS: ValidateCompleteClientDataAsync()
    CVS->>VM: ValidationResult
    
    alt Validation Successful
        VM->>FCBRS: ValidateFileCheckBusinessRules()
        FCBRS->>VM: FileCheck ValidationResult
        
        alt FileCheck Valid
            VM->>UGS: GenerateClientUIDAsync()
            UGS->>DS: Database Query
            DS->>UGS: Next Sequence Number
            UGS->>VM: Generated UID
            
            VM->>CDS: CreateClientWithDetailsAsync()
            CDS->>DS: Begin Transaction
            CDS->>DS: Insert Client Data
            CDS->>DS: Insert Activities
            CDS->>DS: Insert Phone Numbers
            CDS->>DS: Commit Transaction
            DS->>CDS: Success
            CDS->>VM: Client UID
            
            VM->>UI: Show Arabic Success Toast
        else FileCheck Invalid
            VM->>UI: Display Arabic FileCheck Errors
        end
    else Validation Failed
        VM->>UI: Display Arabic Validation Errors
    end
```

### Database Transaction Flow

```mermaid
graph TD
    A[Service Method Call] --> B{Validation Required?}
    B -->|Yes| C[Input Validation]
    B -->|No| D[Create Connection]
    C --> E{Validation Passed?}
    E -->|No| F[Return Validation Errors]
    E -->|Yes| D
    D --> G[Open Connection]
    G --> H{Transaction Required?}
    H -->|Yes| I[Begin Transaction]
    H -->|No| J[Execute Query]
    I --> K[Execute Multiple Queries]
    K --> L{All Successful?}
    L -->|Yes| M[Commit Transaction]
    L -->|No| N[Rollback Transaction]
    M --> O[Log Success]
    N --> P[Log Error]
    J --> Q{Query Successful?}
    Q -->|Yes| O
    Q -->|No| P
    O --> R[Return Success Result]
    P --> S[Handle Error with ErrorManager]
    F --> T[Display Arabic Error Message]
    S --> T
    R --> U[Update UI]
    T --> U
```

---

## Security Architecture

### SQL Injection Prevention

UFU2 implements comprehensive SQL injection prevention through:

#### Parameterized Queries Only
```csharp
// Secure pattern - always used
public async Task<ClientData> FindClientAsync(string name, string idNumber)
{
    using var connection = DatabaseService.CreateConnection();
    await connection.OpenAsync();
    
    return await connection.QueryFirstOrDefaultAsync<ClientData>(
        "SELECT * FROM Clients WHERE Name = @Name AND IdNumber = @IdNumber",
        new { Name = name, IdNumber = idNumber }
    );
}
```

#### Input Validation Layers
1. **UI Validation**: Real-time validation in ViewModels using IDataErrorInfo
2. **Service Validation**: Business rule validation in service layer
3. **Database Validation**: Constraints and triggers at database level

#### Error Information Security
```csharp
// Technical details logged securely
LoggingService.LogError($"Database error: {ex.Message}", "DatabaseService");

// User-friendly Arabic messages displayed
ErrorManager.ShowUserErrorToast("حدث خطأ في قاعدة البيانات", "خطأ", "DatabaseService");
```

### Data Validation Architecture

#### Multi-Layer Validation
1. **Client-Side Validation**: Immediate feedback using ValidationService
2. **Business Rule Validation**: FileCheckBusinessRuleService for Algerian regulations
3. **Database Constraints**: Schema-level validation for data integrity

#### Validation Service Pattern
```csharp
public class ValidationService
{
    // Centralized validation logic
    // Arabic error messages
    // Consistent validation patterns
    // Integration with IDataErrorInfo
}
```

---

## Performance Architecture

### Caching Strategies

#### In-Memory Caching
```csharp
// Microsoft.Extensions.Caching.Memory integration
private readonly IMemoryCache _cache;

public async Task<List<ActivityType>> GetActivityTypesAsync()
{
    return await _cache.GetOrCreateAsync("ActivityTypes", async entry =>
    {
        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
        return await LoadActivityTypesFromDatabaseAsync();
    });
}
```

#### Database Performance Monitoring
```csharp
public class DatabasePerformanceMonitoringService
{
    // Query execution time tracking
    // Performance bottleneck identification
    // Automatic optimization recommendations
}
```

### UI Performance Patterns

#### Async/Await Implementation
```csharp
// Non-blocking UI operations
public async Task LoadDataAsync()
{
    try
    {
        var data = await _databaseService.GetDataAsync();
        // Update UI on main thread
        Application.Current.Dispatcher.Invoke(() => {
            DataCollection.Clear();
            foreach (var item in data)
                DataCollection.Add(item);
        });
    }
    catch (Exception ex)
    {
        ErrorManager.HandleError(ex, "فشل في تحميل البيانات");
    }
}
```

#### Image Processing Optimization
```csharp
// 16ms throttling for smooth interactions
private readonly DispatcherTimer _throttleTimer = new DispatcherTimer
{
    Interval = TimeSpan.FromMilliseconds(16) // 60 FPS
};

// Background processing for image operations
Task.Run(() => ProcessImageAsync(imageData))
    .ContinueWith(task => UpdateUI(task.Result), TaskScheduler.FromCurrentSynchronizationContext());
```

### Resource Management

#### IDisposable Pattern Implementation
```csharp
public class DatabaseService : IDisposable
{
    private bool _disposed = false;
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            // Dispose managed resources
            _connection?.Dispose();
        }
        _disposed = true;
    }
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
```

---

## Localization Architecture

### Arabic RTL Support

#### FlowDirection Management
```csharp
public void UpdateRtlLayout()
{
    var currentCulture = CultureInfo.CurrentUICulture;
    var isRtl = currentCulture.TextInfo.IsRightToLeft;
    
    // Update flow direction for UI components
    TitleBarFlowDirection = isRtl ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
    
    // Adjust alignment for RTL
    if (isRtl)
    {
        TitleAlignment = HorizontalAlignment.Right;
        WindowControlsAlignment = HorizontalAlignment.Left;
    }
}
```

#### Arabic Error Messages
```csharp
// Centralized Arabic error messages
public static class ErrorMessages
{
    public const string DatabaseError = "حدث خطأ في قاعدة البيانات";
    public const string ValidationError = "خطأ في التحقق من البيانات";
    public const string NetworkError = "خطأ في الاتصال بالشبكة";
}
```

### MaterialDesign Theme Integration

#### Dynamic Resource Binding
```xaml
<!-- Theme-aware styling -->
<Button Background="{DynamicResource PrimaryBrush}"
        Foreground="{DynamicResource OnPrimaryBrush}"
        Style="{DynamicResource MaterialDesignRaisedButton}" />
```

#### Theme Switching Architecture
```csharp
public static class ThemeManager
{
    // Dynamic theme switching
    public static async Task<bool> SwitchThemeAsync(ApplicationTheme theme)
    
    // MaterialDesign integration
    private static void UpdateMaterialDesignTheme(ApplicationTheme theme)
    
    // Custom color application
    private static void ApplyCustomMaterialDesignColors(ApplicationTheme theme)
}
```

---

## Deployment Architecture

### Application Packaging

#### .NET 8.0 Windows Desktop Application
- **Target Framework**: net8.0-windows
- **Output Type**: WinExe (Windows Executable)
- **Runtime**: Self-contained or Framework-dependent deployment

#### Build Configuration
```xml
<PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
</PropertyGroup>
```

### Database Deployment

#### Embedded Schema Deployment
```xml
<ItemGroup>
    <EmbeddedResource Include="Database\UFU2_Schema.sql" />
    <EmbeddedResource Include="Database\activity_Type.json" />
</ItemGroup>
```

#### Automatic Schema Migration
```csharp
public async Task InitializeSchemaAsync()
{
    // Check current schema version
    int currentVersion = await GetCurrentSchemaVersionAsync(connection);
    
    // Apply migrations if needed
    if (currentVersion < CURRENT_SCHEMA_VERSION)
    {
        await ApplyMigrationsAsync(connection, currentVersion);
    }
}
```

### Configuration Management

#### Application Configuration
- **Logging Configuration**: Session-based file logging with configurable levels
- **Theme Configuration**: Default Dark theme with user preference persistence
- **Database Configuration**: SQLite with optimized settings for performance

#### System Requirements
- **Operating System**: Windows 10/11 (x64)
- **Runtime**: .NET 8.0 Desktop Runtime
- **Memory**: Minimum 4GB RAM, Recommended 8GB
- **Storage**: 100MB for application, additional space for database growth
- **Display**: 1920x1080 minimum resolution for optimal UI experience

### Update Mechanisms

#### Database Schema Versioning
```csharp
// Automatic schema migration on application startup
private const int CURRENT_SCHEMA_VERSION = 1;

// Future-proof migration system
public async Task ApplyMigrationsAsync(IDbConnection connection, int fromVersion)
{
    for (int version = fromVersion + 1; version <= CURRENT_SCHEMA_VERSION; version++)
    {
        await ApplyMigrationToVersionAsync(connection, transaction, version);
    }
}
```

#### Application Update Strategy
- **In-place updates**: Replace executable and dependencies
- **Database preservation**: Automatic schema migration preserves user data
- **Configuration preservation**: User preferences and settings maintained
- **Rollback capability**: Database migration supports rollback for critical failures

---

---

## Advanced Architecture Patterns

### Image Management Architecture

UFU2 implements a sophisticated image management system with WYSIWYG (What You See Is What You Get) functionality:

#### Image Processing Pipeline

```mermaid
graph LR
    subgraph "Image Loading"
        IL[ImageLoadingService]
        IC[Image Caching]
        IV[Image Validation]
    end

    subgraph "Transformation Engine"
        ITS[ImageTransformationService]
        TH[16ms Throttling]
        TV[TransformationValidator]
    end

    subgraph "Cropping System"
        CS[CroppingService]
        CR[Crop Rectangle]
        SO[127x145 Standardized Output]
    end

    subgraph "Coordinate System"
        CM[Coordinate Mapping]
        RT[Rotation Handling]
        ZM[Zoom Management]
    end

    IL --> ITS
    ITS --> TH
    TH --> TV
    TV --> CS
    CS --> CR
    CR --> SO

    ITS --> CM
    CM --> RT
    RT --> ZM
```

#### Performance Optimization Strategies
```csharp
// 16ms throttling for 60 FPS smooth interactions
private readonly DispatcherTimer _transformationThrottle = new DispatcherTimer
{
    Interval = TimeSpan.FromMilliseconds(16)
};

// Background image processing
public async Task ProcessImageAsync(ImageData imageData)
{
    await Task.Run(() =>
    {
        // CPU-intensive image operations on background thread
        var processedImage = ApplyTransformations(imageData);

        // Update UI on main thread
        Application.Current.Dispatcher.Invoke(() =>
        {
            UpdateImageDisplay(processedImage);
        });
    });
}
```

### Business Logic Architecture

#### UID Generation System
UFU2 implements a sophisticated UID generation system following Algerian business requirements:

```csharp
// Client UID Pattern: {FirstLetter}{SequentialNumber:D2}
// Example: A01, B02, C03
public async Task<string> GenerateClientUIDAsync(string nameFr)
{
    var firstLetter = nameFr.Trim().ToUpper()[0];
    var sequence = await GetNextSequenceForLetterAsync(firstLetter);
    return $"{firstLetter}{sequence:D2}";
}

// Activity UID Pattern: {ClientUID}_Act{ActivitySequence}
// Example: A01_Act1, A01_Act2
public async Task<string> GenerateActivityUIDAsync(string clientUID)
{
    var sequence = await GetNextActivitySequenceAsync(clientUID);
    return $"{clientUID}_Act{sequence}";
}
```

#### File Check Business Rules
```csharp
public class FileCheckBusinessRuleService
{
    // Algerian regulation compliance
    private readonly Dictionary<ActivityType, List<FileCheckType>> _requiredChecks = new()
    {
        [ActivityType.Commercial] = new() { FileCheckType.CAS, FileCheckType.NIF, FileCheckType.NIS, FileCheckType.RC },
        [ActivityType.Craft] = new() { FileCheckType.CAS, FileCheckType.NIF, FileCheckType.NIS, FileCheckType.ART },
        [ActivityType.Professional] = new() { FileCheckType.CAS, FileCheckType.NIF, FileCheckType.NIS, FileCheckType.AGR }
    };
}
```

### Toast Notification Architecture

UFU2 implements a comprehensive toast notification system with Arabic RTL support:

```csharp
public static class ToastService
{
    // MaterialDesign icon integration
    private static readonly Dictionary<ToastType, PackIconKind> ToastIcons = new()
    {
        [ToastType.Error] = PackIconKind.CloseCircle,
        [ToastType.Success] = PackIconKind.CheckCircle,
        [ToastType.Warning] = PackIconKind.AlertCircle,
        [ToastType.Info] = PackIconKind.InformationVariantCircle
    };

    // Arabic RTL layout support
    public static void ShowToast(string title, string message, ToastType type, int duration = 5000)
    {
        var toast = new ToastNotification
        {
            Title = title,
            Message = message,
            Icon = ToastIcons[type],
            FlowDirection = CultureInfo.CurrentUICulture.TextInfo.IsRightToLeft
                ? FlowDirection.RightToLeft
                : FlowDirection.LeftToRight,
            Duration = duration
        };

        DisplayToast(toast);
    }
}
```

---

## Database Architecture Deep Dive

### Schema Design Principles

UFU2's database schema follows these core principles:

#### PascalCase Naming Convention
```sql
-- Table and column names use PascalCase
CREATE TABLE Clients (
    UID TEXT PRIMARY KEY,
    NameFr TEXT NOT NULL,
    NameAr TEXT,
    BirthDate TEXT,
    BirthPlace TEXT
);

CREATE TABLE Activities (
    UID TEXT PRIMARY KEY,
    ClientUID TEXT NOT NULL,
    ActivityType TEXT NOT NULL,
    FOREIGN KEY (ClientUID) REFERENCES Clients(UID)
);
```

#### UID-Based Primary Keys
```sql
-- UIDs as primary keys instead of auto-increment integers
-- Enables business-meaningful identifiers
-- Supports distributed systems and data migration
```

#### Minimal Indexing Strategy
```sql
-- Essential indexes only for performance
CREATE INDEX IF NOT EXISTS idx_clients_name_fr ON Clients(NameFr);
CREATE INDEX IF NOT EXISTS idx_clients_name_ar ON Clients(NameAr);
CREATE INDEX IF NOT EXISTS idx_activities_client_uid ON Activities(ClientUID);
```

### Database Performance Architecture

#### Connection Management
```csharp
public class DatabaseService : IDisposable
{
    // Optimized connection configuration
    public static async Task ConfigureDatabaseAsync(IDbConnection connection)
    {
        await connection.ExecuteAsync("PRAGMA journal_mode = WAL");
        await connection.ExecuteAsync("PRAGMA synchronous = NORMAL");
        await connection.ExecuteAsync("PRAGMA cache_size = 10000");
        await connection.ExecuteAsync("PRAGMA temp_store = MEMORY");
        await connection.ExecuteAsync("PRAGMA mmap_size = 268435456"); // 256MB
    }
}
```

#### Transaction Management
```csharp
public async Task<string> CreateClientWithDetailsAsync(ClientCreationData clientData)
{
    using var connection = CreateConnection();
    await connection.OpenAsync();

    using var transaction = connection.BeginTransaction();
    try
    {
        // Multi-step operation in single transaction
        var clientUID = await CreateClientAsync(connection, transaction, clientData.Client);
        await CreateActivitiesAsync(connection, transaction, clientUID, clientData.Activities);
        await CreatePhoneNumbersAsync(connection, transaction, clientUID, clientData.PhoneNumbers);

        transaction.Commit();
        return clientUID;
    }
    catch
    {
        transaction.Rollback();
        throw;
    }
}
```

#### Performance Monitoring Integration
```csharp
public class DatabasePerformanceMonitoringService
{
    public async Task<T> ExecuteWithMonitoringAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var result = await operation();
            stopwatch.Stop();

            LoggingService.LogDebug($"Database operation '{operationName}' completed in {stopwatch.ElapsedMilliseconds}ms", "DatabasePerformance");

            if (stopwatch.ElapsedMilliseconds > 1000) // Log slow queries
            {
                LoggingService.LogWarning($"Slow database operation detected: '{operationName}' took {stopwatch.ElapsedMilliseconds}ms", "DatabasePerformance");
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            LoggingService.LogError($"Database operation '{operationName}' failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}", "DatabasePerformance");
            throw;
        }
    }
}
```

---

## Advanced Security Patterns

### Input Sanitization Architecture

#### Multi-Layer Validation
```csharp
public class ClientValidationService
{
    // Layer 1: UI Input Validation
    public ValidationResult ValidateClientInput(ClientInputData input)
    {
        var result = new ValidationResult();

        // NameFr: Latin characters only (A-Z, a-z, spaces)
        if (!Regex.IsMatch(input.NameFr, @"^[A-Za-z\s]+$"))
        {
            result.AddError("NameFr", "يجب أن يحتوي الاسم الفرنسي على أحرف لاتينية فقط");
        }

        return result;
    }

    // Layer 2: Business Rule Validation
    public async Task<ValidationResult> ValidateBusinessRulesAsync(ClientData client)
    {
        // Complex business logic validation
        // Integration with FileCheckBusinessRuleService
        // Cross-field validation rules
    }
}
```

#### SQL Injection Prevention Patterns
```csharp
// Always use parameterized queries
public async Task<List<Client>> SearchClientsAsync(string searchTerm, int pageSize, int offset)
{
    using var connection = CreateConnection();
    await connection.OpenAsync();

    // Safe parameterized query
    return (await connection.QueryAsync<Client>(
        @"SELECT * FROM Clients
          WHERE NameFr LIKE @SearchTerm OR NameAr LIKE @SearchTerm
          ORDER BY NameFr
          LIMIT @PageSize OFFSET @Offset",
        new {
            SearchTerm = $"%{searchTerm}%",
            PageSize = pageSize,
            Offset = offset
        }
    )).ToList();
}
```

### Error Handling Security

#### Information Disclosure Prevention
```csharp
public static class ErrorManager
{
    public static void HandleDatabaseError(Exception ex, string userContext)
    {
        // Log technical details securely
        LoggingService.LogError($"Database error in {userContext}: {ex}", "DatabaseSecurity");

        // Show generic user message to prevent information disclosure
        ShowUserErrorToast(
            "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
            "خطأ في النظام",
            userContext
        );
    }
}
```

---

## Integration Patterns

### Service Integration Architecture

#### Cross-Service Communication
```csharp
public class ClientManagementWorkflow
{
    private readonly ClientDatabaseService _clientService;
    private readonly UIDGenerationService _uidService;
    private readonly ClientValidationService _validationService;
    private readonly FileCheckBusinessRuleService _fileCheckService;

    public ClientManagementWorkflow()
    {
        // ServiceLocator dependency injection
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _uidService = ServiceLocator.GetService<UIDGenerationService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
        _fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
    }

    public async Task<WorkflowResult> ExecuteCompleteClientCreationAsync(ClientCreationData data)
    {
        // Orchestrate multiple services for complex business process
        var validationResult = await _validationService.ValidateCompleteClientDataAsync(data);
        if (!validationResult.IsValid) return WorkflowResult.ValidationFailed(validationResult);

        var fileCheckResult = _fileCheckService.ValidateFileCheckBusinessRules(data.Activities);
        if (!fileCheckResult.IsValid) return WorkflowResult.FileCheckFailed(fileCheckResult);

        var clientUID = await _uidService.GenerateClientUIDAsync(data.Client.NameFr);
        data.Client.UID = clientUID;

        var createdUID = await _clientService.CreateClientWithDetailsAsync(data);
        return WorkflowResult.Success(createdUID);
    }
}
```

### Event-Driven Architecture Patterns

#### Property Change Propagation
```csharp
public abstract class BaseViewModel : INotifyPropertyChanged
{
    // Centralized property change handling with logging
    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        try
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

            // Optional: Log property changes for debugging
            LoggingService.LogDebug($"Property changed: {GetType().Name}.{propertyName}", "PropertyChange");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error raising PropertyChanged for {propertyName}: {ex.Message}", GetType().Name);
        }
    }

    // Batch property change notifications for performance
    protected void SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (!EqualityComparer<T>.Default.Equals(field, value))
        {
            field = value;
            OnPropertyChanged(propertyName);
        }
    }
}
```

---

## Testing and Quality Assurance Architecture

### Code Quality Standards

UFU2 maintains high code quality through established architectural patterns:

#### MVVM Compliance Validation
```csharp
// All ViewModels must inherit from BaseViewModel
public class ClientViewModel : BaseViewModel // ✅ Correct
{
    // Use RelayCommand for all UI interactions
    public ICommand SaveCommand { get; }

    public ClientViewModel()
    {
        // ServiceLocator dependency injection
        var clientService = ServiceLocator.GetService<ClientDatabaseService>();

        // Command initialization with error handling
        SaveCommand = new RelayCommand(async () => await SaveAsync());
    }
}
```

#### Database Access Standards
```csharp
// Always use DatabaseService with async/await
public async Task<Client> GetClientAsync(string uid)
{
    using var connection = DatabaseService.CreateConnection();
    await connection.OpenAsync();

    // Parameterized queries only
    return await connection.QueryFirstOrDefaultAsync<Client>(
        "SELECT * FROM Clients WHERE UID = @UID",
        new { UID = uid }
    );
}
```

### Performance Validation Patterns

#### Database Performance Benchmarks
```csharp
public class PerformanceValidation
{
    // Target: Client creation < 500ms
    public async Task ValidateClientCreationPerformance()
    {
        var stopwatch = Stopwatch.StartNew();
        await CreateTestClientAsync();
        stopwatch.Stop();

        if (stopwatch.ElapsedMilliseconds > 500)
        {
            LoggingService.LogWarning($"Client creation exceeded target: {stopwatch.ElapsedMilliseconds}ms", "Performance");
        }
    }

    // Target: Dialog load time < 200ms
    public async Task ValidateDialogLoadPerformance()
    {
        var stopwatch = Stopwatch.StartNew();
        await LoadDialogDataAsync();
        stopwatch.Stop();

        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 200,
            $"Dialog load time exceeded target: {stopwatch.ElapsedMilliseconds}ms");
    }
}
```

---

## Monitoring and Observability

### Logging Architecture

#### Structured Logging Implementation
```csharp
public static class LoggingService
{
    // Session-based logging with structured data
    public static void LogWithContext(LogLevel level, string message, string source,
        Dictionary<string, object>? context = null)
    {
        var logEntry = new
        {
            Timestamp = DateTime.UtcNow,
            Level = level.ToString(),
            Source = source,
            Message = message,
            Context = context ?? new Dictionary<string, object>(),
            SessionId = GetCurrentSessionId(),
            ThreadId = Thread.CurrentThread.ManagedThreadId
        };

        WriteStructuredLog(logEntry);
    }
}
```

#### Performance Monitoring Integration
```csharp
public class OperationMonitor : IDisposable
{
    private readonly Stopwatch _stopwatch;
    private readonly string _operationName;
    private readonly string _source;

    public OperationMonitor(string operationName, string source)
    {
        _operationName = operationName;
        _source = source;
        _stopwatch = Stopwatch.StartNew();

        LoggingService.LogDebug($"Started operation: {operationName}", source);
    }

    public void Dispose()
    {
        _stopwatch.Stop();
        var duration = _stopwatch.ElapsedMilliseconds;

        LoggingService.LogDebug($"Completed operation: {_operationName} in {duration}ms", _source);

        // Alert on slow operations
        if (duration > 1000)
        {
            LoggingService.LogWarning($"Slow operation detected: {_operationName} took {duration}ms", _source);
        }
    }
}

// Usage pattern
public async Task<Client> GetClientAsync(string uid)
{
    using var monitor = new OperationMonitor("GetClient", "ClientDatabaseService");
    // Operation implementation
}
```

### Error Tracking and Analytics

#### Error Deduplication System
```csharp
public static class ErrorDeduplicationManager
{
    private static readonly ConcurrentDictionary<string, ErrorOccurrence> _errorHistory = new();

    public static bool ShouldDisplayToast(string operationId, ErrorSeverity severity,
        string source, Exception exception, string userMessage)
    {
        var errorKey = GenerateErrorKey(source, exception.GetType().Name, userMessage);
        var occurrence = _errorHistory.GetOrAdd(errorKey, _ => new ErrorOccurrence());

        // Implement smart deduplication logic
        return occurrence.ShouldDisplay(severity);
    }
}
```

---

## Deployment and DevOps Architecture

### Build and Release Pipeline

#### Automated Build Configuration
```xml
<!-- UFU2.csproj optimized for production -->
<PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <TrimMode>link</TrimMode>
    <PublishTrimmed>true</PublishTrimmed>
</PropertyGroup>
```

#### Deployment Packaging Strategy
```csharp
// Self-contained deployment for simplified distribution
public class DeploymentConfiguration
{
    public static readonly DeploymentSettings Production = new()
    {
        RuntimeIdentifier = "win-x64",
        SelfContained = true,
        PublishSingleFile = true,
        IncludeNativeLibrariesForSelfExtract = true,
        EnableCompressionInSingleFile = true
    };
}
```

### Database Migration Strategy

#### Version-Controlled Schema Evolution
```csharp
public class DatabaseMigrationService
{
    private const int CURRENT_SCHEMA_VERSION = 1;

    // Future-proof migration system
    public async Task ApplyMigrationsAsync(IDbConnection connection, int fromVersion)
    {
        for (int version = fromVersion + 1; version <= CURRENT_SCHEMA_VERSION; version++)
        {
            LoggingService.LogInfo($"Applying migration to version {version}", "DatabaseMigration");

            using var transaction = connection.BeginTransaction();
            try
            {
                await ApplyMigrationToVersionAsync(connection, transaction, version);
                await UpdateSchemaVersionAsync(connection, transaction, version);
                transaction.Commit();

                LoggingService.LogInfo($"Successfully migrated to version {version}", "DatabaseMigration");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                LoggingService.LogError($"Migration to version {version} failed: {ex.Message}", "DatabaseMigration");
                throw;
            }
        }
    }
}
```

### Configuration Management

#### Environment-Specific Configuration
```csharp
public class ApplicationConfiguration
{
    public DatabaseConfiguration Database { get; set; } = new();
    public LoggingConfiguration Logging { get; set; } = new();
    public ThemeConfiguration Theme { get; set; } = new();
    public PerformanceConfiguration Performance { get; set; } = new();

    public static ApplicationConfiguration LoadFromEnvironment()
    {
        var config = new ApplicationConfiguration();

        // Load from environment variables or config files
        config.Database.ConnectionString = Environment.GetEnvironmentVariable("UFU2_DB_CONNECTION")
            ?? GetDefaultDatabasePath();

        config.Logging.Level = Enum.Parse<LogLevel>(
            Environment.GetEnvironmentVariable("UFU2_LOG_LEVEL") ?? "Info");

        return config;
    }
}
```

---

## Future Architecture Considerations

### Scalability Patterns

#### Modular Service Architecture
```csharp
// Prepare for potential microservices migration
public interface IClientService
{
    Task<string> CreateClientAsync(ClientData client);
    Task<Client> GetClientAsync(string uid);
    Task<bool> UpdateClientAsync(string uid, ClientData updates);
    Task<bool> DeleteClientAsync(string uid);
}

// Current implementation as single service
public class ClientDatabaseService : IClientService
{
    // Implementation using local SQLite database
}

// Future: Could be replaced with HTTP-based service
public class ClientApiService : IClientService
{
    // Implementation using REST API calls
}
```

#### Plugin Architecture Preparation
```csharp
public interface IBusinessRulePlugin
{
    string Name { get; }
    string Version { get; }
    Task<ValidationResult> ValidateAsync(object data);
}

public class PluginManager
{
    private readonly List<IBusinessRulePlugin> _plugins = new();

    public void RegisterPlugin(IBusinessRulePlugin plugin)
    {
        _plugins.Add(plugin);
        LoggingService.LogInfo($"Registered plugin: {plugin.Name} v{plugin.Version}", "PluginManager");
    }
}
```

### Technology Evolution Strategy

#### Framework Migration Readiness
```csharp
// Abstraction layer for UI framework independence
public interface IViewModelBase
{
    event PropertyChangedEventHandler PropertyChanged;
    void OnPropertyChanged(string propertyName);
}

// Current WPF implementation
public abstract class BaseViewModel : IViewModelBase, INotifyPropertyChanged
{
    // WPF-specific implementation
}

// Future: Could support other UI frameworks
public abstract class AvaloniaBaseViewModel : IViewModelBase
{
    // Avalonia UI implementation
}
```

---

## Architecture Decision Records (ADRs)

### ADR-001: ServiceLocator Pattern Choice

**Status**: Accepted
**Date**: 2024-01-15
**Context**: Need for dependency injection in WPF application without heavy DI container
**Decision**: Implement custom ServiceLocator pattern
**Consequences**:
- ✅ Simple implementation and debugging
- ✅ No external dependencies
- ✅ Full control over service lifecycle
- ⚠️ Manual service registration required
- ⚠️ Runtime dependency resolution

### ADR-002: SQLite with Dapper for Data Access

**Status**: Accepted
**Date**: 2024-01-15
**Context**: Need for lightweight, embedded database with ORM capabilities
**Decision**: Use SQLite with Dapper micro-ORM
**Consequences**:
- ✅ Zero-configuration deployment
- ✅ High performance for read operations
- ✅ Full SQL control with parameterized queries
- ✅ Excellent security (no network exposure)
- ⚠️ Single-user application limitation
- ⚠️ Manual schema migration implementation

### ADR-003: MaterialDesign for UI Framework

**Status**: Accepted
**Date**: 2024-01-15
**Context**: Need for modern, consistent UI with theming support
**Decision**: Use MaterialDesign for WPF with custom theme integration
**Consequences**:
- ✅ Professional, modern appearance
- ✅ Built-in dark/light theme support
- ✅ Comprehensive component library
- ✅ Arabic RTL layout compatibility
- ⚠️ Additional dependency and learning curve
- ⚠️ Custom styling requires MaterialDesign knowledge

---

## Conclusion

UFU2's architecture represents a well-designed, maintainable WPF application that successfully balances:

- **Developer Productivity**: Clear patterns and consistent service integration
- **Performance**: Optimized database access and UI responsiveness
- **Security**: Multi-layer validation and SQL injection prevention
- **Localization**: Comprehensive Arabic RTL support
- **Maintainability**: Modular design with clear separation of concerns
- **Scalability**: Future-ready patterns for growth and evolution

The architecture provides a solid foundation for continued development while maintaining the flexibility to adapt to changing requirements and technology evolution.

### Key Architectural Strengths

1. **Consistent Patterns**: MVVM, ServiceLocator, and error handling patterns applied uniformly
2. **Performance Focus**: 16ms UI throttling, database optimization, and resource management
3. **Security First**: Parameterized queries, input validation, and secure error handling
4. **Cultural Awareness**: Arabic RTL support and localized error messages
5. **Developer Experience**: Comprehensive logging, structured error handling, and clear service boundaries

### Recommended Next Steps

1. **Performance Monitoring**: Implement comprehensive performance metrics collection
2. **Automated Testing**: Develop integration tests for critical business workflows
3. **Documentation**: Maintain architectural decision records for future changes
4. **Code Quality**: Implement static analysis tools for pattern compliance
5. **Deployment Automation**: Create automated build and deployment pipelines

This architecture overview serves as the definitive reference for UFU2's system design, enabling informed architectural decisions and consistent development practices across the entire application lifecycle.
