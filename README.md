# UFU2 - Algerian Business Registration & Client Management System

![UFU2 Logo](Resources/logo.png)

UFU2 is a comprehensive Windows desktop application designed for business registration and client management in Algeria. Built with WPF and .NET 8.0, it provides a modern, Arabic-first interface for managing client information, business activities, and regulatory compliance data according to Algerian business registration requirements.

## 🌟 Key Features

- **Client Management**: Complete client registration and data management system
- **Business Activity Tracking**: Support for Commercial, Craft, and Professional activity types
- **Document Management**: File check validation following Algerian regulatory requirements
- **Payment Processing**: G12Check and BisCheck payment year tracking
- **Multi-language Support**: Arabic RTL primary interface with French secondary support
- **Modern UI**: MaterialDesign styling with Light/Dark theme support
- **Image Management**: Advanced WYSIWYG image cropping and editing capabilities
- **Performance Monitoring**: Built-in database performance optimization and monitoring

## 🏗️ Architecture

UFU2 follows established architectural patterns:

- **MVVM Pattern**: Model-View-ViewModel architecture with BaseViewModel inheritance
- **Service Locator Pattern**: Centralized dependency injection and service management
- **MaterialDesign Integration**: Consistent UI styling with DynamicResource theming
- **Arabic RTL Support**: Full right-to-left layout and localization
- **SQLite Database**: Lightweight database with Dapper ORM for data persistence

## 🚀 Quick Start Guide

### Prerequisites

Before setting up UFU2, ensure you have the following installed:

- **Visual Studio 2022** (Community, Professional, or Enterprise)
- **.NET 8.0 SDK** (Windows targeting pack)
- **Windows 10/11** (x64 architecture recommended)
- **Git** for version control

### Development Environment Setup

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd UFU2
   ```

2. **Open in Visual Studio**
   ```bash
   # Open the solution file
   start UFU2.sln
   ```
   Or open Visual Studio 2022 and select "Open a project or solution" → `UFU2.sln`

3. **Restore NuGet Packages**
   ```bash
   # Command line approach
   dotnet restore UFU2.sln
   
   # Or in Visual Studio: Right-click solution → "Restore NuGet Packages"
   ```

### Build and Run

#### Debug Build (Development)
```bash
# Build the solution
dotnet build UFU2.sln --configuration Debug

# Run the application
dotnet run --project UFU2.csproj
```

**Expected Output:**
```
Build succeeded.
    0 Warning(s)
    0 Error(s)

UFU2 Application starting up...
ServiceLocator initialized successfully
ThemeManager initialized with Dark theme
Database services initialized
Application startup completed in ~2000ms
```

#### Release Build (Production)
```bash
# Build for release
dotnet build UFU2.sln --configuration Release

# Run release build
dotnet run --project UFU2.csproj --configuration Release
```

### First Run Verification

When UFU2 starts successfully, you should see:

1. **Main Window**: Dark-themed interface with Arabic RTL layout
2. **Service Initialization**: Check console/debug output for successful service registration
3. **Database Creation**: SQLite database automatically created in application directory
4. **Theme Application**: MaterialDesign components with proper styling

## 🔧 System Requirements

### Minimum Requirements
- **OS**: Windows 10 (version 1809 or later)
- **Framework**: .NET 8.0 Runtime (Windows)
- **Memory**: 4 GB RAM
- **Storage**: 500 MB available space
- **Display**: 1366x768 resolution

### Recommended Requirements
- **OS**: Windows 11
- **Framework**: .NET 8.0 SDK (for development)
- **Memory**: 8 GB RAM or higher
- **Storage**: 2 GB available space
- **Display**: 1920x1080 resolution or higher

## 📦 Dependencies

### Core Framework Dependencies
```xml
<PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
<PackageReference Include="MaterialDesignColors" Version="5.2.1" />
<PackageReference Include="MaterialDesignThemes.MahApps" Version="5.2.1" />
<PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
<PackageReference Include="Dapper" Version="2.1.66" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
```

### Development Dependencies
- **Visual Studio 2022**: IDE with WPF and .NET 8.0 support
- **Windows SDK**: For Windows-specific features
- **Git**: Version control system

## 🏛️ Architecture Integration

UFU2 uses a service-oriented architecture with dependency injection:

```csharp
// Service Registration (App.xaml.cs)
protected override async void OnStartup(StartupEventArgs e)
{
    // Initialize ServiceLocator
    ServiceLocator.Initialize();
    
    // Register core services
    await ServiceLocator.InitializeDatabaseServicesAsync();
    
    // Initialize theme management
    await ThemeManager.InitializeAsync(ApplicationTheme.Dark);
}

// Service Usage in ViewModels
public class NewClientViewModel : BaseViewModel
{
    private readonly ClientDatabaseService _clientService;
    private readonly ClientValidationService _validationService;
    
    public NewClientViewModel()
    {
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
        
        SaveClientCommand = new RelayCommand(async () => await SaveClientAsync());
    }
    
    private async Task SaveClientAsync()
    {
        try
        {
            // Validate client data with Arabic error messages
            var validationResult = await _validationService.ValidateCompleteClientDataAsync(ClientData);
            
            if (!validationResult.IsValid)
            {
                var errorMessage = string.Join("\n", validationResult.Errors.Select(e => e.ErrorMessage));
                ErrorManager.ShowUserErrorToast(errorMessage, "خطأ في البيانات", "ClientValidation");
                return;
            }
            
            // Create client with generated UID
            var clientId = await _clientService.CreateClientWithDetailsAsync(ClientData);
            
            // Show Arabic success message
            ErrorManager.ShowUserSuccessToast("تم حفظ بيانات العميل بنجاح", "نجح الحفظ", "ClientManagement");
            
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل", "خطأ في قاعدة البيانات", 
                                   LogLevel.Error, "ClientManagement");
        }
    }
}
```

## 🔍 Troubleshooting

### Common Build Issues

**Issue**: NuGet package restore fails
```bash
# Solution: Clear NuGet cache and restore
dotnet nuget locals all --clear
dotnet restore UFU2.sln
```

**Issue**: MaterialDesign themes not loading
```bash
# Solution: Rebuild solution and check XAML references
dotnet clean UFU2.sln
dotnet build UFU2.sln
```

**Issue**: Database initialization errors
- Check write permissions in application directory
- Verify SQLite provider is properly installed
- Review application logs in debug output

### Runtime Issues

**Issue**: Arabic text not displaying correctly
- Ensure Windows Arabic language pack is installed
- Verify RTL layout settings in Windows
- Check font rendering in MaterialDesign components

**Issue**: Application startup slow (>5 seconds)
- Check database file size and location
- Review service initialization order
- Monitor performance logs for bottlenecks

## 📚 Documentation

### Core Documentation
- [Database Schema Overview](Documentation/DatabaseSchemaImplementationOverview.md)
- [Client Database Service](Documentation/ClientDatabaseService.md)
- [Validation Service](Documentation/ClientValidationService.md)
- [UID Generation Service](Documentation/UIDGenerationService.md)

### Architecture Documentation
- [Database Migration Service](Documentation/DatabaseMigrationService.md)
- [Performance Monitoring](Documentation/DatabasePerformanceMonitoringService.md)
- [File Check Business Rules](Documentation/FileCheckBusinessRuleService.md)
- [Database Entities](Documentation/DatabaseEntities.md)

### Development Resources
- [Documentation Improvement Tasks](Documentation/Documentation-Improvement-Tasks.md)

## 🤝 Development Workflow

### Getting Started (30-Minute Setup)

1. **Environment Setup** (10 minutes)
   - Install Visual Studio 2022 with .NET 8.0 workload
   - Clone repository and open solution

2. **Build and Run** (10 minutes)
   - Restore NuGet packages
   - Build solution in Debug configuration
   - Run application and verify startup

3. **Explore Architecture** (10 minutes)
   - Review ServiceLocator pattern in `Services/ServiceLocator.cs`
   - Examine MVVM implementation in `ViewModels/BaseViewModel.cs`
   - Test Arabic RTL interface and MaterialDesign theming

### Core Business Functions

UFU2 supports four main business areas:

1. **Client Management**: Registration, updates, and data management
2. **Activity Tracking**: Commercial, Craft, and Professional business activities
3. **Document Management**: File check validation per Algerian regulations
4. **Payment Processing**: G12Check and BisCheck payment year tracking

Each function integrates with the database layer through dedicated services and follows MVVM patterns for UI interaction.

## 🛠️ Development Commands

### Essential Commands
```bash
# Clean and rebuild solution
dotnet clean UFU2.sln
dotnet build UFU2.sln

# Run with specific configuration
dotnet run --project UFU2.csproj --configuration Debug
dotnet run --project UFU2.csproj --configuration Release

# Package for deployment
dotnet publish UFU2.csproj -c Release -o ./publish

# View project information
dotnet list UFU2.sln package
dotnet list UFU2.csproj reference
```

### Performance Monitoring
```bash
# Run with performance profiling
dotnet run --project UFU2.csproj --configuration Release --verbosity detailed

# Monitor database performance (check application logs)
# UFU2 includes built-in DatabasePerformanceMonitoringService
```

## 🎯 Project Structure

```
UFU2/
├── App.xaml/cs                 # Application entry point and service initialization
├── MainWindow.xaml/cs          # Main application window with custom chrome
├── Commands/                   # MVVM command implementations
│   └── RelayCommand.cs         # Generic command with logging integration
├── Common/                     # Shared utilities and infrastructure
│   ├── ErrorManager.cs         # Centralized error handling with Arabic messages
│   ├── LoggingService.cs       # Session-based logging with performance tracking
│   └── Converters/             # WPF value converters
├── Services/                   # Business logic and data access
│   ├── ServiceLocator.cs       # Dependency injection container
│   ├── DatabaseService.cs      # SQLite database operations with Dapper
│   ├── ClientDatabaseService.cs # Complete client CRUD operations
│   ├── ThemeManager.cs         # MaterialDesign theme management
│   └── UIDGenerationService.cs # Business UID generation (A01, A01_Act1)
├── ViewModels/                 # MVVM ViewModels inheriting from BaseViewModel
│   ├── BaseViewModel.cs        # Base class with INotifyPropertyChanged
│   ├── NewClientViewModel.cs   # Client registration and management
│   └── ImageManagementViewModel.cs # WYSIWYG image editing
├── Views/                      # WPF Views and UserControls
│   ├── NewClientView.xaml      # Main client registration interface
│   └── Dialogs/                # Modal dialog views
├── Models/                     # Data models with business logic
│   ├── ClientCreationData.cs   # Client creation data transfer objects
│   ├── DatabaseEntities.cs     # Database entity models for Dapper
│   └── ValidationResultModels.cs # Validation result containers
├── Resources/                  # Application assets and styling
│   ├── Styles/                 # XAML style definitions
│   ├── Themes/                 # Light/Dark theme resources
│   └── logo.png               # Application branding
├── Database/                   # Database schema and data
│   ├── UFU2_Schema.sql         # Complete SQLite schema
│   └── activity_Type.json      # Algerian business activity codes (4000+ entries)
└── Documentation/              # Comprehensive technical documentation
    ├── DatabaseSchemaImplementationOverview.md
    ├── ClientDatabaseService.md
    └── [8 additional service documentation files]
```

## 🔐 Security and Compliance

### Data Security
- **SQLite Database**: Local data storage with file-level security
- **Input Validation**: Comprehensive validation at application and database levels
- **Error Handling**: Secure error messages without sensitive data exposure
- **Logging**: Structured logging with configurable detail levels

### Algerian Business Compliance
- **Activity Type Validation**: Supports 4000+ official Algerian business activity codes
- **File Check Requirements**: Enforces proper document validation per activity type
  - **Commercial Activities**: CAS, NIF, NIS, RC, DEX requirements
  - **Craft Activities**: CAS, NIF, NIS, ART, DEX requirements
  - **Professional Activities**: CAS, NIF, NIS, AGR, DEX requirements
- **UID Generation**: Follows Algerian business registration patterns
- **Arabic Localization**: Primary interface language with proper RTL support

## 🚀 Performance Characteristics

### Application Performance
- **Startup Time**: ~2-3 seconds on modern hardware
- **Memory Usage**: ~150-200 MB typical operation
- **Database Operations**: <500ms for typical queries
- **UI Responsiveness**: 60 FPS with MaterialDesign animations

### Optimization Features
- **Async Operations**: Non-blocking UI with proper async/await patterns
- **Connection Pooling**: Efficient database connection management
- **Image Processing**: Optimized WYSIWYG cropping with 16ms throttling
- **Performance Monitoring**: Built-in query performance tracking and optimization recommendations

## 🌍 Localization Support

### Arabic RTL Implementation
```csharp
// Example: Proper Arabic text handling in ViewModels
public class NewClientViewModel : BaseViewModel
{
    private string _firstName = string.Empty;

    public string FirstName
    {
        get => _firstName;
        set
        {
            if (SetProperty(ref _firstName, value))
            {
                // Validate Arabic text input
                ValidateArabicTextInput(value, nameof(FirstName));
            }
        }
    }

    private void ValidateArabicTextInput(string input, string propertyName)
    {
        if (!string.IsNullOrEmpty(input) && !IsValidArabicText(input))
        {
            AddValidationError(propertyName, "يجب إدخال نص عربي صحيح");
        }
        else
        {
            RemoveValidationError(propertyName);
        }
    }
}
```

### Supported Languages
- **Primary**: Arabic (RTL layout, proper text shaping)
- **Secondary**: French (Latin alphabet for specific fields)
- **Technical**: English (development and logging)

## 📊 Monitoring and Diagnostics

### Built-in Monitoring
UFU2 includes comprehensive monitoring capabilities:

```csharp
// Performance monitoring example
var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();

using var monitor = perfService.StartQueryMonitoring("ClientCreation");
var clientId = await clientService.CreateClientAsync(clientData);

// Automatic performance logging and optimization recommendations
```

### Log File Locations
- **Session Logs**: `./Logs/UFU2_Session_[timestamp].log`
- **Error Logs**: Integrated with session logs with ERROR level
- **Performance Logs**: Database operation timing and optimization suggestions

### Diagnostic Information
- Service initialization status and timing
- Database schema version and migration status
- Theme application and resource loading
- Memory usage and performance metrics

---

**Ready to contribute?** Follow the architecture patterns demonstrated above and refer to the comprehensive documentation in the `/Documentation/` directory for detailed implementation guidance.

## 📞 Support and Resources

### Getting Help
- **Documentation**: Comprehensive guides in `/Documentation/` directory
- **Architecture Patterns**: Follow established MVVM, ServiceLocator, and MaterialDesign patterns
- **Code Examples**: Working examples throughout documentation with Arabic localization
- **Performance Guidelines**: Built-in monitoring and optimization recommendations

### Development Best Practices
- Inherit all ViewModels from `BaseViewModel`
- Use `ServiceLocator.GetService<T>()` for dependency injection
- Implement proper Arabic error messages with `ErrorManager`
- Follow async/await patterns for database operations
- Maintain MaterialDesign styling with `DynamicResource` usage
- Support RTL layout in all UI components
