# FileCheckBusinessRuleService Documentation

## Overview

The `FileCheckBusinessRuleService` is a critical component of UFU2's database schema implementation that enforces file check business rules at both the database and application levels. This service ensures data integrity by validating that only appropriate file check types are associated with specific activity types, implementing the Algerian business registration requirements.

**Location**: `Services/FileCheckBusinessRuleService.cs`  
**Namespace**: `UFU2.Services`  
**Implementation Status**: ✅ CREATED (Task 11)  
**Requirements**: 5.1, 5.2, 5.3, 5.4, 5.5

## What the Code Does

### Core Functionality

The service implements a comprehensive file check validation system with three main responsibilities:

1. **Database Trigger Management**: Creates and manages SQLite triggers that enforce business rules at the database level
2. **Application-Level Validation**: Provides validation methods for use in ViewModels and other services
3. **Activity Type Change Handling**: Manages cleanup when activity types change, removing invalid file check states

### Business Rules Enforced

The service enforces the following Algerian business registration file check requirements:

- **MainCommercial/SecondaryCommercial Activities**: CAS, NIF, NIS, RC, DEX
- **Craft Activities**: CAS, NIF, NIS, ART, DEX  
- **Professional Activities**: CAS, NIF, NIS, AGR, DEX

### Key Features

- **Database-Level Enforcement**: SQLite triggers prevent invalid data insertion/updates
- **Application-Level Validation**: Rich validation with Arabic error messages
- **Automatic Cleanup**: Removes invalid file checks when activity types change
- **Comprehensive Error Handling**: Integration with UFU2's ErrorManager and LoggingService
- **Thread-Safe Operations**: Proper transaction management for concurrent access

## How It's Used

### Basic Usage - Service Registration

```csharp
// During application startup (typically in App.xaml.cs)
FileCheckBusinessRuleService.RegisterService();

// The service is then available through ServiceLocator
var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
```

### Database Trigger Creation

```csharp
// Initialize database triggers (typically during app startup)
var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
await fileCheckService.CreateFileCheckValidationTriggersAsync();
```

### Validating File Check Types

```csharp
// Basic validation example
var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();

var fileCheckTypes = new[] { "CAS", "NIF", "NIS", "RC", "DEX" };
var result = fileCheckService.ValidateFileCheckTypesForActivity("MainCommercial", fileCheckTypes);

if (!result.IsValid)
{
    foreach (var error in result.Errors)
    {
        // Display Arabic error messages to user
        ErrorManager.ShowUserErrorToast(error.Value, "خطأ في التحقق", "FileCheckValidation");
    }
}
```

### Comprehensive Business Rule Validation

```csharp
// Complete validation with completion enforcement
var fileCheckStates = new Dictionary<string, bool>
{
    { "CAS", true },
    { "NIF", false },
    { "NIS", true },
    { "RC", true },
    { "DEX", false }
};

var result = fileCheckService.ValidateFileCheckBusinessRules(
    "MainCommercial", 
    fileCheckStates, 
    enforceCompletion: true
);

if (!result.IsValid)
{
    // Handle validation errors
    foreach (var error in result.Errors)
    {
        LoggingService.LogWarning($"File check validation error: {error.Value}", "ClientValidation");
    }
}
```

### Handling Activity Type Changes

```csharp
// When an activity type changes in the UI
var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();

try
{
    await fileCheckService.HandleActivityTypeChangeAsync(activityUID, "Craft");
    // Invalid file check types for Craft activities are automatically removed
}
catch (Exception ex)
{
    ErrorManager.HandleError(ex, "فشل في تحديث نوع النشاط", "خطأ في قاعدة البيانات", 
                           LogLevel.Error, "ActivityManagement");
}
```

### Advanced Usage - ViewModel Integration

```csharp
// In NewClientViewModel or similar
public class NewClientViewModel : BaseViewModel
{
    private readonly FileCheckBusinessRuleService _fileCheckService;
    
    public NewClientViewModel()
    {
        _fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
    }
    
    private async Task ValidateFileChecksAsync()
    {
        var result = _fileCheckService.ValidateFileCheckBusinessRules(
            SelectedActivity.ActivityType,
            GetCurrentFileCheckStates(),
            enforceCompletion: false
        );
        
        if (!result.IsValid)
        {
            // Update UI with validation errors
            ValidationErrors.Clear();
            foreach (var error in result.Errors)
            {
                ValidationErrors.Add(error.Value);
            }
        }
    }
    
    private async Task OnActivityTypeChangedAsync()
    {
        if (!string.IsNullOrEmpty(SelectedActivity?.UID))
        {
            await _fileCheckService.HandleActivityTypeChangeAsync(
                SelectedActivity.UID, 
                SelectedActivity.ActivityType
            );
            
            // Refresh file check options in UI
            await RefreshFileCheckOptionsAsync();
        }
    }
}
```

## Integration with UFU2 Architecture

### MVVM Pattern Integration

The service integrates seamlessly with UFU2's MVVM architecture:

- **ViewModels**: Use the service for real-time validation and business rule enforcement
- **Models**: Work with `ValidationResult` objects that support WPF data binding
- **Views**: Display Arabic error messages through the existing error handling system

### Service Dependencies

```mermaid
graph TD
    A[FileCheckBusinessRuleService] --> B[DatabaseService]
    A --> C[ServiceLocator]
    A --> D[LoggingService]
    A --> E[ErrorManager]
    A --> F[FileCheckTypeRules]
    
    G[NewClientViewModel] --> A
    H[ClientDatabaseService] --> A
    I[ActivityManagementService] --> A
```

### Database Integration

The service works with the following database tables:

- **activities**: Source of activity type information
- **file_check_states**: Target table for file check validation
- **Database Triggers**: Enforce rules at the database level

### Error Handling Integration

```csharp
// Consistent error handling with Arabic messages
try
{
    await fileCheckService.CreateFileCheckValidationTriggersAsync();
}
catch (Exception ex)
{
    ErrorManager.HandleError(ex, "فشل في إنشاء قواعد التحقق من الملفات", 
                           "خطأ في قاعدة البيانات", LogLevel.Error, "FileCheckService");
}
```

### Logging Integration

```csharp
// Comprehensive logging throughout operations
LoggingService.LogInfo("File check validation triggers created successfully", "FileCheckBusinessRuleService");
LoggingService.LogError($"Failed to create file check validation triggers: {ex.Message}", "FileCheckBusinessRuleService");
```

## Performance Considerations

### Database Trigger Performance

- **Trigger Efficiency**: Triggers use efficient CASE statements and IN clauses
- **Index Usage**: Leverages existing indexes on `activities.uid` and `file_check_states.activity_uid`
- **Minimal Overhead**: Triggers only fire on INSERT/UPDATE operations

### Memory Management

```csharp
// Proper resource disposal
using var connection = _databaseService.CreateConnection();
await connection.OpenAsync();
using var transaction = connection.BeginTransaction();
// Operations...
transaction.Commit(); // or Rollback on error
```

### Caching Strategy

The service relies on `FileCheckTypeRules` static methods for business rule definitions, providing:

- **Static Rule Caching**: Business rules are cached in memory
- **No Database Lookups**: Rule validation doesn't require database queries
- **Fast Validation**: In-memory validation for UI responsiveness

### Transaction Management

```csharp
// All multi-step operations use transactions
using var transaction = connection.BeginTransaction();
try
{
    await DropExistingTriggersAsync(connection, transaction);
    await CreateInsertTriggerAsync(connection, transaction);
    await CreateUpdateTriggerAsync(connection, transaction);
    transaction.Commit();
}
catch (Exception ex)
{
    transaction.Rollback();
    throw;
}
```

### Concurrent Access Handling

- **Database Transactions**: Ensure ACID properties for concurrent operations
- **Connection Management**: Uses connection pooling through `DatabaseService`
- **Thread Safety**: All public methods are thread-safe through proper transaction isolation

## Mermaid Diagram

### Service Architecture and Data Flow

```mermaid
graph TB
    subgraph "UI Layer"
        A[NewClientViewModel]
        B[ActivityManagementView]
        C[FileCheckControls]
    end
    
    subgraph "Service Layer"
        D[FileCheckBusinessRuleService]
        E[ClientDatabaseService]
        F[DatabaseService]
    end
    
    subgraph "Database Layer"
        G[(SQLite Database)]
        H[file_check_states Table]
        I[activities Table]
        J[Database Triggers]
    end
    
    subgraph "Business Rules"
        K[FileCheckTypeRules]
        L[ValidationResult]
    end
    
    subgraph "Infrastructure"
        M[ServiceLocator]
        N[ErrorManager]
        O[LoggingService]
    end
    
    %% UI to Service connections
    A --> D
    B --> D
    C --> D
    
    %% Service layer connections
    D --> E
    D --> F
    D --> K
    E --> D
    
    %% Database connections
    F --> G
    D --> H
    D --> I
    J --> H
    J --> I
    
    %% Business rule connections
    D --> L
    K --> L
    
    %% Infrastructure connections
    D --> M
    D --> N
    D --> O
    
    %% Data flow annotations
    A -.->|"Validate file checks"| D
    D -.->|"Return ValidationResult"| A
    D -.->|"Create/Update triggers"| J
    J -.->|"Enforce rules"| H
    D -.->|"Arabic error messages"| N
    D -.->|"Operation logging"| O
    
    classDef uiClass fill:#e1f5fe
    classDef serviceClass fill:#f3e5f5
    classDef dbClass fill:#e8f5e8
    classDef ruleClass fill:#fff3e0
    classDef infraClass fill:#fce4ec
    
    class A,B,C uiClass
    class D,E,F serviceClass
    class G,H,I,J dbClass
    class K,L ruleClass
    class M,N,O infraClass
```

### Business Rule Validation Flow

```mermaid
sequenceDiagram
    participant UI as ViewModel
    participant FCS as FileCheckBusinessRuleService
    participant FCR as FileCheckTypeRules
    participant DB as Database
    participant EM as ErrorManager
    participant LS as LoggingService
    
    UI->>FCS: ValidateFileCheckBusinessRules(activityType, fileCheckStates)
    
    FCS->>LS: LogInfo("Starting validation")
    
    FCS->>FCR: GetValidFileCheckTypes(activityType)
    FCR-->>FCS: validTypes[]
    
    FCS->>FCR: GetRequiredFileCheckTypes(activityType)
    FCR-->>FCS: requiredTypes[]
    
    loop For each file check type
        FCS->>FCS: Validate type against validTypes
        alt Invalid type found
            FCS->>FCR: GetFileCheckTypeDisplayName(type)
            FCR-->>FCS: displayName
            FCS->>FCS: AddError with Arabic message
        end
    end
    
    loop For each required type
        FCS->>FCS: Check if type exists and completed
        alt Missing or incomplete
            FCS->>FCR: GetFileCheckTypeDisplayName(type)
            FCR-->>FCS: displayName
            FCS->>FCS: AddError with Arabic message
        end
    end
    
    FCS->>LS: LogInfo("Validation completed")
    FCS-->>UI: ValidationResult
    
    alt Validation failed
        UI->>EM: ShowUserErrorToast(arabicMessage)
        EM->>UI: Display error toast
    end
```

### Database Trigger Creation Flow

```mermaid
flowchart TD
    A[CreateFileCheckValidationTriggersAsync] --> B[Open Database Connection]
    B --> C[Begin Transaction]
    C --> D[Drop Existing Triggers]
    D --> E[Create INSERT Trigger]
    E --> F[Create UPDATE Trigger]
    F --> G{All Operations Successful?}
    
    G -->|Yes| H[Commit Transaction]
    G -->|No| I[Rollback Transaction]
    
    H --> J[Log Success]
    I --> K[Log Error]
    K --> L[Throw Exception]
    
    J --> M[End]
    L --> M
    
    subgraph "Trigger Logic"
        N[Check Activity Type]
        N --> O{MainCommercial/SecondaryCommercial?}
        N --> P{Craft?}
        N --> Q{Professional?}
        
        O -->|Yes| R[Validate: CAS, NIF, NIS, RC, DEX]
        P -->|Yes| S[Validate: CAS, NIF, NIS, ART, DEX]
        Q -->|Yes| T[Validate: CAS, NIF, NIS, AGR, DEX]
        
        R --> U[RAISE ABORT if invalid]
        S --> U
        T --> U
    end
    
    E -.-> N
    F -.-> N
```

This documentation provides comprehensive coverage of the FileCheckBusinessRuleService, including practical usage examples, architectural integration details, performance considerations, and visual diagrams showing the service's role within the UFU2 system. The service is a critical component for maintaining data integrity in the Algerian business registration system.