# NewClientViewModel Implementation Guide

## Overview

The `NewClientViewModel` is the primary orchestrating ViewModel for client registration and management in the UFU2 application. This sophisticated implementation follows the composition pattern, utilizing smaller, focused component ViewModels to manage different aspects of client data while maintaining a clean separation of concerns. The ViewModel serves as the main coordinator for the NewClientView, handling the complete client creation and editing workflow with comprehensive validation, state management, and database operations.

**Recent Update**: Enhanced phone number data conversion with proper enum-to-string mapping for seamless integration between UI models and database transfer objects.

## What the Code Does

The NewClientViewModel manages the following core functionalities:

### Composition Architecture
- **Component ViewModels**: Orchestrates four specialized ViewModels (PersonalInfo, ContactInfo, ActivityManagement, NotesManagement)
- **Event Coordination**: Manages property change notifications and command forwarding between components
- **State Synchronization**: Maintains consistent state across all component ViewModels
- **Validation Aggregation**: Combines validation results from all components to determine overall form validity

### Client Lifecycle Management
- **New Client Creation**: Handles the complete workflow for registering new clients
- **Existing Client Editing**: Supports editing existing clients loaded from duplicate detection
- **Data Persistence**: Manages database operations through ClientDatabaseService
- **Audit Logging**: Tracks all changes for compliance and historical records
- **Data Type Conversion**: Seamlessly converts between UI models (enum types) and database transfer objects (string types)

### Service Integration
- **Database Services**: Integrates with ClientDatabaseService, UIDGenerationService, and ClientValidationService
- **Error Handling**: Provides comprehensive error handling with Arabic localized messages
- **Loading States**: Manages UI loading states during async operations
- **Resource Management**: Proper disposal of resources and event subscriptions

### Command Management
- **SaveClientCommand**: Handles client data persistence with validation
- **CloseCommand**: Manages dialog closure and cleanup operations
- **Component Commands**: Forwards commands from component ViewModels to the UI

## How It's Used

### Basic Usage Pattern

```csharp
// Initialize the ViewModel
var newClientVM = new NewClientViewModel();

// Set personal information
newClientVM.NameFr = "Ahmed Ben Ali";
newClientVM.NameAr = "أحمد بن علي";
newClientVM.BirthDate = "15/03/1985";
newClientVM.BirthPlace = "الجزائر";
newClientVM.Gender = 0; // Male
newClientVM.Address = "شارع الاستقلال، الجزائر العاصمة";
newClientVM.NationalId = "1234567890123456";

// Configure activity information
newClientVM.SelectedActivityType = "MainCommercial";
newClientVM.CurrentActivity.CommercialRegister = "123456789";
newClientVM.CurrentActivity.NifNumber = "NIF123456789";
newClientVM.CurrentActivity.NisNumber = "NIS987654321";
newClientVM.CurrentActivity.ActivityStatus = "نشط";

// Set CPI location
newClientVM.SelectedCpiWilaya = availableWilayas.First(w => w.NameAr == "الجزائر");
newClientVM.SelectedCpiDaira = availableDairas.First(d => d.NameAr == "سيدي أمحمد");

// Add phone numbers
newClientVM.PhoneNumbers.AddPhoneNumber("0555123456", PhoneType.Mobile, true);
newClientVM.PhoneNumbers.AddPhoneNumber("021456789", PhoneType.Home, false);

// Add notes
newClientVM.NotesManagement.AddNoteWithContent("عميل جديد - تم التحقق من الوثائق", true);

// Save the client
if (newClientVM.CanSave)
{
    await newClientVM.SaveClientCommand.ExecuteAsync(null);
}
```

### Component ViewModel Integration

```csharp
// Working with component ViewModels directly
public void ConfigureClientComponents(NewClientViewModel viewModel)
{
    // Personal Information Component
    var personalInfo = viewModel.PersonalInfo;
    personalInfo.NameFr = "Fatima Zahra";
    personalInfo.NameAr = "فاطمة الزهراء";
    personalInfo.SetProfileImage(profileImageBitmap);

    // Contact Information Component - Enhanced Phone Number Handling
    var contactInfo = viewModel.ContactInfo;
    contactInfo.PhoneNumbers.AddPhoneNumber("0661234567", PhoneType.Mobile, true);
    contactInfo.PhoneNumbers.AddPhoneNumber("0771234567", PhoneType.Work, false);
    
    // Verify phone number conversion works correctly
    var phoneModel = contactInfo.PhoneNumbers.PhoneNumbers.First();
    Debug.Assert(phoneModel.PhoneType == PhoneType.Mobile); // Enum in UI model
    
    // When saving, the enum will be converted to string automatically
    var phoneData = new PhoneNumberData
    {
        PhoneNumber = phoneModel.PhoneNumber,
        PhoneType = phoneModel.PhoneType.ToString(), // "Mobile" string for database
        IsPrimary = phoneModel.IsPrimary
    };

    // Activity Management Component
    var activityMgmt = viewModel.ActivityManagement;
    activityMgmt.SelectedActivityType = "Craft";
    activityMgmt.CurrentActivity.ArtNumber = "ART123456789";
    activityMgmt.G12SelectedYears = new List<int> { 2023, 2024 };
    activityMgmt.BISSelectedYears = new List<int> { 2024 };

    // Notes Management Component
    var notesMgmt = viewModel.NotesManagement;
    notesMgmt.AddNoteWithContent("عميلة حرفية متخصصة في الخياطة التقليدية", true);
    notesMgmt.AddNoteWithContent("تحتاج متابعة خاصة للوثائق", false);

    LoggingService.LogInfo("Client components configured successfully", "ClientConfiguration");
}
```

### Existing Client Editing Workflow

```csharp
// Load existing client for editing
public async Task LoadExistingClientForEditingAsync(NewClientViewModel viewModel, string clientUid)
{
    try
    {
        // Simulate loading client data from duplicate detection
        var duplicateClientData = new DuplicateClientData
        {
            ClientUid = clientUid,
            NameFr = "Mohamed Amine",
            NameAr = "محمد أمين",
            BirthDate = "20/05/1990",
            BirthPlace = "وهران",
            Gender = 0,
            Address = "حي السلام، وهران",
            NationalId = "9876543210987654"
        };

        // Set editing mode
        viewModel.SetEditingExistingClient(duplicateClientData);

        // Verify editing state
        Debug.Assert(viewModel.IsEditingExistingClient == true);
        Debug.Assert(viewModel.ExistingClientUid == clientUid);
        Debug.Assert(viewModel.NameFr == "Mohamed Amine");
        Debug.Assert(viewModel.NameAr == "محمد أمين");

        // Make modifications
        viewModel.Address = "حي النصر، وهران"; // Update address
        viewModel.NotesManagement.AddNoteWithContent("تم تحديث العنوان", false);

        // Save changes
        if (viewModel.CanSave)
        {
            await viewModel.SaveClientCommand.ExecuteAsync(null);
            LoggingService.LogInfo($"Client {clientUid} updated successfully", "ClientEditing");
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error loading existing client: {ex.Message}", "ClientEditing");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء تحميل بيانات العميل",
            "خطأ في التحميل",
            LogLevel.Error,
            "ClientEditing");
    }
}
```

### Validation and Error Handling

```csharp
// Comprehensive validation example
public async Task<bool> ValidateAndSaveClientAsync(NewClientViewModel viewModel)
{
    try
    {
        // Check individual component validation
        if (!viewModel.PersonalInfo.IsValid())
        {
            LoggingService.LogWarning("Personal information validation failed", "ClientValidation");
            ErrorManager.HandleErrorToast(new ValidationException("Personal info invalid"),
                "يرجى التحقق من البيانات الشخصية",
                "خطأ في التحقق",
                LogLevel.Warning,
                "ClientValidation");
            return false;
        }

        if (!viewModel.ContactInfo.IsValid())
        {
            LoggingService.LogWarning("Contact information validation failed", "ClientValidation");
            ErrorManager.HandleErrorToast(new ValidationException("Contact info invalid"),
                "يرجى التحقق من معلومات الاتصال",
                "خطأ في التحقق",
                LogLevel.Warning,
                "ClientValidation");
            return false;
        }

        if (!viewModel.ActivityManagement.IsValid())
        {
            LoggingService.LogWarning("Activity management validation failed", "ClientValidation");
            ErrorManager.HandleErrorToast(new ValidationException("Activity info invalid"),
                "يرجى التحقق من بيانات النشاط",
                "خطأ في التحقق",
                LogLevel.Warning,
                "ClientValidation");
            return false;
        }

        if (!viewModel.NotesManagement.IsValid())
        {
            LoggingService.LogWarning("Notes management validation failed", "ClientValidation");
            return false;
        }

        // Specific phone number validation with type conversion
        if (!ValidatePhoneNumberConversion(viewModel))
        {
            LoggingService.LogWarning("Phone number type conversion validation failed", "ClientValidation");
            ErrorManager.HandleErrorToast(new ValidationException("Phone type conversion failed"),
                "حدث خطأ في تحويل نوع الهاتف",
                "خطأ في التحقق",
                LogLevel.Warning,
                "ClientValidation");
            return false;
        }

        // Overall validation check
        if (!viewModel.CanSave)
        {
            LoggingService.LogWarning("Overall validation failed - cannot save", "ClientValidation");
            return false;
        }

        // Perform save operation
        await viewModel.SaveClientCommand.ExecuteAsync(null);
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error during validation and save: {ex.Message}", "ClientValidation");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء حفظ البيانات",
            "خطأ في الحفظ",
            LogLevel.Error,
            "ClientValidation");
        return false;
    }
}

// Phone number type conversion validation
private bool ValidatePhoneNumberConversion(NewClientViewModel viewModel)
{
    try
    {
        if (viewModel.ContactInfo.PhoneNumbers?.PhoneNumbers == null)
            return true; // No phone numbers to validate

        foreach (var phoneModel in viewModel.ContactInfo.PhoneNumbers.PhoneNumbers)
        {
            // Validate enum to string conversion
            var phoneTypeString = phoneModel.PhoneType.ToString();
            var validTypes = new[] { "Mobile", "Home", "Work", "Fax" };
            
            if (!validTypes.Contains(phoneTypeString))
            {
                LoggingService.LogError($"Invalid phone type conversion: {phoneModel.PhoneType} -> {phoneTypeString}", 
                                      "PhoneNumberValidation");
                return false;
            }

            // Validate reverse conversion
            if (!Enum.TryParse<PhoneType>(phoneTypeString, out var parsedType) || parsedType != phoneModel.PhoneType)
            {
                LoggingService.LogError($"Phone type reverse conversion failed: {phoneTypeString} -> {parsedType}", 
                                      "PhoneNumberValidation");
                return false;
            }

            // Validate Arabic display name exists
            var displayName = PhoneNumberModel.GetPhoneTypeDisplayName(phoneModel.PhoneType);
            if (string.IsNullOrEmpty(displayName))
            {
                LoggingService.LogError($"Missing Arabic display name for phone type: {phoneModel.PhoneType}", 
                                      "PhoneNumberValidation");
                return false;
            }

            LoggingService.LogDebug($"Phone number validation passed: {phoneModel.PhoneNumber} ({phoneModel.PhoneType})", 
                                  "PhoneNumberValidation");
        }

        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Exception during phone number validation: {ex.Message}", "PhoneNumberValidation");
        return false;
    }
}
```

### Event Handling and Property Synchronization

```csharp
// Event handling example
public class ClientManagementService
{
    private NewClientViewModel _viewModel;

    public ClientManagementService()
    {
        _viewModel = new NewClientViewModel();
        
        // Subscribe to events
        _viewModel.EditStatusRequested += OnEditStatusRequested;
        _viewModel.PropertyChanged += OnViewModelPropertyChanged;
        
        // Subscribe to component events
        _viewModel.PersonalInfo.PropertyChanged += OnPersonalInfoChanged;
        _viewModel.ActivityManagement.PropertyChanged += OnActivityManagementChanged;
    }

    private void OnEditStatusRequested()
    {
        // Handle edit status request - typically opens a dialog
        LoggingService.LogInfo("Edit status dialog should be opened", "ClientManagementService");
        
        // Example: Open ActivityStatusUpdateDialog
        var dialog = new ActivityStatusUpdateDialog(_viewModel.CurrentActivity);
        var result = dialog.ShowDialog();
        
        if (result == true)
        {
            // Refresh activity status display
            _viewModel.ActivityManagement.OnPropertyChanged(nameof(ActivityManagementViewModel.CurrentActivity));
        }
    }

    private void OnViewModelPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(NewClientViewModel.IsLoading):
                // Update UI loading indicators
                UpdateLoadingState(_viewModel.IsLoading);
                break;
                
            case nameof(NewClientViewModel.CanSave):
                // Update save button state
                UpdateSaveButtonState(_viewModel.CanSave);
                break;
                
            case nameof(NewClientViewModel.SelectedActivityType):
                // Handle activity type changes
                OnActivityTypeChanged(_viewModel.SelectedActivityType);
                break;
        }
    }

    private void OnPersonalInfoChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(PersonalInformationViewModel.NameFr))
        {
            // Trigger duplicate client detection
            _ = CheckForDuplicateClientsAsync(_viewModel.NameFr);
        }
    }

    private void OnActivityManagementChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ActivityManagementViewModel.SelectedActivityType))
        {
            // Update file check requirements based on activity type
            UpdateFileCheckRequirements(_viewModel.SelectedActivityType);
        }
    }

    private async Task CheckForDuplicateClientsAsync(string nameFr)
    {
        // Implementation for duplicate detection
        LoggingService.LogInfo($"Checking for duplicates: {nameFr}", "DuplicateDetection");
    }

    private void UpdateFileCheckRequirements(string activityType)
    {
        // Update UI based on activity type requirements
        LoggingService.LogInfo($"Updated file check requirements for: {activityType}", "FileCheckUpdate");
    }
}
```

### Phone Number Data Conversion

The NewClientViewModel handles seamless conversion between UI models and database transfer objects, particularly for phone number data:

```csharp
// Phone Number Type Conversion Example
public void DemonstratePhoneNumberConversion(NewClientViewModel viewModel)
{
    // UI Model uses PhoneType enum for type safety and ComboBox binding
    var phoneModel = new PhoneNumberModel("0555123456", PhoneType.Mobile);
    phoneModel.IsPrimary = true;
    
    // Add to collection (UI layer)
    viewModel.ContactInfo.PhoneNumbers.AddPhoneNumber(
        phoneModel.PhoneNumber, 
        phoneModel.PhoneType, 
        phoneModel.IsPrimary
    );
    
    // During save operation, enum is converted to string for database
    // This happens automatically in CreateClientDataFromComponents()
    var phoneData = new PhoneNumberData
    {
        PhoneNumber = phoneModel.PhoneNumber,
        PhoneType = phoneModel.PhoneType.ToString(), // "Mobile" -> string
        IsPrimary = phoneModel.IsPrimary
    };
    
    // Verify conversion maintains data integrity
    Debug.Assert(phoneData.PhoneType == "Mobile");
    Debug.Assert(phoneData.PhoneTypeDisplayName == "هاتف محمول");
    
    LoggingService.LogInfo($"Phone type converted: {phoneModel.PhoneType} -> {phoneData.PhoneType}", 
                          "PhoneNumberConversion");
}

// Reverse conversion when loading existing client data
public void LoadPhoneNumbersFromDatabase(NewClientViewModel viewModel, List<PhoneNumberData> phoneDataList)
{
    foreach (var phoneData in phoneDataList)
    {
        // Convert string back to enum for UI binding
        if (Enum.TryParse<PhoneType>(phoneData.PhoneType, out var phoneType))
        {
            var phoneModel = new PhoneNumberModel(phoneData.PhoneNumber, phoneType)
            {
                IsPrimary = phoneData.IsPrimary
            };
            
            viewModel.ContactInfo.PhoneNumbers.PhoneNumbers.Add(phoneModel);
        }
        else
        {
            // Fallback to Mobile if conversion fails
            LoggingService.LogWarning($"Invalid phone type from database: {phoneData.PhoneType}", 
                                    "PhoneNumberConversion");
            
            var phoneModel = new PhoneNumberModel(phoneData.PhoneNumber, PhoneType.Mobile)
            {
                IsPrimary = phoneData.IsPrimary
            };
            
            viewModel.ContactInfo.PhoneNumbers.PhoneNumbers.Add(phoneModel);
        }
    }
}

// Validation of phone type conversion
public bool ValidatePhoneTypeConversion()
{
    var testCases = new[]
    {
        (PhoneType.Mobile, "Mobile", "هاتف محمول"),
        (PhoneType.Home, "Home", "هاتف المنزل"),
        (PhoneType.Work, "Work", "هاتف العمل"),
        (PhoneType.Fax, "Fax", "فاكس")
    };
    
    foreach (var (enumValue, stringValue, arabicDisplay) in testCases)
    {
        // Test enum to string conversion
        var convertedString = enumValue.ToString();
        if (convertedString != stringValue)
        {
            LoggingService.LogError($"Enum conversion failed: {enumValue} -> {convertedString} (expected {stringValue})", 
                                  "PhoneTypeValidation");
            return false;
        }
        
        // Test Arabic display name
        var displayName = PhoneNumberModel.GetPhoneTypeDisplayName(enumValue);
        if (displayName != arabicDisplay)
        {
            LoggingService.LogError($"Arabic display failed: {enumValue} -> {displayName} (expected {arabicDisplay})", 
                                  "PhoneTypeValidation");
            return false;
        }
        
        // Test reverse conversion
        if (Enum.TryParse<PhoneType>(stringValue, out var parsedEnum) && parsedEnum == enumValue)
        {
            LoggingService.LogDebug($"Phone type conversion validated: {enumValue} <-> {stringValue}", 
                                  "PhoneTypeValidation");
        }
        else
        {
            LoggingService.LogError($"Reverse conversion failed: {stringValue} -> {parsedEnum} (expected {enumValue})", 
                                  "PhoneTypeValidation");
            return false;
        }
    }
    
    return true;
}
```

### Advanced Usage with Complete Client Setup

```csharp
public async Task<bool> CreateCompleteClientAsync(NewClientViewModel viewModel, ClientSetupData setupData)
{
    try
    {
        // Set loading state
        viewModel.IsLoading = true;

        // Configure personal information
        viewModel.NameFr = setupData.NameFr;
        viewModel.NameAr = setupData.NameAr;
        viewModel.BirthDate = setupData.BirthDate;
        viewModel.BirthPlace = setupData.BirthPlace;
        viewModel.Gender = setupData.Gender;
        viewModel.Address = setupData.Address;
        viewModel.NationalId = setupData.NationalId;

        // Set profile image if provided
        if (setupData.ProfileImagePath != null)
        {
            var profileImage = LoadImageFromPath(setupData.ProfileImagePath);
            viewModel.ProfileImage = profileImage;
        }

        // Configure contact information
        foreach (var phoneData in setupData.PhoneNumbers)
        {
            viewModel.PhoneNumbers.AddPhoneNumber(
                phoneData.Number, 
                phoneData.Type, 
                phoneData.IsPrimary
            );
        }

        // Configure activity based on type
        viewModel.SelectedActivityType = setupData.ActivityType;
        await ConfigureActivityByTypeAsync(viewModel, setupData);

        // Configure CPI location
        if (setupData.CpiWilayaName != null)
        {
            var wilaya = viewModel.CpiWilayas.FirstOrDefault(w => w.NameAr == setupData.CpiWilayaName);
            if (wilaya != null)
            {
                viewModel.SelectedCpiWilaya = wilaya;
                
                // Wait for dairas to load
                await Task.Delay(500);
                
                if (setupData.CpiDairaName != null)
                {
                    var daira = viewModel.CpiDairas.FirstOrDefault(d => d.NameAr == setupData.CpiDairaName);
                    if (daira != null)
                    {
                        viewModel.SelectedCpiDaira = daira;
                    }
                }
            }
        }

        // Configure payment years
        if (setupData.G12Years?.Any() == true)
        {
            viewModel.G12SelectedYears = setupData.G12Years.ToList();
        }

        if (setupData.BISYears?.Any() == true)
        {
            viewModel.BISSelectedYears = setupData.BISYears.ToList();
        }

        // Add notes
        foreach (var noteContent in setupData.Notes)
        {
            viewModel.NotesManagement.AddNoteWithContent(noteContent, false);
        }

        // Validate complete setup
        if (!viewModel.CanSave)
        {
            LoggingService.LogWarning("Complete client setup validation failed", "ClientSetup");
            return false;
        }

        // Save the client
        await viewModel.SaveClientCommand.ExecuteAsync(null);

        LoggingService.LogInfo($"Complete client setup successful for {setupData.NameFr}", "ClientSetup");
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error in complete client setup: {ex.Message}", "ClientSetup");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء إعداد العميل",
            "خطأ في الإعداد",
            LogLevel.Error,
            "ClientSetup");
        return false;
    }
    finally
    {
        viewModel.IsLoading = false;
    }
}

private async Task ConfigureActivityByTypeAsync(NewClientViewModel viewModel, ClientSetupData setupData)
{
    var currentActivity = viewModel.CurrentActivity;
    
    switch (setupData.ActivityType)
    {
        case "MainCommercial":
            currentActivity.CommercialRegister = setupData.CommercialRegister;
            currentActivity.NifNumber = setupData.NifNumber;
            currentActivity.NisNumber = setupData.NisNumber;
            currentActivity.ActivityStatus = "نشط";
            
            // Configure file checks for commercial activity
            var commercialFileChecks = viewModel.CurrentFileCheckStates;
            commercialFileChecks.CasFileCheck = true;
            commercialFileChecks.NifFileCheck = true;
            commercialFileChecks.NisFileCheck = true;
            commercialFileChecks.RcFileCheck = true;
            break;
            
        case "Craft":
            currentActivity.ArtNumber = setupData.ArtNumber;
            currentActivity.NifNumber = setupData.NifNumber;
            currentActivity.NisNumber = setupData.NisNumber;
            currentActivity.ActivityStatus = "نشط";
            
            // Configure file checks for craft activity
            var craftFileChecks = viewModel.CurrentFileCheckStates;
            craftFileChecks.CasFileCheck = true;
            craftFileChecks.NifFileCheck = true;
            craftFileChecks.NisFileCheck = true;
            craftFileChecks.ArtFileCheck = true;
            break;
            
        case "Professional":
            currentActivity.NifNumber = setupData.NifNumber;
            currentActivity.NisNumber = setupData.NisNumber;
            currentActivity.ActivityStatus = "نشط";
            
            // Configure file checks for professional activity
            var professionalFileChecks = viewModel.CurrentFileCheckStates;
            professionalFileChecks.CasFileCheck = true;
            professionalFileChecks.NifFileCheck = true;
            professionalFileChecks.NisFileCheck = true;
            professionalFileChecks.AgrFileCheck = true;
            break;
    }
    
    currentActivity.ActivityStartDate = DateTime.Now.ToString("dd/MM/yyyy");
}

// Supporting data class
public class ClientSetupData
{
    public string NameFr { get; set; } = string.Empty;
    public string NameAr { get; set; } = string.Empty;
    public string BirthDate { get; set; } = string.Empty;
    public string BirthPlace { get; set; } = string.Empty;
    public int Gender { get; set; }
    public string Address { get; set; } = string.Empty;
    public string NationalId { get; set; } = string.Empty;
    public string? ProfileImagePath { get; set; }
    public List<PhoneNumberData> PhoneNumbers { get; set; } = new();
    public string ActivityType { get; set; } = "MainCommercial";
    public string? CommercialRegister { get; set; }
    public string? NifNumber { get; set; }
    public string? NisNumber { get; set; }
    public string? ArtNumber { get; set; }
    public string? CpiWilayaName { get; set; }
    public string? CpiDairaName { get; set; }
    public IEnumerable<int>? G12Years { get; set; }
    public IEnumerable<int>? BISYears { get; set; }
    public List<string> Notes { get; set; } = new();
}
```

### XAML Data Binding Example

```xml
<UserControl x:Class="UFU2.Views.NewClientView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Text="{Binding IsEditingExistingClient, 
                          Converter={StaticResource BooleanToStringConverter}, 
                          ConverterParameter='تعديل عميل موجود|إضافة عميل جديد'}"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="20,10"/>
        
        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="20">
            <!-- Personal Information Tab -->
            <TabItem Header="البيانات الشخصية">
                <StackPanel Margin="20">
                    <TextBox Text="{Binding NameFr, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="الاسم بالفرنسية"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <TextBox Text="{Binding NameAr, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="الاسم بالعربية"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <TextBox Text="{Binding BirthDate, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="تاريخ الميلاد (DD/MM/YYYY)"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <TextBox Text="{Binding BirthPlace, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="مكان الميلاد"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <ComboBox SelectedIndex="{Binding Gender}"
                              materialDesign:HintAssist.Hint="الجنس"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,5">
                        <ComboBoxItem Content="ذكر"/>
                        <ComboBoxItem Content="أنثى"/>
                    </ComboBox>
                    
                    <TextBox Text="{Binding Address, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="العنوان"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <TextBox Text="{Binding NationalId, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="رقم البطاقة الوطنية"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                </StackPanel>
            </TabItem>
            
            <!-- Activity Information Tab -->
            <TabItem Header="معلومات النشاط">
                <StackPanel Margin="20">
                    <ComboBox SelectedValue="{Binding SelectedActivityType}"
                              materialDesign:HintAssist.Hint="نوع النشاط"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,5">
                        <ComboBoxItem Content="التجاري الرئيسي" Tag="MainCommercial"/>
                        <ComboBoxItem Content="التجاري الثانوي" Tag="SecondaryCommercial"/>
                        <ComboBoxItem Content="الحرفي" Tag="Craft"/>
                        <ComboBoxItem Content="المهني" Tag="Professional"/>
                    </ComboBox>
                    
                    <TextBox Text="{Binding CurrentActivity.ActivityStatus, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="حالة النشاط"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             Margin="0,5"/>
                    
                    <!-- CPI Location Selection -->
                    <ComboBox ItemsSource="{Binding CpiWilayas}"
                              SelectedItem="{Binding SelectedCpiWilaya}"
                              DisplayMemberPath="NameAr"
                              materialDesign:HintAssist.Hint="الولاية"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,5"/>
                    
                    <ComboBox ItemsSource="{Binding CpiDairas}"
                              SelectedItem="{Binding SelectedCpiDaira}"
                              DisplayMemberPath="NameAr"
                              materialDesign:HintAssist.Hint="الدائرة"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              Margin="0,5"/>
                    
                    <!-- Payment Years Display -->
                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <Button Content="اختيار سنوات G12"
                                Command="{Binding ActivityManagement.SelectG12YearsCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding G12DisplayText}"
                                   VerticalAlignment="Center"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <Button Content="اختيار سنوات BIS"
                                Command="{Binding ActivityManagement.SelectBISYearsCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,10,0"/>
                        <TextBlock Text="{Binding BISDisplayText}"
                                   VerticalAlignment="Center"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                    </StackPanel>
                </StackPanel>
            </TabItem>
            
            <!-- Notes Tab -->
            <TabItem Header="الملاحظات">
                <StackPanel Margin="20">
                    <TextBlock Text="{Binding NotesDisplayText}"
                               Style="{StaticResource MaterialDesignBody1TextBlock}"
                               TextWrapping="Wrap"
                               Margin="0,10"/>
                    
                    <Button Content="إدارة الملاحظات"
                            Command="{Binding NotesManagement.ManageNotesCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            HorizontalAlignment="Left"
                            Margin="0,10"/>
                </StackPanel>
            </TabItem>
        </TabControl>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Left" 
                    Margin="20">
            <Button Content="{Binding IsEditingExistingClient, 
                             Converter={StaticResource BooleanToStringConverter}, 
                             ConverterParameter='تحديث العميل|حفظ العميل'}"
                    Command="{Binding SaveClientCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    IsEnabled="{Binding CanSave}"
                    Margin="0,0,10,0"/>
            
            <Button Content="إلغاء"
                    Command="{Binding CloseCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
        </StackPanel>
        
        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" 
              Background="#80000000" 
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" 
                        VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                             IsIndeterminate="True"
                             Width="50" Height="50"/>
                <TextBlock Text="جاري الحفظ..."
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,10"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
```

## Integration with UFU2 Architecture

### MVVM Pattern Compliance

The NewClientViewModel exemplifies UFU2's advanced MVVM architecture:

```csharp
// Inherits from BaseViewModel for smart batching
public class NewClientViewModel : BaseViewModel
{
    // Uses SetProperty for optimized property notifications
    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            if (SetProperty(ref _isLoading, value))
            {
                // Trigger related updates
                UpdateCanSave();
            }
        }
    }
    
    // Composition pattern with component ViewModels
    public PersonalInformationViewModel PersonalInfo { get; }
    public ContactInformationViewModel ContactInfo { get; }
    public ActivityManagementViewModel ActivityManagement { get; }
    public NotesManagementViewModel NotesManagement { get; }
}
```

### Data Layer Integration

The ViewModel seamlessly bridges UI models and database transfer objects:

```csharp
// CreateClientDataFromComponents method demonstrates proper data conversion
private ClientCreationData CreateClientDataFromComponents()
{
    // Convert PhoneNumbersCollectionModel to List<PhoneNumberData>
    var phoneNumbersList = new List<PhoneNumberData>();
    if (ContactInfo.PhoneNumbers?.PhoneNumbers != null)
    {
        foreach (var phoneModel in ContactInfo.PhoneNumbers.PhoneNumbers)
        {
            phoneNumbersList.Add(new PhoneNumberData
            {
                PhoneNumber = phoneModel.PhoneNumber,
                PhoneType = phoneModel.PhoneType.ToString(), // Enum -> String conversion
                IsPrimary = phoneModel.IsPrimary
            });
        }
    }

    return new ClientCreationData
    {
        NameFr = PersonalInfo.NameFr,
        NameAr = PersonalInfo.NameAr,
        BirthDate = PersonalInfo.BirthDate,
        BirthPlace = PersonalInfo.BirthPlace,
        Gender = PersonalInfo.Gender,
        Address = PersonalInfo.Address,
        NationalId = PersonalInfo.NationalId,
        PhoneNumbers = phoneNumbersList
    };
}
```

**Key Architectural Benefits:**
- **Type Safety**: UI layer uses strongly-typed enums for ComboBox binding
- **Database Compatibility**: Database layer uses strings for flexibility and JSON serialization
- **Automatic Conversion**: ViewModel handles conversion transparently
- **Maintainability**: Changes to phone types only require enum updates

### Service Integration

```csharp
// Integration with UFU2 services through ServiceLocator
private void InitializeServices()
{
    try
    {
        // Service injection through ServiceLocator pattern
        _clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
        _uidGenerationService = ServiceLocator.GetService<UIDGenerationService>();
        _clientValidationService = ServiceLocator.GetService<ClientValidationService>();
        _clientFolderManagementService = ServiceLocator.GetService<ClientFolderManagementService>();

        LoggingService.LogDebug("Services initialized successfully", "NewClientViewModel");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error initializing services: {ex.Message}", "NewClientViewModel");
    }
}
```

### Component Event Coordination

```csharp
// Event coordination between component ViewModels
public NewClientViewModel()
{
    // Initialize component ViewModels
    PersonalInfo = new PersonalInformationViewModel();
    ContactInfo = new ContactInformationViewModel();
    ActivityManagement = new ActivityManagementViewModel();
    NotesManagement = new NotesManagementViewModel();

    // Subscribe to component property changes
    PersonalInfo.PropertyChanged += OnComponentPropertyChanged;
    ContactInfo.PropertyChanged += OnComponentPropertyChanged;
    ActivityManagement.PropertyChanged += OnComponentPropertyChanged;
    NotesManagement.PropertyChanged += OnComponentPropertyChanged;

    // Forward events from components
    ActivityManagement.EditStatusRequested += () => EditStatusRequested?.Invoke();
}
```

### Error Handling Integration

```csharp
// Uses UFU2's ErrorManager for consistent error handling
private async Task SaveClientAsync()
{
    try
    {
        IsLoading = true;
        // ... save logic
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error saving client: {ex.Message}", "NewClientViewModel");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء حفظ بيانات العميل",
            "خطأ في الحفظ",
            LogLevel.Error,
            "NewClientViewModel");
    }
    finally
    {
        IsLoading = false;
    }
}
```

## Component Architecture Diagram

The following diagram illustrates the NewClientViewModel's composition architecture and data flow:

```mermaid
graph TB
    subgraph "NewClientViewModel (Orchestrator)"
        NCV[NewClientViewModel]
        SCC[SaveClientCommand]
        CC[CloseCommand]
        IS[IsLoading State]
        CS[CanSave State]
    end

    subgraph "Component ViewModels"
        PIV[PersonalInformationViewModel]
        CIV[ContactInformationViewModel]
        AMV[ActivityManagementViewModel]
        NMV[NotesManagementViewModel]
    end

    subgraph "UI Models (Type-Safe)"
        PNM[PhoneNumberModel<br/>PhoneType: Enum]
        AM[ActivityModel]
        NM[NoteModel]
        PI[Personal Info Properties]
    end

    subgraph "Database Transfer Objects"
        PND[PhoneNumberData<br/>PhoneType: String]
        CCD[ClientCreationData]
        CUD[ClientUpdateData]
    end

    subgraph "Services Layer"
        CDS[ClientDatabaseService]
        UGS[UIDGenerationService]
        CVS[ClientValidationService]
        SL[ServiceLocator]
    end

    subgraph "Database Layer"
        DB[(SQLite Database)]
    end

    %% Component relationships
    NCV --> PIV
    NCV --> CIV
    NCV --> AMV
    NCV --> NMV

    %% UI Model relationships
    PIV --> PI
    CIV --> PNM
    AMV --> AM
    NMV --> NM

    %% Data conversion flow
    PNM -->|"PhoneType.ToString()"| PND
    PI --> CCD
    AM --> CCD
    NM --> CCD

    %% Service integration
    NCV --> SL
    SL --> CDS
    SL --> UGS
    SL --> CVS

    %% Database operations
    CDS --> DB
    CCD --> CDS
    CUD --> CDS

    %% Command flow
    SCC --> NCV
    CC --> NCV
    IS --> SCC
    CS --> SCC

    %% Styling
    classDef viewModel fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef uiModel fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dataModel fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef database fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class NCV,PIV,CIV,AMV,NMV viewModel
    class PNM,AM,NM,PI uiModel
    class PND,CCD,CUD dataModel
    class CDS,UGS,CVS,SL service
    class DB database
```

## Data Conversion Flow Diagram

The following sequence diagram shows the phone number data conversion process:

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant VM as NewClientViewModel
    participant CIV as ContactInformationViewModel
    participant PNM as PhoneNumberModel
    participant CDS as ClientDatabaseService
    participant DB as Database

    UI->>VM: User enters phone number
    VM->>CIV: Forward to ContactInfo
    CIV->>PNM: Create PhoneNumberModel
    Note over PNM: PhoneType = PhoneType.Mobile (Enum)
    
    UI->>VM: User clicks Save
    VM->>VM: CreateClientDataFromComponents()
    
    Note over VM: Data Conversion Process
    VM->>PNM: Get PhoneType enum
    VM->>VM: phoneModel.PhoneType.ToString()
    Note over VM: PhoneType.Mobile → "Mobile" (String)
    
    VM->>CDS: CreateClientAsync(clientData)
    Note over CDS: PhoneNumberData.PhoneType = "Mobile"
    CDS->>DB: INSERT with string value
    DB-->>CDS: Success
    CDS-->>VM: Client UID
    VM-->>UI: Success notification

    Note over UI,DB: Reverse process for loading existing data
    UI->>VM: Load existing client
    VM->>CDS: GetClientData(uid)
    CDS->>DB: SELECT client data
    DB-->>CDS: PhoneNumberData (PhoneType = "Mobile")
    CDS-->>VM: Client data
    VM->>VM: Convert string to enum
    Note over VM: "Mobile" → PhoneType.Mobile
    VM->>PNM: Set PhoneType enum
    VM-->>UI: Display with type-safe enum
```

## Performance Considerations

### Memory Management

The ViewModel implements proper resource disposal and event management:

```csharp
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        // Unsubscribe from component events to prevent memory leaks
        if (PersonalInfo != null)
        {
            PersonalInfo.PropertyChanged -= OnComponentPropertyChanged;
            PersonalInfo.Dispose();
        }

        if (ContactInfo != null)
        {
            ContactInfo.PropertyChanged -= OnComponentPropertyChanged;
            ContactInfo.Dispose();
        }

        if (ActivityManagement != null)
        {
            ActivityManagement.PropertyChanged -= OnComponentPropertyChanged;
            ActivityManagement.EditStatusRequested -= () => EditStatusRequested?.Invoke();
            ActivityManagement.Dispose();
        }

        if (NotesManagement != null)
        {
            NotesManagement.PropertyChanged -= OnComponentPropertyChanged;
            NotesManagement.Dispose();
        }

        LoggingService.LogDebug("NewClientViewModel disposed", "NewClientViewModel");
    }

    base.Dispose(disposing);
}
```

### Smart Property Batching

Inherits BaseViewModel's smart batching system for optimal UI performance:

- **Normal Priority**: Standard property changes batched at 16ms intervals (60 FPS)
- **High Priority**: Frequent changes batched at 8ms intervals (120 FPS)
- **Critical Priority**: Immediate notifications bypass batching

### Efficient Component Coordination

Property change coordination minimizes unnecessary notifications:

```csharp
private void OnComponentPropertyChanged(object? sender, PropertyChangedEventArgs e)
{
    // Selective property forwarding to minimize UI updates
    switch (e.PropertyName)
    {
        case nameof(PersonalInformationViewModel.NameFr):
            UpdateCanSave();
            OnPropertyChanged(nameof(NameFr));
            break;
            
        case nameof(ActivityManagementViewModel.SelectedActivityType):
            // Batch related property notifications
            OnPropertyChanged(nameof(SelectedActivityType));
            OnPropertyChanged(nameof(CurrentActivity));
            OnPropertyChanged(nameof(CurrentFileCheckStates));
            OnPropertyChanged(nameof(G12SelectedYears));
            OnPropertyChanged(nameof(BISSelectedYears));
            OnPropertyChanged(nameof(G12DisplayText));
            OnPropertyChanged(nameof(BISDisplayText));
            break;
    }
}
```

### Async Operation Management

Proper async/await patterns prevent UI blocking:

```csharp
// Non-blocking save operation with proper error handling
private async Task SaveClientAsync()
{
    if (IsLoading || !CanSave) return;

    try
    {
        IsLoading = true;

        // Request data collection from views
        RequestDataCollection?.Invoke();

        // Validate all components
        if (!PersonalInfo.IsValid() || !ContactInfo.IsValid() || 
            !ActivityManagement.IsValid() || !NotesManagement.IsValid())
        {
            LoggingService.LogWarning("Validation failed during save operation", "NewClientViewModel");
            return;
        }

        // Create client data object from component ViewModels
        var clientData = CreateClientDataFromComponents();

        // Perform database operation
        string clientUid;
        if (IsEditingExistingClient && !string.IsNullOrEmpty(ExistingClientUid))
        {
            clientUid = await UpdateExistingClientAsync(clientData);
        }
        else
        {
            clientUid = await CreateNewClientAsync(clientData);
        }

        if (!string.IsNullOrEmpty(clientUid))
        {
            LoggingService.LogInfo($"Client saved successfully: {clientUid}", "NewClientViewModel");
            
            ErrorManager.HandleSuccessToast(
                IsEditingExistingClient ? "تم تحديث بيانات العميل بنجاح" : "تم إنشاء العميل بنجاح",
                "نجح الحفظ"
            );

            CloseDialog();
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error saving client: {ex.Message}", "NewClientViewModel");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء حفظ بيانات العميل",
            "خطأ في الحفظ",
            LogLevel.Error,
            "NewClientViewModel");
    }
    finally
    {
        IsLoading = false;
    }
}
```

## Component Architecture Diagram

```mermaid
classDiagram
    class NewClientViewModel {
        -bool _isLoading
        -bool _canSave
        -bool _isEditingExistingClient
        -string _existingClientUid
        -DuplicateClientData _originalClientData
        -ClientDatabaseService _clientDatabaseService
        -UIDGenerationService _uidGenerationService
        -ClientValidationService _clientValidationService
        -ClientFolderManagementService _clientFolderManagementService
        
        +PersonalInformationViewModel PersonalInfo
        +ContactInformationViewModel ContactInfo
        +ActivityManagementViewModel ActivityManagement
        +NotesManagementViewModel NotesManagement
        +bool IsLoading
        +bool CanSave
        +string SelectedActivityType
        +bool ActivityIdFormatEnabled
        +ActivityModel CurrentActivity
        +List~ActivityTypeBaseModel~ CurrentMultipleActivities
        +FileCheckStatesModel CurrentFileCheckStates
        +string NameFr
        +string NameAr
        +string BirthDate
        +string BirthPlace
        +int Gender
        +string Address
        +string NationalId
        +PhoneNumbersCollectionModel PhoneNumbers
        +NotesCollectionModel Notes
        +List~CpiWilaya~ CpiWilayas
        +ObservableCollection~CpiDaira~ CpiDairas
        +CpiWilaya SelectedCpiWilaya
        +CpiDaira SelectedCpiDaira
        +bool IsEditingExistingClient
        +string ExistingClientUid
        +string NotesDisplayText
        +List~int~ G12SelectedYears
        +List~int~ BISSelectedYears
        +string G12DisplayText
        +string BISDisplayText
        +BitmapSource ProfileImage
        +ICommand SaveClientCommand
        +ICommand CloseCommand
        +Action RequestDataCollection
        +event Action EditStatusRequested
        
        +void RefreshNotesDisplay()
        +void SetEditingExistingClient(DuplicateClientData)
        +void ClearEditingState()
        -void InitializeServices()
        -void OnComponentPropertyChanged(object, PropertyChangedEventArgs)
        -void UpdateCanSave()
        -bool HasPersonalInfoChanges()
        -Task SaveClientAsync()
        -ClientCreationData CreateClientDataFromComponents()
        -Task~string~ CreateNewClientAsync(ClientCreationData)
        -Task~string~ UpdateExistingClientAsync(ClientCreationData)
        -void CloseDialog()
        +void Dispose(bool)
    }
    
    class BaseViewModel {
        <<abstract>>
        +PropertyPriority enum
        +UIState enum
        +BatchingStrategy enum
        #void SetProperty(ref T, T, string, PropertyPriority)
        #virtual void OnPropertyChanged(string, PropertyPriority)
        +abstract void Dispose(bool)
    }
    
    class PersonalInformationViewModel {
        +string NameFr
        +string NameAr
        +string BirthDate
        +string BirthPlace
        +int Gender
        +string Address
        +string NationalId
        +BitmapSource ProfileImage
        +string ProfileImageOriginalExtension
        +bool IsValid()
        +void Clear()
        +void LoadFromClientData(DuplicateClientData)
        +bool HasChanges(DuplicateClientData)
        +void SetProfileImage(BitmapSource)
    }
    
    class ContactInformationViewModel {
        +PhoneNumbersCollectionModel PhoneNumbers
        +bool IsValid()
        +void Clear()
    }
    
    class ActivityManagementViewModel {
        +string SelectedActivityType
        +bool ActivityIdFormatEnabled
        +ActivityModel CurrentActivity
        +List~ActivityTypeBaseModel~ CurrentMultipleActivities
        +FileCheckStatesModel CurrentFileCheckStates
        +List~CpiWilaya~ CpiWilayas
        +ObservableCollection~CpiDaira~ CpiDairas
        +CpiWilaya SelectedCpiWilaya
        +CpiDaira SelectedCpiDaira
        +List~int~ G12SelectedYears
        +List~int~ BISSelectedYears
        +string G12DisplayText
        +string BISDisplayText
        +ICommand AddActivityCommand
        +ICommand EditStatusCommand
        +ICommand SelectG12YearsCommand
        +ICommand SelectBISYearsCommand
        +event Action EditStatusRequested
        +bool IsValid()
        +void Clear()
    }
    
    class NotesManagementViewModel {
        +NotesCollectionModel Notes
        +string NotesDisplayText
        +int NotesCount
        +bool HasNotes
        +ICommand AddNoteCommand
        +ICommand EditNoteCommand
        +ICommand DeleteNoteCommand
        +ICommand ManageNotesCommand
        +void RefreshNotesDisplay()
        +bool IsValid()
        +void Clear()
        +void AddNoteWithContent(string, bool)
        +void UpdateNote(NoteModel, string, bool)
        +void RemoveNote(NoteModel)
        +List~NoteModel~ GetNotesSortedByDate()
        +List~NoteModel~ GetImportantNotes()
        +List~NoteModel~ SearchNotes(string)
    }
    
    class RelayCommand {
        +bool CanExecute(object)
        +void Execute(object)
        +event EventHandler CanExecuteChanged
    }
    
    class ClientDatabaseService {
        +Task~string~ CreateClientAsync(ClientCreationData)
        +Task UpdateClientAsync(string, ClientCreationData)
        +Task DeleteClientAsync(string)
        +Task~DuplicateClientData~ GetClientByUidAsync(string)
    }
    
    class UIDGenerationService {
        +Task~string~ GenerateClientUidAsync()
        +Task~string~ GenerateActivityUidAsync(string, string)
    }
    
    class ClientValidationService {
        +Task~ClientValidationResult~ ValidateCompleteClientDataAsync(ClientCreationData)
        +Task~bool~ ValidateNationalIdAsync(string)
        +Task~bool~ ValidatePhoneNumberAsync(string)
    }
    
    class ServiceLocator {
        <<static>>
        +void Initialize()
        +T GetService~T~()
        +void RegisterService~T~(T)
        +void DisposeServices()
    }
    
    class LoggingService {
        <<static>>
        +void LogDebug(string, string)
        +void LogInfo(string, string)
        +void LogWarning(string, string)
        +void LogError(string, string)
    }
    
    class ErrorManager {
        <<static>>
        +void HandleErrorToast(Exception, string, string, LogLevel, string)
        +void HandleSuccessToast(string, string)
    }
    
    NewClientViewModel --|> BaseViewModel : inherits
    NewClientViewModel --> PersonalInformationViewModel : contains
    NewClientViewModel --> ContactInformationViewModel : contains
    NewClientViewModel --> ActivityManagementViewModel : contains
    NewClientViewModel --> NotesManagementViewModel : contains
    NewClientViewModel --> RelayCommand : contains
    NewClientViewModel --> ClientDatabaseService : uses
    NewClientViewModel --> UIDGenerationService : uses
    NewClientViewModel --> ClientValidationService : uses
    NewClientViewModel --> ServiceLocator : uses
    NewClientViewModel --> LoggingService : uses
    NewClientViewModel --> ErrorManager : uses
    
    PersonalInformationViewModel --|> BaseViewModel : inherits
    ContactInformationViewModel --|> BaseViewModel : inherits
    ActivityManagementViewModel --|> BaseViewModel : inherits
    NotesManagementViewModel --|> BaseViewModel : inherits
    
    note for NewClientViewModel "Composition-based ViewModel\nOrchestrates component ViewModels\nHandles client creation/editing workflow\nManages validation and persistence"
    note for BaseViewModel "Provides smart property batching\nand optimized UI performance"
```

## Data Flow Diagram

```mermaid
flowchart TD
    A[User Interaction] --> B[NewClientView]
    B --> C[NewClientViewModel]
    C --> D{Component Selection}
    
    D -->|Personal Info| E[PersonalInformationViewModel]
    D -->|Contact Info| F[ContactInformationViewModel]
    D -->|Activity Info| G[ActivityManagementViewModel]
    D -->|Notes| H[NotesManagementViewModel]
    
    E --> I[Property Change Event]
    F --> I
    G --> I
    H --> I
    
    I --> J[OnComponentPropertyChanged]
    J --> K[Update Aggregated Properties]
    K --> L[UpdateCanSave]
    L --> M[UI Property Notifications]
    
    M --> N{Save Command}
    N -->|Can Save| O[SaveClientAsync]
    N -->|Cannot Save| P[Validation Error]
    
    O --> Q[Request Data Collection]
    Q --> R[Validate All Components]
    R --> S{All Valid?}
    
    S -->|Yes| T[CreateClientDataFromComponents]
    S -->|No| U[Show Validation Error]
    
    T --> V{Editing Mode?}
    V -->|New Client| W[CreateNewClientAsync]
    V -->|Existing Client| X[UpdateExistingClientAsync]
    
    W --> Y[ClientDatabaseService]
    X --> Y
    
    Y --> Z[Database Operation]
    Z --> AA{Success?}
    
    AA -->|Success| BB[Success Toast]
    AA -->|Error| CC[Error Toast]
    
    BB --> DD[CloseDialog]
    CC --> EE[Stay in Dialog]
    
    DD --> FF[Clear All Components]
    FF --> GG[Dispose Resources]
    
    subgraph "Component ViewModels"
        E
        F
        G
        H
    end
    
    subgraph "Database Layer"
        Y
        Z
    end
    
    subgraph "UI Feedback"
        BB
        CC
        U
        P
    end
```

## State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> Initializing : Constructor Called
    
    Initializing --> Ready : Components Initialized
    
    Ready --> EditingNew : New Client Mode
    Ready --> EditingExisting : SetEditingExistingClient()
    
    EditingNew --> Validating : User Input Changes
    EditingExisting --> Validating : User Input Changes
    
    Validating --> Valid : All Components Valid
    Validating --> Invalid : Validation Failed
    
    Valid --> Saving : SaveClientCommand Executed
    Invalid --> EditingNew : Continue Editing
    Invalid --> EditingExisting : Continue Editing
    
    Saving --> Success : Database Save Successful
    Saving --> Error : Database Save Failed
    
    Success --> Closing : Show Success Message
    Error --> EditingNew : Show Error, Return to Edit
    Error --> EditingExisting : Show Error, Return to Edit
    
    Closing --> Disposed : CloseDialog()
    
    EditingNew --> Disposed : CloseCommand
    EditingExisting --> Disposed : CloseCommand
    
    Disposed --> [*]
    
    note right of Initializing
        - Initialize component ViewModels
        - Subscribe to events
        - Initialize services
        - Set up commands
    end note
    
    note right of Validating
        - PersonalInfo.IsValid()
        - ContactInfo.IsValid()
        - ActivityManagement.IsValid()
        - NotesManagement.IsValid()
    end note
    
    note right of Saving
        - Set IsLoading = true
        - Request data collection
        - Create ClientCreationData
        - Call database service
    end note
```

This comprehensive documentation provides complete coverage of the NewClientViewModel implementation, demonstrating its role as the primary orchestrator for client management in the UFU2 application. The composition-based architecture with component ViewModels provides excellent maintainability and separation of concerns while maintaining the robust MVVM patterns that characterize the UFU2 codebase.
## Recen
t Changes Summary

### Phone Number Type Conversion Enhancement

**Change Made**: Modified the `CreateClientDataFromComponents()` method to properly convert `PhoneType` enum values to strings when creating `PhoneNumberData` objects.

**Code Change**:
```csharp
// Before (would cause compilation error)
PhoneType = phoneModel.PhoneType,

// After (proper enum to string conversion)
PhoneType = phoneModel.PhoneType.ToString(),
```

**Technical Benefits**:

1. **Type Safety**: UI layer maintains strongly-typed enum for ComboBox binding and validation
2. **Database Compatibility**: Database layer receives string values for flexible storage and JSON serialization
3. **Seamless Integration**: Automatic conversion between UI models and database transfer objects
4. **Maintainability**: Single source of truth for phone types in the enum definition
5. **Localization Support**: Arabic display names remain tied to enum values

**Architectural Impact**:

- **MVVM Compliance**: Maintains clean separation between UI models and data models
- **Service Layer Integration**: ClientDatabaseService receives properly formatted data
- **Error Prevention**: Eliminates type mismatch errors during database operations
- **Future-Proof**: Easy to add new phone types by extending the enum

**Testing Considerations**:

```csharp
// Validation test for phone type conversion
[TestMethod]
public void PhoneTypeConversion_ShouldMaintainDataIntegrity()
{
    // Arrange
    var phoneModel = new PhoneNumberModel("0555123456", PhoneType.Mobile);
    
    // Act
    var phoneData = new PhoneNumberData
    {
        PhoneNumber = phoneModel.PhoneNumber,
        PhoneType = phoneModel.PhoneType.ToString(),
        IsPrimary = phoneModel.IsPrimary
    };
    
    // Assert
    Assert.AreEqual("Mobile", phoneData.PhoneType);
    Assert.AreEqual("هاتف محمول", phoneData.PhoneTypeDisplayName);
    
    // Test reverse conversion
    Assert.IsTrue(Enum.TryParse<PhoneType>(phoneData.PhoneType, out var parsedType));
    Assert.AreEqual(PhoneType.Mobile, parsedType);
}
```

**Performance Impact**: Minimal - `ToString()` on enum is a lightweight operation that doesn't impact overall performance.

**Backward Compatibility**: Fully backward compatible - existing database records with string phone types continue to work without modification.

## Best Practices Demonstrated

1. **Proper Data Conversion**: Always convert between UI and database models at the ViewModel boundary
2. **Type Safety**: Use enums in UI layer for compile-time safety and IntelliSense support
3. **String Storage**: Use strings in database layer for flexibility and interoperability
4. **Validation**: Implement validation at both UI and database layers
5. **Error Handling**: Provide meaningful Arabic error messages for user-facing issues
6. **Logging**: Log conversion operations for debugging and monitoring
7. **Documentation**: Maintain comprehensive documentation for all architectural decisions

This enhancement exemplifies UFU2's commitment to robust, maintainable, and user-friendly software architecture while maintaining the highest standards of code quality and performance.