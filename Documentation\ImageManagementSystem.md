# UFU2 Image Management System Documentation

## Overview

The UFU2 Image Management System provides comprehensive WYSIWYG (What You See Is What You Get) image editing capabilities for client profile photos. The system supports real-time cropping, rotation, zoom, and drag operations with pixel-perfect accuracy, standardized 127x145 pixel output, and advanced coordinate transformation handling.

## Architecture Components

The Image Management System consists of several integrated components:

- **ImageManagementViewModel**: Core MVVM ViewModel managing image state and operations
- **InteractiveCropRectangle**: Visual crop guide control with corner markers
- **CoordinateMapper**: Handles complex coordinate transformations between UI and image space
- **TransformationValidator**: Validates operations and ensures boundary compliance
- **PreviewBackendSynchronizer**: Synchronizes UI transformations with backend processing

---

## WYSIWYG Image Editing Workflow

### Complete Image Editing Process

The UFU2 image editing workflow follows a comprehensive WYSIWYG approach:

```csharp
/// <summary>
/// Complete image editing workflow in ImageManagementViewModel
/// </summary>
public class ImageEditingWorkflow
{
    // 1. Image Loading with Original Backup
    public async Task LoadImageAsync(string imagePath)
    {
        try
        {
            var image = new BitmapImage(new Uri(imagePath));
            
            // Create backup for reset functionality
            OriginalImage = image;
            CurrentImage = image;
            
            // Initialize transformations
            ResetTransforms();
            ResetCropRectangle();
            
            // Initialize WYSIWYG backend simulation
            if (_isWysiwygEnabled)
            {
                InitializeBackendSimulation();
            }
            
            LoggingService.LogInfo($"Image loaded successfully - Dimensions: {image.PixelWidth}x{image.PixelHeight}", 
                                 "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في تحميل الصورة", "خطأ في الملف", 
                                   LogLevel.Error, "ImageLoading");
        }
    }
    
    // 2. Real-time Transformation Updates
    public void UpdateTransformation(double zoom, double rotation, Point dragOffset)
    {
        // Apply 16ms throttling for performance
        var now = DateTime.Now;
        if ((now - _lastDragUpdate).TotalMilliseconds < DragThrottleMs)
        {
            return;
        }
        
        // Update transformation properties
        ZoomPercentage = zoom;
        RotationAngle = rotation;
        ImageOffsetX = dragOffset.X;
        ImageOffsetY = dragOffset.Y;
        
        // Synchronize with WYSIWYG backend
        if (_isWysiwygEnabled && _backendSynchronizer != null)
        {
            _backendSynchronizer.SynchronizeTransformations(
                ZoomPercentage, RotationAngle, dragOffset, CropRectangle);
        }
        
        _lastDragUpdate = now;
    }
    
    // 3. WYSIWYG Cropping Operation
    public BitmapSource? CreateWYSIWYGCroppedImage()
    {
        try
        {
            // Get current transformation state
            var backendState = _backendSynchronizer.SynchronizeTransformations(
                ZoomPercentage, RotationAngle, 
                new Point(ImageOffsetX, ImageOffsetY), CropRectangle);
            
            // Map crop rectangle to exact image coordinates
            var imageCropArea = _coordinateMapper.MapCropRectangleToImageCoordinates(
                CropRectangle, backendState);
            
            // Validate transformation
            var validation = TransformationValidator.ValidateTransformation(
                new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight),
                backendState, imageCropArea);
            
            if (!validation.IsValid)
            {
                ErrorManager.ShowUserErrorToast(
                    "لا يمكن قص الصورة - المنطقة المحددة تتجاوز حدود الصورة. يرجى تعديل منطقة القص أو موضع الصورة",
                    "خطأ في القص", "ImageCropping");
                return null;
            }
            
            // Create cropped image with exact WYSIWYG accuracy
            return CreateCroppedBitmapFromValidatedArea(imageCropArea);
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"WYSIWYG cropping failed: {ex.Message}", "ImageManagementViewModel");
            return null;
        }
    }
}
```

### Image Loading and Initialization

```csharp
/// <summary>
/// Image loading with comprehensive initialization
/// </summary>
public void LoadImage(BitmapImage image)
{
    try
    {
        LoggingService.LogInfo($"Loading image - Dimensions: {image.PixelWidth}x{image.PixelHeight}", 
                             "ImageManagementViewModel");
        
        // Store original image for reset functionality
        OriginalImage = image;
        CurrentImage = image;
        
        // Reset all transformations to defaults
        ZoomPercentage = DefaultZoomPercentage; // 100.0
        RotationAngle = DefaultRotationAngle;   // 0.0
        ImageOffsetX = 0.0;
        ImageOffsetY = 0.0;
        PostCropVisualScale = 1.0;
        
        // Reset crop state
        HasBeenCropped = false;
        ResetCropRectangle(); // Default: 133, 15, 254, 290
        
        // Initialize WYSIWYG backend simulation
        if (_isWysiwygEnabled)
        {
            InitializeBackendSimulation();
        }
        
        // Update UI state
        IsImageLoaded = true;
        IsLoading = false;
        
        LoggingService.LogInfo("Image loading completed successfully", "ImageManagementViewModel");
    }
    catch (Exception ex)
    {
        ErrorManager.HandleError(ex, "خطأ في تحميل الصورة", "خطأ في معالجة الصورة", 
                               LogLevel.Error, "ImageLoading");
    }
}
```

---

## Coordinate Transformation Patterns

### Mathematical Foundation

UFU2's coordinate transformation system handles complex mappings between different coordinate spaces:

```csharp
/// <summary>
/// Coordinate transformation mathematics in CoordinateMapper
/// </summary>
public class CoordinateTransformationMath
{
    /// <summary>
    /// Transform preview coordinates to image coordinates
    /// Mathematical sequence: Preview → Backend → Image
    /// </summary>
    public Point MapPreviewToImageCoordinates(Point previewPoint, BackendTransformState state)
    {
        // Step 1: Convert preview point to backend coordinates
        var backendPoint = new Point(
            previewPoint.X * _synchronizer.BackendContainer.ScaleFactor,
            previewPoint.Y * _synchronizer.BackendContainer.ScaleFactor
        );
        
        // Step 2: Apply inverse transformations to get image coordinates
        var matrix = CreateInverseTransformationMatrix(state);
        var imagePoint = matrix.Transform(backendPoint);
        
        return imagePoint;
    }
    
    /// <summary>
    /// Create inverse transformation matrix for coordinate mapping
    /// Applies transformations in reverse order for accurate mapping
    /// </summary>
    private Matrix CreateInverseTransformationMatrix(BackendTransformState state)
    {
        var matrix = Matrix.Identity;
        
        // Backend center point for transformation origin
        var backendCenter = new Point(
            _synchronizer.BackendContainer.Width / 2,
            _synchronizer.BackendContainer.Height / 2
        );
        
        // Apply transformations in REVERSE order (inverse operations):
        
        // 1. Translate from backend center to origin
        matrix.Translate(-backendCenter.X, -backendCenter.Y);
        
        // 2. Apply drag offset (inverse)
        matrix.Translate(-state.DragOffset.X, -state.DragOffset.Y);
        
        // 3. Apply rotation (inverse)
        if (Math.Abs(state.RotationAngle) > FloatingPointTolerance)
        {
            matrix.Rotate(-state.RotationAngle);
        }
        
        // 4. Apply zoom scale (inverse)
        if (Math.Abs(state.ZoomScale) > FloatingPointTolerance)
        {
            matrix.Scale(1.0 / state.ZoomScale, 1.0 / state.ZoomScale);
        }
        
        // 5. Translate to image center
        var imageCenter = new Point(
            _synchronizer.OriginalImageSize.Width / 2,
            _synchronizer.OriginalImageSize.Height / 2
        );
        matrix.Translate(imageCenter.X, imageCenter.Y);
        
        return matrix;
    }
    
    /// <summary>
    /// Special crop rectangle mapping (excludes rotation)
    /// Crop rectangles maintain their orientation regardless of image rotation
    /// </summary>
    public Rect MapCropRectangleToImageCoordinates(Rect cropRect, BackendTransformState state)
    {
        // Create transformation matrix WITHOUT rotation
        var matrix = CreateInverseTransformationMatrixWithoutRotation(state);
        
        // Transform crop rectangle corners
        var topLeft = matrix.Transform(new Point(cropRect.Left, cropRect.Top));
        var bottomRight = matrix.Transform(new Point(cropRect.Right, cropRect.Bottom));
        
        // Create mapped rectangle
        var mappedRect = new Rect(topLeft, bottomRight);
        
        LoggingService.LogDebug($"Crop rectangle mapped: {cropRect} → {mappedRect}", "CoordinateMapper");
        
        return mappedRect;
    }
}
```

### Coordinate Space Relationships

```mermaid
graph TB
    subgraph "Coordinate Spaces"
        UI[UI Preview Space<br/>500x320 container]
        Backend[Backend Space<br/>Scaled container]
        Image[Image Space<br/>Original pixel coordinates]
    end
    
    subgraph "Transformations Applied"
        T1[1. Scale Factor]
        T2[2. Drag Offset]
        T3[3. Rotation]
        T4[4. Zoom Scale]
        T5[5. Center Translation]
    end
    
    subgraph "Special Cases"
        Crop[Crop Rectangle<br/>No rotation applied]
        Point[Point Mapping<br/>Full transformation]
    end
    
    UI -->|Forward Transform| T1
    T1 --> T2
    T2 --> T3
    T3 --> T4
    T4 --> T5
    T5 --> Backend
    
    Backend -->|Inverse Transform| Image
    
    UI -.->|Crop Mapping| Crop
    Crop -.->|Skip Rotation| Image
    
    UI -.->|Point Mapping| Point
    Point -.->|Full Transform| Image
    
    style UI fill:#e1f5fe
    style Backend fill:#f3e5f5
    style Image fill:#e8f5e8
    style Crop fill:#fff3e0
```

---

## Cropping Implementation Details

### 127x145 Pixel Standardized Output

UFU2 enforces a standardized aspect ratio and output size for all profile images:

```csharp
/// <summary>
/// Standardized cropping with 127x145 pixel output
/// </summary>
public class StandardizedCropping
{
    // Target dimensions for profile images
    private const int TargetWidth = 127;
    private const int TargetHeight = 145;
    private const double TargetAspectRatio = 127.0 / 145.0; // ≈ 0.876
    
    /// <summary>
    /// Create standardized cropped image with exact 127x145 output
    /// </summary>
    public BitmapSource CreateStandardizedCroppedImage(Rect imageCropArea)
    {
        try
        {
            // Step 1: Extract crop area from original image
            var croppedBitmap = new CroppedBitmap(CurrentImage,
                new Int32Rect(
                    (int)Math.Max(0, imageCropArea.X),
                    (int)Math.Max(0, imageCropArea.Y),
                    (int)Math.Min(CurrentImage.PixelWidth - imageCropArea.X, imageCropArea.Width),
                    (int)Math.Min(CurrentImage.PixelHeight - imageCropArea.Y, imageCropArea.Height)));
            
            // Step 2: Scale to exact 127x145 dimensions
            var scaledBitmap = new TransformedBitmap(croppedBitmap,
                new ScaleTransform(
                    TargetWidth / imageCropArea.Width,
                    TargetHeight / imageCropArea.Height));
            
            // Step 3: Ensure exact pixel dimensions
            var renderTarget = new RenderTargetBitmap(
                TargetWidth, TargetHeight, 96, 96, PixelFormats.Pbgra32);
            
            var drawingVisual = new DrawingVisual();
            using (var drawingContext = drawingVisual.RenderOpen())
            {
                drawingContext.DrawImage(scaledBitmap, new Rect(0, 0, TargetWidth, TargetHeight));
            }
            
            renderTarget.Render(drawingVisual);
            renderTarget.Freeze();
            
            LoggingService.LogInfo($"Standardized crop completed - Output: {TargetWidth}x{TargetHeight}", 
                                 "ImageManagementViewModel");
            
            return renderTarget;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Standardized cropping failed: {ex.Message}", "ImageManagementViewModel");
            throw;
        }
    }
}
```

### Crop Rectangle Management

```csharp
/// <summary>
/// Interactive crop rectangle management
/// </summary>
public class CropRectangleManagement
{
    // Default crop rectangle (centered in 500x320 preview)
    private Rect _defaultCropRectangle = new Rect(133, 15, 254, 290);
    
    /// <summary>
    /// Crop rectangle with validation and aspect ratio maintenance
    /// </summary>
    public Rect CropRectangle
    {
        get => _cropRectangle;
        set
        {
            if (SetProperty(ref _cropRectangle, value))
            {
                LoggingService.LogDebug($"Crop rectangle updated: {value}", "ImageManagementViewModel");
                
                // Validate crop rectangle bounds and aspect ratio
                ValidateCropRectangle();
                
                // Update WYSIWYG backend if enabled
                if (_isWysiwygEnabled && _backendSynchronizer != null)
                {
                    _backendSynchronizer.SynchronizeTransformations(
                        ZoomPercentage, RotationAngle, 
                        new Point(ImageOffsetX, ImageOffsetY), value);
                }
            }
        }
    }
    
    /// <summary>
    /// Validate crop rectangle maintains proper aspect ratio and bounds
    /// </summary>
    private void ValidateCropRectangle()
    {
        try
        {
            // Check aspect ratio (127:145)
            var currentAspectRatio = _cropRectangle.Width / _cropRectangle.Height;
            var targetAspectRatio = 127.0 / 145.0;
            
            if (Math.Abs(currentAspectRatio - targetAspectRatio) > 0.001)
            {
                LoggingService.LogWarning($"Crop rectangle aspect ratio deviation: {currentAspectRatio:F3} vs {targetAspectRatio:F3}", 
                                        "ImageManagementViewModel");
            }
            
            // Check bounds within preview container (500x320)
            if (_cropRectangle.Right > 500 || _cropRectangle.Bottom > 320)
            {
                LoggingService.LogWarning($"Crop rectangle exceeds preview bounds: {_cropRectangle}", 
                                        "ImageManagementViewModel");
            }
            
            // Check minimum size requirements
            if (_cropRectangle.Width < 50 || _cropRectangle.Height < 50)
            {
                LoggingService.LogWarning($"Crop rectangle below minimum size: {_cropRectangle}", 
                                        "ImageManagementViewModel");
            }
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Crop rectangle validation failed: {ex.Message}", "ImageManagementViewModel");
        }
    }
    
    /// <summary>
    /// Reset crop rectangle to default position
    /// </summary>
    public void ResetCropRectangle()
    {
        CropRectangle = _defaultCropRectangle;
        LoggingService.LogDebug("Crop rectangle reset to default position", "ImageManagementViewModel");
    }
}
```

---

## Performance Optimization Strategies

### 16ms Throttling Implementation

UFU2 implements sophisticated throttling to maintain 60 FPS performance during interactive operations:

```csharp
/// <summary>
/// Performance optimization with 16ms throttling
/// </summary>
public class PerformanceOptimization
{
    // Throttling constants for different operations
    private const int DragThrottleMs = 16;        // ~60 FPS for smooth dragging
    private const int SliderDebounceMs = 50;      // Minimum time between performance logs
    private const int BoundaryLogThrottleMs = 500; // Boundary calculation logging throttle

    // Performance monitoring fields
    private DateTime _lastDragUpdate = DateTime.MinValue;
    private DateTime _lastSliderUpdate = DateTime.MinValue;
    private Stopwatch? _sliderOperationStopwatch;

    /// <summary>
    /// Throttled drag update with performance monitoring
    /// </summary>
    public void UpdateDrag(Point mousePosition)
    {
        try
        {
            if (!IsDragging || !IsImageLoaded)
                return;

            // Apply 16ms throttling for 60 FPS performance
            var now = DateTime.Now;
            if ((now - _lastDragUpdate).TotalMilliseconds < DragThrottleMs)
            {
                return; // Skip this update to maintain performance
            }

            // Calculate position delta
            var deltaX = mousePosition.X - _lastMousePosition.X;
            var deltaY = mousePosition.Y - _lastMousePosition.Y;

            // Apply boundary constraints
            var newOffsetX = ImageOffsetX + deltaX;
            var newOffsetY = ImageOffsetY + deltaY;
            var constrainedOffsets = ApplyDragBoundaryConstraints(newOffsetX, newOffsetY);

            // Update image position
            ImageOffsetX = constrainedOffsets.X;
            ImageOffsetY = constrainedOffsets.Y;

            // Update WYSIWYG backend with throttled synchronization
            if (_isWysiwygEnabled && _backendSynchronizer != null)
            {
                _backendSynchronizer.SynchronizeTransformations(
                    ZoomPercentage, RotationAngle,
                    new Point(ImageOffsetX, ImageOffsetY), CropRectangle);
            }

            // Update tracking variables
            _lastMousePosition = mousePosition;
            _lastDragUpdate = now;

            LoggingService.LogDebugLazy(() =>
                $"Drag updated - Delta: ({deltaX:F1}, {deltaY:F1}), Position: ({ImageOffsetX:F1}, {ImageOffsetY:F1})",
                "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error in throttled drag update: {ex.Message}", "ImageManagementViewModel");
        }
    }

    /// <summary>
    /// Performance-optimized slider operation logging
    /// </summary>
    private void LogSliderOperation(string propertyName, double value, string unit)
    {
        try
        {
            var now = DateTime.Now;

            // Debounce logging to prevent excessive log entries
            if ((now - _lastSliderUpdate).TotalMilliseconds >= SliderDebounceMs)
            {
                // Use lazy logging for performance
                LoggingService.LogDebugLazy(() =>
                    $"{propertyName} changed to: {value:F1}{unit}", "ImageManagementViewModel");
                _lastSliderUpdate = now;
            }
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error logging slider operation: {ex.Message}", "ImageManagementViewModel");
        }
    }

    /// <summary>
    /// Complete slider performance monitoring
    /// </summary>
    private void CompleteSliderPerformanceMonitoring(string operationName)
    {
        try
        {
            if (_sliderOperationStopwatch != null)
            {
                _sliderOperationStopwatch.Stop();
                var elapsedMs = _sliderOperationStopwatch.ElapsedMilliseconds;

                // Log performance warnings for slow operations
                if (elapsedMs > 50) // Warn if operation takes more than 50ms
                {
                    LoggingService.LogWarning($"Slow {operationName} operation: {elapsedMs}ms",
                                            "ImageManagementViewModel");
                }

                _sliderOperationStopwatch = null;
            }
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error completing performance monitoring: {ex.Message}", "ImageManagementViewModel");
        }
    }
}
```

### Image Processing Optimizations

```csharp
/// <summary>
/// Memory-efficient image processing optimizations
/// </summary>
public class ImageProcessingOptimizations
{
    /// <summary>
    /// Optimized bitmap creation with memory management
    /// </summary>
    public BitmapSource CreateOptimizedBitmap(BitmapSource source, Rect cropArea)
    {
        try
        {
            // Step 1: Create cropped bitmap with precise bounds
            var croppedBitmap = new CroppedBitmap(source,
                new Int32Rect(
                    (int)Math.Max(0, cropArea.X),
                    (int)Math.Max(0, cropArea.Y),
                    (int)Math.Min(source.PixelWidth - cropArea.X, cropArea.Width),
                    (int)Math.Min(source.PixelHeight - cropArea.Y, cropArea.Height)));

            // Step 2: Freeze bitmap for performance and thread safety
            croppedBitmap.Freeze();

            // Step 3: Create render target with optimal pixel format
            var renderTarget = new RenderTargetBitmap(
                127, 145, // Standardized dimensions
                96, 96,   // Standard DPI
                PixelFormats.Pbgra32); // Optimal pixel format

            // Step 4: Use DrawingVisual for efficient rendering
            var drawingVisual = new DrawingVisual();
            using (var drawingContext = drawingVisual.RenderOpen())
            {
                drawingContext.DrawImage(croppedBitmap, new Rect(0, 0, 127, 145));
            }

            // Step 5: Render and freeze final bitmap
            renderTarget.Render(drawingVisual);
            renderTarget.Freeze();

            LoggingService.LogDebug($"Optimized bitmap created - Size: {renderTarget.PixelWidth}x{renderTarget.PixelHeight}",
                                  "ImageProcessingOptimizations");

            return renderTarget;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Optimized bitmap creation failed: {ex.Message}", "ImageProcessingOptimizations");
            throw;
        }
    }
}
```

---

## Integration with UFU2 Architecture

### ServiceLocator Integration

```csharp
/// <summary>
/// ImageManagementViewModel with proper UFU2 service integration
/// </summary>
public class ImageManagementViewModel : BaseViewModel
{
    #region Service Dependencies

    private readonly ErrorManager _errorManager;
    private readonly LoggingService _loggingService;

    #endregion

    #region Constructor with Service Injection

    /// <summary>
    /// Constructor with ServiceLocator dependency injection
    /// </summary>
    public ImageManagementViewModel()
    {
        try
        {
            // Initialize services through ServiceLocator
            _errorManager = ServiceLocator.GetService<ErrorManager>();
            _loggingService = ServiceLocator.GetService<LoggingService>();

            // Verify service availability
            if (_errorManager == null || _loggingService == null)
            {
                throw new InvalidOperationException("Required services not available through ServiceLocator");
            }

            // Initialize commands with proper naming for debugging
            InitializeCommands();

            // Initialize WYSIWYG system
            InitializeWysiwygSystem();

            LoggingService.LogInfo("ImageManagementViewModel initialized successfully", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Failed to initialize ImageManagementViewModel: {ex.Message}", "ImageManagementViewModel");
            throw;
        }
    }

    #endregion

    #region Command Initialization

    private void InitializeCommands()
    {
        // Initialize commands with proper RelayCommand pattern
        CropCommand = new RelayCommand(ExecuteCrop, CanExecuteCrop, "CropImage");
        ResetCommand = new RelayCommand(ExecuteReset, CanExecuteReset, "ResetImage");
        SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave, "SaveImage");

        LoggingService.LogDebug("Image management commands initialized", "ImageManagementViewModel");
    }

    #endregion

    #region Error Handling Integration

    /// <summary>
    /// Standardized error handling with Arabic messages
    /// </summary>
    private void HandleImageOperationError(Exception ex, string operation, string arabicMessage)
    {
        try
        {
            // Log technical details
            LoggingService.LogError($"Image operation failed - {operation}: {ex.Message}", "ImageManagementViewModel");

            // Show user-friendly Arabic error message
            ErrorManager.HandleError(ex, arabicMessage, "خطأ في معالجة الصورة",
                                   LogLevel.Error, "ImageManagement");

            // Reset to safe state if necessary
            if (operation == "Crop" && OriginalImage != null)
            {
                ResetToOriginal();
            }
        }
        catch (Exception handlingEx)
        {
            LoggingService.LogError($"Error handling failed: {handlingEx.Message}", "ImageManagementViewModel");
        }
    }

    #endregion
}
```

### ErrorManager Integration

```csharp
/// <summary>
/// Comprehensive error handling with Arabic localization
/// </summary>
public class ImageErrorHandling
{
    /// <summary>
    /// Handle crop operation errors with specific Arabic messages
    /// </summary>
    private void HandleCropError(Exception ex, string context)
    {
        var arabicMessage = context switch
        {
            "BoundaryViolation" => "لا يمكن قص الصورة - المنطقة المحددة تتجاوز حدود الصورة. يرجى تعديل منطقة القص أو موضع الصورة",
            "InvalidDimensions" => "أبعاد منطقة القص غير صحيحة. يرجى التأكد من أن منطقة القص ضمن حدود الصورة",
            "TransformationFailed" => "فشل في تطبيق التحويلات على الصورة. يرجى إعادة تعيين الصورة والمحاولة مرة أخرى",
            "MemoryError" => "لا توجد ذاكرة كافية لمعالجة الصورة. يرجى إغلاق التطبيقات الأخرى والمحاولة مرة أخرى",
            _ => "حدث خطأ أثناء قص الصورة. يرجى المحاولة مرة أخرى"
        };

        ErrorManager.HandleError(ex, arabicMessage, "خطأ في قص الصورة",
                               LogLevel.Error, "ImageCropping");
    }

    /// <summary>
    /// Handle transformation validation errors
    /// </summary>
    private void HandleValidationError(ValidationResult validation)
    {
        if (!validation.IsValid)
        {
            var errorMessages = validation.Errors.Select(e => e.Value).ToList();
            var combinedMessage = string.Join("\n", errorMessages);

            ErrorManager.ShowUserErrorToast(combinedMessage, "خطأ في التحقق", "ImageValidation");

            LoggingService.LogWarning($"Image validation failed: {combinedMessage}", "ImageManagementViewModel");
        }
    }
}
```

---

## Real-time Preview System

### WYSIWYG Preview Implementation

```csharp
/// <summary>
/// Real-time WYSIWYG preview system with backend synchronization
/// </summary>
public class WysiwygPreviewSystem
{
    private PreviewBackendSynchronizer? _backendSynchronizer;
    private CoordinateMapper? _coordinateMapper;
    private bool _isWysiwygEnabled = true;

    /// <summary>
    /// Initialize WYSIWYG backend simulation system
    /// </summary>
    public void InitializeBackendSimulation()
    {
        try
        {
            if (CurrentImage == null)
            {
                LoggingService.LogWarning("Cannot initialize backend simulation without loaded image", "ImageManagementViewModel");
                return;
            }

            // Create backend synchronizer with current image
            _backendSynchronizer = new PreviewBackendSynchronizer(
                new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight),
                new Size(500, 320)); // Preview container size

            // Create coordinate mapper
            _coordinateMapper = new CoordinateMapper(_backendSynchronizer);

            // Initial synchronization
            _backendSynchronizer.SynchronizeTransformations(
                ZoomPercentage, RotationAngle,
                new Point(ImageOffsetX, ImageOffsetY), CropRectangle);

            LoggingService.LogInfo("WYSIWYG backend simulation initialized successfully", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Failed to initialize WYSIWYG backend simulation: {ex.Message}", "ImageManagementViewModel");
            _isWysiwygEnabled = false;
        }
    }

    /// <summary>
    /// Update preview in real-time with transformation changes
    /// </summary>
    public void UpdatePreviewTransformation(double zoom, double rotation, Point dragOffset, Rect cropRect)
    {
        try
        {
            if (!_isWysiwygEnabled || _backendSynchronizer == null)
                return;

            // Synchronize transformations with backend
            var backendState = _backendSynchronizer.SynchronizeTransformations(
                zoom, rotation, dragOffset, cropRect);

            // Update coordinate mapping
            if (_coordinateMapper != null)
            {
                // Test coordinate mapping accuracy
                var testPoint = new Point(cropRect.X + cropRect.Width / 2, cropRect.Y + cropRect.Height / 2);
                var mappedPoint = _coordinateMapper.MapPreviewToImageCoordinates(testPoint, backendState);

                LoggingService.LogDebugLazy(() =>
                    $"Preview updated - Center: {testPoint} → Image: {mappedPoint}", "WysiwygPreviewSystem");
            }
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Preview transformation update failed: {ex.Message}", "WysiwygPreviewSystem");
        }
    }
}
```

### Sequential Crop Operations

```csharp
/// <summary>
/// Sequential crop operations with original image reference preservation
/// </summary>
public class SequentialCropOperations
{
    /// <summary>
    /// Execute crop operation while preserving original image reference
    /// </summary>
    public async Task ExecuteCropAsync()
    {
        try
        {
            if (CurrentImage == null || OriginalImage == null)
            {
                ErrorManager.ShowUserErrorToast("لا توجد صورة محملة للقص", "خطأ في القص", "ImageCropping");
                return;
            }

            LoggingService.LogInfo("Starting sequential crop operation", "ImageManagementViewModel");

            // Create cropped image using WYSIWYG system
            var croppedImage = CreateWYSIWYGCroppedImage();

            if (croppedImage == null)
            {
                ErrorManager.ShowUserErrorToast(
                    "فشل في قص الصورة. يرجى التحقق من موضع منطقة القص والمحاولة مرة أخرى",
                    "خطأ في القص", "ImageCropping");
                return;
            }

            // Convert to BitmapImage for UI display
            var croppedBitmapImage = ConvertToBitmapImage(croppedImage);

            // Replace current image with cropped result
            CurrentImage = croppedBitmapImage;

            // Reset transformations for new cropped image
            ZoomPercentage = 100.0;
            RotationAngle = 0.0;
            ImageOffsetX = 0.0;
            ImageOffsetY = 0.0;

            // Apply post-crop visual fit (90% zoom for better visibility)
            ApplyPostCropVisualFit(croppedBitmapImage);

            // Reset crop rectangle for potential subsequent crops
            ResetCropRectangle();

            // Mark as cropped and reinitialize WYSIWYG system
            HasBeenCropped = true;

            if (_isWysiwygEnabled)
            {
                InitializeBackendSimulation();
            }

            // Show success message
            ErrorManager.ShowUserSuccessToast("تم قص الصورة بنجاح", "نجح القص", "ImageCropping");

            LoggingService.LogInfo("Sequential crop operation completed successfully", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            HandleImageOperationError(ex, "Crop", "حدث خطأ أثناء قص الصورة. يرجى المحاولة مرة أخرى");
        }
    }

    /// <summary>
    /// Apply post-crop visual fit with 90% zoom for better visibility
    /// </summary>
    private void ApplyPostCropVisualFit(BitmapImage croppedImage)
    {
        try
        {
            // Apply 90% zoom for better post-crop visibility
            PostCropVisualScale = 0.9;

            LoggingService.LogDebug($"Applied post-crop visual fit - Scale: {PostCropVisualScale}", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Post-crop visual fit failed: {ex.Message}", "ImageManagementViewModel");
        }
    }
}
```

---

## Error Handling and Validation

### TransformationValidator Integration

```csharp
/// <summary>
/// Comprehensive transformation validation with boundary checking
/// </summary>
public class TransformationValidation
{
    /// <summary>
    /// Validate transformation before crop operation
    /// </summary>
    public ValidationResult ValidateBeforeCrop()
    {
        try
        {
            if (CurrentImage == null || _backendSynchronizer == null || _coordinateMapper == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Errors = { { "SystemState", "النظام غير جاهز لعملية القص" } }
                };
            }

            // Get current transformation state
            var backendState = _backendSynchronizer.SynchronizeTransformations(
                ZoomPercentage, RotationAngle,
                new Point(ImageOffsetX, ImageOffsetY), CropRectangle);

            // Map crop rectangle to image coordinates
            var imageCropArea = _coordinateMapper.MapCropRectangleToImageCoordinates(
                CropRectangle, backendState);

            // Validate transformation
            var validation = TransformationValidator.ValidateTransformation(
                new Size(CurrentImage.PixelWidth, CurrentImage.PixelHeight),
                backendState, imageCropArea);

            LoggingService.LogDebug($"Transformation validation result: {validation.IsValid}", "ImageManagementViewModel");

            return validation;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Transformation validation failed: {ex.Message}", "ImageManagementViewModel");
            return new ValidationResult
            {
                IsValid = false,
                Errors = { { "ValidationError", "فشل في التحقق من صحة التحويلات" } }
            };
        }
    }

    /// <summary>
    /// Handle boundary violation errors with specific Arabic messages
    /// </summary>
    public void HandleBoundaryViolation(ValidationResult validation)
    {
        if (!validation.IsValid)
        {
            var boundaryErrors = validation.Errors.Where(e => e.Key.Contains("Boundary")).ToList();

            if (boundaryErrors.Any())
            {
                var errorMessage = "لا يمكن قص الصورة - المنطقة المحددة تتجاوز حدود الصورة. يرجى تعديل منطقة القص أو موضع الصورة";

                ErrorManager.ShowUserErrorToast(errorMessage, "خطأ في حدود القص", "BoundaryValidation");

                LoggingService.LogWarning($"Boundary violation detected: {string.Join(", ", boundaryErrors.Select(e => e.Value))}",
                                        "ImageManagementViewModel");
            }
        }
    }
}
```

### Arabic Error Messages

```csharp
/// <summary>
/// Comprehensive Arabic error message handling for image operations
/// </summary>
public class ArabicErrorMessages
{
    /// <summary>
    /// Get appropriate Arabic error message based on operation context
    /// </summary>
    public string GetArabicErrorMessage(string operation, Exception ex)
    {
        return operation switch
        {
            "ImageLoading" => ex switch
            {
                FileNotFoundException => "لم يتم العثور على ملف الصورة المحدد",
                UnauthorizedAccessException => "ليس لديك صلاحية للوصول إلى ملف الصورة",
                OutOfMemoryException => "الصورة كبيرة جداً ولا توجد ذاكرة كافية لتحميلها",
                _ => "حدث خطأ أثناء تحميل الصورة. يرجى التأكد من صحة الملف"
            },

            "ImageCropping" => ex switch
            {
                ArgumentException => "أبعاد منطقة القص غير صحيحة",
                InvalidOperationException => "لا يمكن تنفيذ عملية القص في الوضع الحالي",
                OutOfMemoryException => "لا توجد ذاكرة كافية لمعالجة الصورة",
                _ => "حدث خطأ أثناء قص الصورة. يرجى المحاولة مرة أخرى"
            },

            "ImageTransformation" => ex switch
            {
                ArgumentOutOfRangeException => "قيم التحويل خارج النطاق المسموح",
                InvalidOperationException => "لا يمكن تطبيق التحويل على الصورة الحالية",
                _ => "حدث خطأ أثناء تطبيق التحويلات على الصورة"
            },

            "ImageSaving" => ex switch
            {
                UnauthorizedAccessException => "ليس لديك صلاحية لحفظ الصورة في المجلد المحدد",
                DirectoryNotFoundException => "المجلد المحدد للحفظ غير موجود",
                IOException => "حدث خطأ أثناء كتابة ملف الصورة",
                _ => "حدث خطأ أثناء حفظ الصورة"
            },

            _ => "حدث خطأ غير متوقع في معالجة الصورة"
        };
    }
}
```

---

## Reset Functionality

### Reset Button Behavior

```csharp
/// <summary>
/// Comprehensive reset functionality with state management
/// </summary>
public class ResetFunctionality
{
    /// <summary>
    /// Determines if reset button should be enabled
    /// Reset is enabled when any changes have been made to the image
    /// </summary>
    public bool CanReset
    {
        get => IsImageLoaded && HasChanges && OriginalImage != null;
    }

    /// <summary>
    /// Tracks if any changes have been made to the image
    /// </summary>
    public bool HasChanges
    {
        get => HasBeenCropped ||
               Math.Abs(ZoomPercentage - DefaultZoomPercentage) > 0.1 ||
               Math.Abs(RotationAngle - DefaultRotationAngle) > 0.1 ||
               Math.Abs(ImageOffsetX) > 0.1 ||
               Math.Abs(ImageOffsetY) > 0.1;
    }

    /// <summary>
    /// Reset image to original state with comprehensive restoration
    /// </summary>
    public void ResetToOriginal()
    {
        try
        {
            LoggingService.LogInfo("Starting reset to original image operation", "ImageManagementViewModel");

            // Validate original image availability
            if (OriginalImage == null)
            {
                LoggingService.LogWarning("No original image backup available for reset", "ImageManagementViewModel");
                ErrorManager.ShowUserErrorToast(
                    "لا توجد نسخة احتياطية من الصورة الأصلية للاستعادة.",
                    "لا توجد صورة أصلية", "ImageReset");
                return;
            }

            // Restore original image
            CurrentImage = OriginalImage;

            // Reset all transformations to defaults
            ZoomPercentage = DefaultZoomPercentage;  // 100.0
            RotationAngle = DefaultRotationAngle;    // 0.0
            ImageOffsetX = 0.0;
            ImageOffsetY = 0.0;
            PostCropVisualScale = 1.0;

            // Reset crop state
            HasBeenCropped = false;
            ResetCropRectangle();

            // Clear drag state
            ResetDragPosition();

            // Reinitialize WYSIWYG system with original image
            if (_isWysiwygEnabled)
            {
                InitializeBackendSimulation();
            }

            // Show success notification
            ErrorManager.ShowUserSuccessToast("تم استعادة الصورة الأصلية بنجاح", "تمت الاستعادة", "ImageReset");

            LoggingService.LogInfo("Successfully reset to original image with all transformations cleared", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error resetting to original image: {ex.Message}", "ImageManagementViewModel");
            ErrorManager.HandleError(ex, "حدث خطأ أثناء إعادة تعيين الصورة إلى حالتها الأصلية",
                                   "خطأ في إعادة التعيين", LogLevel.Error, "ImageReset");
        }
    }

    /// <summary>
    /// Reset transformations only (keep current image)
    /// </summary>
    public void ResetTransforms()
    {
        try
        {
            LoggingService.LogInfo("Resetting image transforms to default values", "ImageManagementViewModel");

            // Reset transformation values
            ZoomPercentage = DefaultZoomPercentage;
            RotationAngle = DefaultRotationAngle;

            // Reset position
            ResetDragPosition();

            // Reset visual scaling
            PostCropVisualScale = 1.0;

            LoggingService.LogInfo("Image transforms reset successfully", "ImageManagementViewModel");
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error resetting transforms: {ex.Message}", "ImageManagementViewModel");
            ErrorManager.HandleError(ex, "حدث خطأ أثناء إعادة تعيين تحويلات الصورة",
                                   "خطأ في إعادة التعيين", LogLevel.Warning, "TransformReset");
        }
    }

    /// <summary>
    /// Reset drag position to center
    /// </summary>
    public void ResetDragPosition()
    {
        try
        {
            LoggingService.LogDebug("Resetting image drag position to center", "ImageManagementViewModel");

            ImageOffsetX = 0.0;
            ImageOffsetY = 0.0;
            IsDragging = false;

            // Clear drag tracking state
            _lastMousePosition = new Point(0, 0);
            _dragStartPosition = new Point(0, 0);
            _dragStartOffsetX = 0.0;
            _dragStartOffsetY = 0.0;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error resetting drag position: {ex.Message}", "ImageManagementViewModel");
        }
    }
}
```

---

## Image Processing Workflow

### Complete Image Management Workflow

```mermaid
graph TB
    subgraph "Image Loading Phase"
        Load[Load Image File]
        Backup[Create Original Backup]
        Init[Initialize WYSIWYG System]
        Reset[Reset Transformations]
    end

    subgraph "Interactive Editing Phase"
        Drag[Drag Image<br/>16ms Throttling]
        Zoom[Zoom Control<br/>25%-400%]
        Rotate[Rotation Control<br/>-180° to +180°]
        Preview[Real-time Preview<br/>WYSIWYG Updates]
    end

    subgraph "Validation Phase"
        Validate[TransformationValidator]
        Boundary[Boundary Check]
        AspectRatio[Aspect Ratio Check<br/>127:145]
        MinSize[Minimum Size Check]
    end

    subgraph "Cropping Phase"
        MapCoords[Map Coordinates<br/>UI → Image Space]
        CreateCrop[Create Cropped Bitmap<br/>127x145 Output]
        PostProcess[Post-Crop Processing<br/>90% Visual Scale]
        UpdateUI[Update Current Image]
    end

    subgraph "Reset/Save Phase"
        CheckChanges{Has Changes?}
        EnableReset[Enable Reset Button]
        SaveImage[Save Final Image]
        ShowSuccess[Show Success Toast]
    end

    %% Workflow connections
    Load --> Backup
    Backup --> Init
    Init --> Reset
    Reset --> Drag

    Drag --> Preview
    Zoom --> Preview
    Rotate --> Preview
    Preview --> Validate

    Validate --> Boundary
    Boundary --> AspectRatio
    AspectRatio --> MinSize

    MinSize -->|Valid| MapCoords
    MinSize -->|Invalid| ShowError[Show Arabic Error]

    MapCoords --> CreateCrop
    CreateCrop --> PostProcess
    PostProcess --> UpdateUI
    UpdateUI --> CheckChanges

    CheckChanges -->|Yes| EnableReset
    CheckChanges -->|No| SaveImage
    EnableReset --> SaveImage
    SaveImage --> ShowSuccess

    %% Error handling
    ShowError --> Preview

    %% Reset flow
    EnableReset -.->|Reset Action| Backup

    style Load fill:#e1f5fe
    style Preview fill:#f3e5f5
    style Validate fill:#fff3e0
    style CreateCrop fill:#e8f5e8
    style ShowError fill:#ffebee
```

### Coordinate Transformation Flow

```mermaid
sequenceDiagram
    participant UI as UI Preview (500x320)
    participant VM as ImageManagementViewModel
    participant BS as BackendSynchronizer
    participant CM as CoordinateMapper
    participant TV as TransformationValidator
    participant IMG as Image Processing

    UI->>VM: User Interaction (Drag/Zoom/Rotate)
    VM->>VM: Apply 16ms Throttling

    alt Throttling Passed
        VM->>BS: SynchronizeTransformations()
        BS->>BS: Calculate Backend State
        BS->>VM: Return BackendTransformState

        VM->>CM: MapCropRectangleToImageCoordinates()
        CM->>CM: Apply Inverse Transformations
        Note over CM: 1. Scale Factor<br/>2. Drag Offset (inverse)<br/>3. Skip Rotation<br/>4. Zoom Scale (inverse)<br/>5. Center Translation
        CM->>VM: Return Image Coordinates

        VM->>TV: ValidateTransformation()
        TV->>TV: Check Boundaries & Aspect Ratio

        alt Validation Successful
            TV->>VM: ValidationResult (Valid)
            VM->>IMG: CreateWYSIWYGCroppedImage()
            IMG->>IMG: Process 127x145 Output
            IMG->>VM: Return Cropped Bitmap
            VM->>UI: Update Preview
        else Validation Failed
            TV->>VM: ValidationResult (Invalid)
            VM->>VM: Show Arabic Error Message
            VM->>UI: Display Error Toast
        end
    else Throttling Blocked
        VM->>VM: Skip Update (Performance)
    end
```

---

## Best Practices and Guidelines

### Performance Best Practices

1. **16ms Throttling**: Always implement throttling for interactive operations to maintain 60 FPS
2. **Lazy Logging**: Use `LoggingService.LogDebugLazy()` for expensive string operations
3. **Bitmap Freezing**: Always freeze bitmaps after creation for thread safety and performance
4. **Memory Management**: Dispose of temporary bitmaps and use `using` statements for drawing contexts
5. **Boundary Caching**: Cache boundary calculations to avoid repeated expensive computations

### WYSIWYG Implementation Guidelines

1. **Coordinate Mapping**: Always use CoordinateMapper for accurate UI-to-image coordinate transformation
2. **Backend Synchronization**: Keep backend state synchronized with UI transformations
3. **Validation First**: Always validate transformations before applying crop operations
4. **Error Recovery**: Implement graceful fallback to standard cropping if WYSIWYG fails

### Arabic Localization Guidelines

1. **Error Messages**: Provide specific Arabic error messages for each operation context
2. **Success Notifications**: Use toast notifications instead of modal dialogs for success messages
3. **RTL Support**: Ensure all UI components support right-to-left layout
4. **Cultural Considerations**: Use appropriate Arabic terminology for image editing operations

### Architecture Integration Guidelines

1. **ServiceLocator Usage**: Always access services through ServiceLocator dependency injection
2. **BaseViewModel Inheritance**: Inherit from BaseViewModel for proper MVVM implementation
3. **RelayCommand Pattern**: Use RelayCommand with descriptive names for debugging
4. **Error Handling**: Integrate with ErrorManager for consistent error handling
5. **Logging Integration**: Use LoggingService for all debugging and monitoring operations

---

## Troubleshooting Common Issues

### Performance Issues

**Issue**: Slow drag operations
- **Solution**: Verify 16ms throttling is properly implemented
- **Check**: `DragThrottleMs` constant and `_lastDragUpdate` timing

**Issue**: Memory leaks during image processing
- **Solution**: Ensure all bitmaps are frozen and disposed properly
- **Check**: `using` statements for DrawingContext and proper bitmap lifecycle

### Coordinate Transformation Issues

**Issue**: Crop area doesn't match visual selection
- **Solution**: Verify CoordinateMapper inverse transformation matrix
- **Check**: Transformation order and rotation handling for crop rectangles

**Issue**: Boundary validation failures
- **Solution**: Check TransformationValidator bounds checking logic
- **Check**: Image size calculations and crop area mapping accuracy

### WYSIWYG System Issues

**Issue**: Backend synchronization failures
- **Solution**: Verify PreviewBackendSynchronizer initialization
- **Check**: Service availability and error handling in synchronization calls

**Issue**: Inconsistent crop results
- **Solution**: Ensure proper validation before crop operations
- **Check**: ValidationResult handling and error message display

This comprehensive documentation provides developers with everything needed to understand, maintain, and extend UFU2's sophisticated image management system while following established architectural patterns and performance optimization requirements.
