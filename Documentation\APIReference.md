# UFU2 API Reference

## Table of Contents

1. [Core Database Services](#core-database-services)
2. [Validation Services](#validation-services)
3. [Business Logic Services](#business-logic-services)
4. [Infrastructure Services](#infrastructure-services)
5. [Service Integration Patterns](#service-integration-patterns)
6. [Error Handling and Logging](#error-handling-and-logging)
7. [Usage Examples](#usage-examples)

---

## Core Database Services

### ClientDatabaseService

The primary service for client data management with complete CRUD operations.

#### Service Registration
```csharp
// Registered automatically during application startup
var clientService = ServiceLocator.GetService<ClientDatabaseService>();
```

#### CreateClientAsync
```csharp
/// <summary>
/// Creates a new client with complete validation and UID generation
/// </summary>
/// <param name="clientData">Client creation data with all required fields</param>
/// <returns>Generated client UID on success</returns>
/// <exception cref="ValidationException">Thrown when validation fails with Arabic error messages</exception>
/// <exception cref="SqliteException">Thrown when database operation fails</exception>
public async Task<string> CreateClientAsync(ClientData clientData)
```

**Parameters:**
- `clientData` (ClientData): Complete client information including NameFr (required), NameAr, BirthDate, etc.

**Returns:** 
- `string`: Generated client UID (format: {FirstLetter}{SequentialNumber:D2}, e.g., "A01")

**Usage Example:**
```csharp
var clientService = ServiceLocator.GetService<ClientDatabaseService>();
try
{
    var clientData = new ClientData
    {
        NameFr = "Ahmed",
        NameAr = "أحمد",
        BirthDate = "1990-01-01",
        Gender = 0 // Male
    };
    
    var clientUID = await clientService.CreateClientAsync(clientData);
    ErrorManager.ShowUserSuccessToast("تم إنشاء العميل بنجاح", "نجح الحفظ", "ClientManagement");
}
catch (ValidationException ex)
{
    ErrorManager.HandleError(ex, "خطأ في بيانات العميل", "خطأ في التحقق");
}
catch (SqliteException ex)
{
    ErrorManager.HandleError(ex, "خطأ في قاعدة البيانات", "خطأ في النظام");
}
```

#### GetClientAsync
```csharp
/// <summary>
/// Retrieves a client by UID with all related data (activities, phone numbers)
/// </summary>
/// <param name="clientUID">The unique identifier of the client</param>
/// <returns>Complete client data or null if not found</returns>
/// <exception cref="ArgumentException">Thrown when clientUID is null or empty</exception>
public async Task<ClientData?> GetClientAsync(string clientUID)
```

**Parameters:**
- `clientUID` (string): Client unique identifier (required, non-empty)

**Returns:**
- `ClientData?`: Complete client information with related data, or null if not found

**Usage Example:**
```csharp
try
{
    var client = await clientService.GetClientAsync("A01");
    if (client != null)
    {
        // Use client data
        Console.WriteLine($"Client: {client.NameFr} ({client.NameAr})");
        Console.WriteLine($"Activities: {client.Activities.Count}");
        Console.WriteLine($"Phone Numbers: {client.PhoneNumbers.Count}");
    }
    else
    {
        ErrorManager.ShowUserWarningToast("العميل غير موجود", "تحذير", "ClientManagement");
    }
}
catch (ArgumentException ex)
{
    ErrorManager.HandleError(ex, "معرف العميل غير صحيح", "خطأ في المعرف");
}
```

#### CreateClientWithDetailsAsync
```csharp
/// <summary>
/// Creates a client with activities, phone numbers, and file check states in a single transaction
/// </summary>
/// <param name="clientCreationData">Complete client creation data with all related information</param>
/// <returns>Generated client UID on success</returns>
/// <exception cref="ValidationException">Thrown when any validation fails</exception>
/// <exception cref="SqliteException">Thrown when database transaction fails</exception>
public async Task<string> CreateClientWithDetailsAsync(ClientCreationData clientCreationData)
```

**Parameters:**
- `clientCreationData` (ClientCreationData): Complete client data including:
  - Client: Basic client information
  - Activities: List of business activities
  - PhoneNumbers: List of phone numbers with primary designation
  - FileCheckStates: Document verification states

**Returns:**
- `string`: Generated client UID

**Usage Example:**
```csharp
try
{
    var creationData = new ClientCreationData
    {
        Client = new ClientData
        {
            NameFr = "Mohamed",
            NameAr = "محمد",
            BirthDate = "1985-05-15"
        },
        Activities = new List<ActivityData>
        {
            new ActivityData
            {
                ActivityType = "Commercial",
                ActivityCode = "COMM001",
                ActivityDescription = "تجارة عامة"
            }
        },
        PhoneNumbers = new List<PhoneNumberData>
        {
            new PhoneNumberData
            {
                PhoneNumber = "0555123456",
                IsPrimary = true
            }
        }
    };
    
    var clientUID = await clientService.CreateClientWithDetailsAsync(creationData);
    ErrorManager.ShowUserSuccessToast($"تم إنشاء العميل بنجاح: {clientUID}", "نجح الحفظ", "ClientManagement");
}
catch (ValidationException ex)
{
    ErrorManager.HandleError(ex, "خطأ في بيانات العميل", "خطأ في التحقق");
}
```

### UIDGenerationService

Service for generating unique identifiers following UFU2 business rules.

#### GenerateClientUIDAsync
```csharp
/// <summary>
/// Generates a unique Client UID following the pattern {FirstLetter}{SequentialNumber:D2}
/// </summary>
/// <param name="nameFr">The French name of the client (required)</param>
/// <param name="operationId">Optional operation ID for error deduplication tracking</param>
/// <returns>Generated Client UID (e.g., "A01", "B02", "Z99")</returns>
/// <exception cref="ArgumentException">Thrown when nameFr is null or empty</exception>
/// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
public async Task<string> GenerateClientUIDAsync(string nameFr, string? operationId = null)
```

**Parameters:**
- `nameFr` (string): French name of the client (required, non-empty)
- `operationId` (string?, optional): Operation ID for error tracking

**Returns:**
- `string`: Generated client UID

**Usage Example:**
```csharp
var uidService = ServiceLocator.GetService<UIDGenerationService>();
try
{
    var clientUID = await uidService.GenerateClientUIDAsync("Ahmed");
    Console.WriteLine($"Generated UID: {clientUID}"); // Output: "A01" (or next available)
}
catch (ArgumentException ex)
{
    ErrorManager.HandleError(ex, "اسم العميل مطلوب لتوليد المعرف", "خطأ في المعرف");
}
```

#### GenerateActivityUIDAsync
```csharp
/// <summary>
/// Generates a unique Activity UID following the pattern {ClientUID}_Act{ActivitySequence}
/// </summary>
/// <param name="clientUID">The client UID to associate the activity with (required)</param>
/// <param name="operationId">Optional operation ID for error deduplication tracking</param>
/// <returns>Generated Activity UID (e.g., "A01_Act1", "B02_Act2")</returns>
/// <exception cref="ArgumentException">Thrown when clientUID is null or empty</exception>
/// <exception cref="InvalidOperationException">Thrown when UID generation fails</exception>
public async Task<string> GenerateActivityUIDAsync(string clientUID, string? operationId = null)
```

**Parameters:**
- `clientUID` (string): Client UID (required, must exist in database)
- `operationId` (string?, optional): Operation ID for error tracking

**Returns:**
- `string`: Generated activity UID

**Usage Example:**
```csharp
try
{
    var activityUID = await uidService.GenerateActivityUIDAsync("A01");
    Console.WriteLine($"Generated Activity UID: {activityUID}"); // Output: "A01_Act1"
}
catch (ArgumentException ex)
{
    ErrorManager.HandleError(ex, "معرف العميل مطلوب لتوليد معرف النشاط", "خطأ في المعرف");
}
```

---

## Validation Services

### ClientValidationService

Comprehensive validation service for client data with Arabic error messages.

#### ValidateClientCreation
```csharp
/// <summary>
/// Validates client creation data according to UFU2 business rules
/// </summary>
/// <param name="clientData">The client data to validate</param>
/// <returns>Validation result with detailed error information</returns>
public ClientValidationResult ValidateClientCreation(ClientCreationData clientData)
```

**Parameters:**
- `clientData` (ClientCreationData): Complete client creation data

**Returns:**
- `ClientValidationResult`: Validation result with IsValid flag and error details

**Usage Example:**
```csharp
var validationService = ServiceLocator.GetService<ClientValidationService>();
var validationResult = validationService.ValidateClientCreation(clientData);

if (!validationResult.IsValid)
{
    foreach (var error in validationResult.Errors)
    {
        ErrorManager.ShowUserErrorToast(error.Value.First(), "خطأ في التحقق", "Validation");
    }
    return;
}
```

#### ValidatePhoneNumber
```csharp
/// <summary>
/// Validates phone number format and business rules
/// </summary>
/// <param name="phoneNumber">Phone number to validate</param>
/// <param name="isPrimary">Whether this is a primary phone number</param>
/// <returns>Validation result with Arabic error messages</returns>
public ValidationResult ValidatePhoneNumber(string phoneNumber, bool isPrimary = false)
```

**Parameters:**
- `phoneNumber` (string): Phone number to validate
- `isPrimary` (bool): Whether this is the primary phone number

**Returns:**
- `ValidationResult`: Validation result with error details

**Usage Example:**
```csharp
var phoneValidation = validationService.ValidatePhoneNumber("0555123456", true);
if (!phoneValidation.IsValid)
{
    ErrorManager.ShowUserErrorToast(phoneValidation.ErrorMessage, "خطأ في رقم الهاتف", "PhoneValidation");
}
```

### ValidationService

General validation service for common validation patterns.

#### ValidateNameFr
```csharp
/// <summary>
/// Validates French name format (Latin characters only)
/// </summary>
/// <param name="nameFr">French name to validate</param>
/// <returns>Empty string if valid, Arabic error message if invalid</returns>
public string ValidateNameFr(string nameFr)
```

**Parameters:**
- `nameFr` (string): French name to validate

**Returns:**
- `string`: Empty if valid, Arabic error message if invalid

**Usage Example:**
```csharp
var validationService = ServiceLocator.GetService<ValidationService>();
var nameError = validationService.ValidateNameFr("Ahmed123"); // Invalid - contains numbers
if (!string.IsNullOrEmpty(nameError))
{
    ErrorManager.ShowUserErrorToast(nameError, "خطأ في الاسم", "NameValidation");
}
```

---

## Business Logic Services

### FileCheckBusinessRuleService

Service for enforcing file check business rules according to Algerian regulations.

#### ValidateFileCheckBusinessRules
```csharp
/// <summary>
/// Validates file check states against activity type business rules
/// </summary>
/// <param name="activityType">The activity type (Commercial, Craft, Professional)</param>
/// <param name="fileCheckStates">Dictionary of file check types and their states</param>
/// <param name="enforceCompletion">Whether to enforce completion of all required checks</param>
/// <returns>Validation result with detailed error information</returns>
public ValidationResult ValidateFileCheckBusinessRules(string activityType, 
    Dictionary<string, bool> fileCheckStates, bool enforceCompletion = true)
```

**Parameters:**
- `activityType` (string): Activity type ("Commercial", "Craft", "Professional")
- `fileCheckStates` (Dictionary<string, bool>): File check states
- `enforceCompletion` (bool): Whether to require all checks to be completed

**Returns:**
- `ValidationResult`: Validation result with business rule compliance

**Usage Example:**
```csharp
var fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
var fileCheckStates = new Dictionary<string, bool>
{
    ["CAS"] = true,
    ["NIF"] = true,
    ["RC"] = false // Required for Commercial but not completed
};

var result = fileCheckService.ValidateFileCheckBusinessRules("Commercial", fileCheckStates, true);
if (!result.IsValid)
{
    foreach (var error in result.Errors)
    {
        ErrorManager.ShowUserErrorToast(error.Value.First(), "خطأ في فحص الملفات", "FileCheck");
    }
}
```

---

## Infrastructure Services

### DatabaseService

Core database service providing SQLite connection management and configuration.

#### CreateConnection
```csharp
/// <summary>
/// Creates a new SQLite database connection with optimized configuration
/// </summary>
/// <returns>Configured SQLite connection (not opened)</returns>
/// <exception cref="InvalidOperationException">Thrown when database path is not configured</exception>
public SqliteConnection CreateConnection()
```

**Returns:**
- `SqliteConnection`: Configured database connection

**Usage Example:**
```csharp
var databaseService = ServiceLocator.GetService<DatabaseService>();
using var connection = databaseService.CreateConnection();
await connection.OpenAsync();

// Use connection for database operations
var result = await connection.QueryAsync<ClientEntity>("SELECT * FROM Clients WHERE UID = @UID", new { UID = "A01" });
```

#### ConfigureDatabaseAsync
```csharp
/// <summary>
/// Applies optimal SQLite configuration for performance and reliability
/// </summary>
/// <param name="connection">Open SQLite connection to configure</param>
/// <returns>Task representing the configuration operation</returns>
/// <exception cref="SqliteException">Thrown when configuration fails</exception>
public static async Task ConfigureDatabaseAsync(IDbConnection connection)
```

**Parameters:**
- `connection` (IDbConnection): Open database connection to configure

**Usage Example:**
```csharp
using var connection = databaseService.CreateConnection();
await connection.OpenAsync();
await DatabaseService.ConfigureDatabaseAsync(connection);
// Connection is now optimally configured for UFU2 operations
```

### ThemeManager

Static service for managing application themes and MaterialDesign integration.

#### SwitchThemeAsync
```csharp
/// <summary>
/// Switches to the specified theme with full UI update
/// </summary>
/// <param name="theme">The theme to switch to (Light or Dark)</param>
/// <returns>True if theme switch was successful</returns>
/// <exception cref="InvalidOperationException">Thrown when ThemeManager is not initialized</exception>
public static async Task<bool> SwitchThemeAsync(ApplicationTheme theme)
```

**Parameters:**
- `theme` (ApplicationTheme): Theme to switch to (ApplicationTheme.Light or ApplicationTheme.Dark)

**Returns:**
- `bool`: True if theme switch was successful

**Usage Example:**
```csharp
try
{
    var success = await ThemeManager.SwitchThemeAsync(ApplicationTheme.Dark);
    if (success)
    {
        LoggingService.LogInfo("Theme switched to Dark successfully", "ThemeManager");
    }
    else
    {
        ErrorManager.ShowUserWarningToast("فشل في تغيير المظهر", "تحذير", "ThemeManager");
    }
}
catch (InvalidOperationException ex)
{
    ErrorManager.HandleError(ex, "خطأ في إدارة المظهر", "خطأ في النظام");
}
```

---

## Service Integration Patterns

### ServiceLocator Usage

All services in UFU2 are accessed through the ServiceLocator pattern:

#### Service Registration (Application Startup)
```csharp
// In App.xaml.cs OnStartup method
ServiceLocator.Initialize();
await ServiceLocator.InitializeDatabaseServicesAsync();

// Services are automatically registered:
// - DatabaseService
// - ClientDatabaseService
// - UIDGenerationService
// - ClientValidationService
// - FileCheckBusinessRuleService
// - ValidationService
// - DatabaseMigrationService
// - DatabasePerformanceMonitoringService
```

#### Service Resolution in ViewModels
```csharp
public class ClientManagementViewModel : BaseViewModel
{
    private readonly ClientDatabaseService _clientService;
    private readonly UIDGenerationService _uidService;
    private readonly ClientValidationService _validationService;

    public ClientManagementViewModel()
    {
        // ServiceLocator dependency injection
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _uidService = ServiceLocator.GetService<UIDGenerationService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
    }
}
```

#### Safe Service Resolution
```csharp
// Use TryGetService for optional services
if (ServiceLocator.TryGetService<DatabasePerformanceMonitoringService>(out var perfService))
{
    // Use performance monitoring if available
    using var monitor = perfService.StartQueryMonitoring("ClientQuery");
    // Perform operation
}
```

### Async/Await Patterns

All database operations in UFU2 use async/await patterns:

#### Proper Async Implementation
```csharp
public async Task<string> CreateClientWorkflowAsync(ClientCreationData clientData)
{
    try
    {
        // 1. Validate data
        var validationResult = await _validationService.ValidateCompleteClientDataAsync(clientData);
        if (!validationResult.IsValid)
        {
            throw new ValidationException("بيانات العميل غير صحيحة");
        }

        // 2. Generate UID
        var clientUID = await _uidService.GenerateClientUIDAsync(clientData.Client.NameFr);
        clientData.Client.UID = clientUID;

        // 3. Create client with all details
        var result = await _clientService.CreateClientWithDetailsAsync(clientData);

        return result;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error in client creation workflow: {ex.Message}", "ClientWorkflow");
        throw;
    }
}
```

#### ConfigureAwait Usage
```csharp
// For library code, use ConfigureAwait(false) to avoid deadlocks
public async Task<ClientData> GetClientAsync(string clientUID)
{
    using var connection = _databaseService.CreateConnection();
    await connection.OpenAsync().ConfigureAwait(false);

    var result = await connection.QueryFirstOrDefaultAsync<ClientEntity>(
        "SELECT * FROM Clients WHERE UID = @UID",
        new { UID = clientUID }
    ).ConfigureAwait(false);

    return MapToClientData(result);
}
```

### Transaction Management

UFU2 uses comprehensive transaction management for data integrity:

#### Multi-Operation Transactions
```csharp
public async Task<string> CreateClientWithDetailsAsync(ClientCreationData clientData)
{
    using var connection = _databaseService.CreateConnection();
    await connection.OpenAsync();

    using var transaction = connection.BeginTransaction();
    try
    {
        // Step 1: Create client
        var clientUID = await CreateClientInternalAsync(connection, transaction, clientData.Client);

        // Step 2: Create activities
        foreach (var activity in clientData.Activities)
        {
            activity.ClientUID = clientUID;
            await CreateActivityInternalAsync(connection, transaction, activity);
        }

        // Step 3: Create phone numbers
        foreach (var phone in clientData.PhoneNumbers)
        {
            phone.ClientUID = clientUID;
            await CreatePhoneNumberInternalAsync(connection, transaction, phone);
        }

        // Commit all operations
        transaction.Commit();
        return clientUID;
    }
    catch (Exception)
    {
        transaction.Rollback();
        throw;
    }
}
```

---

## Error Handling and Logging

### ErrorManager Integration

All services integrate with UFU2's centralized error handling:

#### Error Handling Patterns
```csharp
public async Task<ClientData> GetClientAsync(string clientUID)
{
    try
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(clientUID))
        {
            throw new ArgumentException("معرف العميل مطلوب");
        }

        // Perform operation
        var result = await GetClientInternalAsync(clientUID);

        if (result == null)
        {
            LoggingService.LogWarning($"Client not found: {clientUID}", "ClientDatabaseService");
            return null;
        }

        return result;
    }
    catch (ArgumentException ex)
    {
        // User input error - show Arabic message
        ErrorManager.HandleError(ex, "معرف العميل غير صحيح", "خطأ في المعرف", LogLevel.Warning, "ClientDatabaseService");
        throw;
    }
    catch (SqliteException ex)
    {
        // Database error - show generic message, log technical details
        ErrorManager.HandleError(ex, "خطأ في قاعدة البيانات", "خطأ في النظام", LogLevel.Error, "ClientDatabaseService");
        throw;
    }
    catch (Exception ex)
    {
        // Unexpected error - log and show generic message
        ErrorManager.HandleError(ex, "حدث خطأ غير متوقع", "خطأ في النظام", LogLevel.Error, "ClientDatabaseService");
        throw;
    }
}
```

#### Toast Notifications for User Feedback
```csharp
// Success notifications
ErrorManager.ShowUserSuccessToast("تم حفظ البيانات بنجاح", "نجح الحفظ", "ClientManagement");

// Warning notifications
ErrorManager.ShowUserWarningToast("بعض البيانات مفقودة", "تحذير", "ClientManagement");

// Error notifications
ErrorManager.ShowUserErrorToast("فشل في حفظ البيانات", "خطأ في الحفظ", "ClientManagement");

// Info notifications
ErrorManager.ShowUserInfoToast("جاري تحميل البيانات...", "معلومات", "ClientManagement");
```

### LoggingService Integration

All services use structured logging for debugging and monitoring:

#### Logging Patterns
```csharp
public async Task<string> CreateClientAsync(ClientData clientData)
{
    var operationId = Guid.NewGuid().ToString("N")[..8];

    LoggingService.LogInfo($"Starting client creation - Operation: {operationId}", "ClientDatabaseService");

    try
    {
        // Log input validation
        LoggingService.LogDebug($"Validating client data for: {clientData.NameFr}", "ClientDatabaseService");

        // Perform operation
        var result = await CreateClientInternalAsync(clientData);

        // Log success
        LoggingService.LogInfo($"Client created successfully: {result} - Operation: {operationId}", "ClientDatabaseService");

        return result;
    }
    catch (Exception ex)
    {
        // Log error with context
        LoggingService.LogError($"Client creation failed - Operation: {operationId}, Error: {ex.Message}", "ClientDatabaseService");
        throw;
    }
}
```

#### Performance Logging
```csharp
public async Task<List<ClientData>> GetClientsPagedAsync(int pageSize, int pageIndex)
{
    var stopwatch = Stopwatch.StartNew();

    try
    {
        var result = await GetClientsInternalAsync(pageSize, pageIndex);

        stopwatch.Stop();
        LoggingService.LogDebug($"Paged client query completed in {stopwatch.ElapsedMilliseconds}ms - Page: {pageIndex}, Size: {pageSize}, Results: {result.Count}", "ClientDatabaseService");

        // Alert on slow queries
        if (stopwatch.ElapsedMilliseconds > 1000)
        {
            LoggingService.LogWarning($"Slow query detected: {stopwatch.ElapsedMilliseconds}ms for paged client query", "ClientDatabaseService");
        }

        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        LoggingService.LogError($"Paged client query failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}", "ClientDatabaseService");
        throw;
    }
}
```

---

## Usage Examples

### Complete Client Creation Workflow

This example demonstrates a complete client creation workflow using multiple services:

```csharp
public class ClientCreationWorkflow
{
    private readonly ClientDatabaseService _clientService;
    private readonly UIDGenerationService _uidService;
    private readonly ClientValidationService _validationService;
    private readonly FileCheckBusinessRuleService _fileCheckService;

    public ClientCreationWorkflow()
    {
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _uidService = ServiceLocator.GetService<UIDGenerationService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
        _fileCheckService = ServiceLocator.GetService<FileCheckBusinessRuleService>();
    }

    public async Task<WorkflowResult> CreateCompleteClientAsync(ClientCreationData clientData)
    {
        var operationId = Guid.NewGuid().ToString("N")[..8];

        try
        {
            LoggingService.LogInfo($"Starting complete client creation workflow - Operation: {operationId}", "ClientCreationWorkflow");

            // Step 1: Validate client data
            var clientValidation = _validationService.ValidateClientCreation(clientData);
            if (!clientValidation.IsValid)
            {
                LoggingService.LogWarning($"Client validation failed - Operation: {operationId}", "ClientCreationWorkflow");
                return WorkflowResult.ValidationFailed(clientValidation.Errors);
            }

            // Step 2: Validate file check business rules
            foreach (var activity in clientData.Activities)
            {
                var fileCheckValidation = _fileCheckService.ValidateFileCheckBusinessRules(
                    activity.ActivityType,
                    activity.FileCheckStates,
                    enforceCompletion: false
                );

                if (!fileCheckValidation.IsValid)
                {
                    LoggingService.LogWarning($"File check validation failed for activity {activity.ActivityType} - Operation: {operationId}", "ClientCreationWorkflow");
                    return WorkflowResult.FileCheckFailed(fileCheckValidation.Errors);
                }
            }

            // Step 3: Generate client UID
            var clientUID = await _uidService.GenerateClientUIDAsync(clientData.Client.NameFr, operationId);
            clientData.Client.UID = clientUID;

            // Step 4: Generate activity UIDs
            foreach (var activity in clientData.Activities)
            {
                activity.UID = await _uidService.GenerateActivityUIDAsync(clientUID, operationId);
                activity.ClientUID = clientUID;
            }

            // Step 5: Create client with all details
            var result = await _clientService.CreateClientWithDetailsAsync(clientData);

            LoggingService.LogInfo($"Complete client creation successful: {result} - Operation: {operationId}", "ClientCreationWorkflow");

            // Show success message
            ErrorManager.ShowUserSuccessToast(
                $"تم إنشاء العميل بنجاح: {result}",
                "نجح الحفظ",
                "ClientCreationWorkflow"
            );

            return WorkflowResult.Success(result);
        }
        catch (ValidationException ex)
        {
            LoggingService.LogError($"Validation error in client creation - Operation: {operationId}: {ex.Message}", "ClientCreationWorkflow");
            ErrorManager.HandleError(ex, "خطأ في بيانات العميل", "خطأ في التحقق", LogLevel.Warning, "ClientCreationWorkflow");
            return WorkflowResult.ValidationFailed(new Dictionary<string, List<string>> { ["General"] = new List<string> { ex.Message } });
        }
        catch (SqliteException ex)
        {
            LoggingService.LogError($"Database error in client creation - Operation: {operationId}: {ex.Message}", "ClientCreationWorkflow");
            ErrorManager.HandleError(ex, "خطأ في قاعدة البيانات", "خطأ في النظام", LogLevel.Error, "ClientCreationWorkflow");
            return WorkflowResult.DatabaseError(ex.Message);
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Unexpected error in client creation - Operation: {operationId}: {ex.Message}", "ClientCreationWorkflow");
            ErrorManager.HandleError(ex, "حدث خطأ غير متوقع", "خطأ في النظام", LogLevel.Error, "ClientCreationWorkflow");
            return WorkflowResult.UnexpectedError(ex.Message);
        }
    }
}

public class WorkflowResult
{
    public bool IsSuccess { get; set; }
    public string? ClientUID { get; set; }
    public Dictionary<string, List<string>> Errors { get; set; } = new();
    public string? ErrorMessage { get; set; }

    public static WorkflowResult Success(string clientUID) => new() { IsSuccess = true, ClientUID = clientUID };
    public static WorkflowResult ValidationFailed(Dictionary<string, List<string>> errors) => new() { IsSuccess = false, Errors = errors };
    public static WorkflowResult FileCheckFailed(Dictionary<string, List<string>> errors) => new() { IsSuccess = false, Errors = errors };
    public static WorkflowResult DatabaseError(string message) => new() { IsSuccess = false, ErrorMessage = message };
    public static WorkflowResult UnexpectedError(string message) => new() { IsSuccess = false, ErrorMessage = message };
}
```

### ViewModel Integration Example

This example shows how to integrate services in a ViewModel following UFU2 patterns:

```csharp
public class NewClientViewModel : BaseViewModel, IDataErrorInfo
{
    #region Services
    private readonly ClientDatabaseService _clientService;
    private readonly ClientValidationService _validationService;
    private readonly ValidationService _generalValidationService;
    #endregion

    #region Properties
    private string _nameFr = string.Empty;
    public string NameFr
    {
        get => _nameFr;
        set
        {
            if (SetProperty(ref _nameFr, value))
            {
                OnPropertyChanged($"Item[{nameof(NameFr)}]");
                UpdateCanSave();
            }
        }
    }

    private bool _isSaveEnabled = false;
    public bool IsSaveEnabled
    {
        get => _isSaveEnabled;
        private set => SetProperty(ref _isSaveEnabled, value);
    }
    #endregion

    #region Commands
    public ICommand SaveCommand { get; }
    public ICommand CancelCommand { get; }
    #endregion

    #region Constructor
    public NewClientViewModel()
    {
        // ServiceLocator dependency injection
        _clientService = ServiceLocator.GetService<ClientDatabaseService>();
        _validationService = ServiceLocator.GetService<ClientValidationService>();
        _generalValidationService = ServiceLocator.GetService<ValidationService>();

        // Initialize commands
        SaveCommand = new RelayCommand(async () => await SaveClientAsync(), () => IsSaveEnabled);
        CancelCommand = new RelayCommand(CancelOperation);
    }
    #endregion

    #region Command Implementations
    private async Task SaveClientAsync()
    {
        try
        {
            var clientData = new ClientCreationData
            {
                Client = new ClientData
                {
                    NameFr = NameFr,
                    // ... other properties
                }
            };

            var result = await _clientService.CreateClientWithDetailsAsync(clientData);

            ErrorManager.ShowUserSuccessToast(
                $"تم حفظ العميل بنجاح: {result}",
                "نجح الحفظ",
                "NewClientViewModel"
            );

            ClearForm();
        }
        catch (Exception ex)
        {
            ErrorManager.HandleError(ex, "خطأ في حفظ بيانات العميل", "خطأ في الحفظ");
        }
    }
    #endregion

    #region IDataErrorInfo Implementation
    public string Error => string.Empty;

    public string this[string columnName]
    {
        get
        {
            return columnName switch
            {
                nameof(NameFr) => _generalValidationService.ValidateNameFr(NameFr),
                _ => string.Empty
            };
        }
    }
    #endregion

    #region Helper Methods
    private void UpdateCanSave()
    {
        IsSaveEnabled = !string.IsNullOrWhiteSpace(NameFr) &&
                       string.IsNullOrEmpty(this[nameof(NameFr)]);
        ((RelayCommand)SaveCommand).RaiseCanExecuteChanged();
    }

    private void ClearForm()
    {
        NameFr = string.Empty;
        UpdateCanSave();
    }
    #endregion
}
```

---

## API Reference Summary

This comprehensive API reference provides complete documentation for all UFU2 services, including:

### Core Services Covered
- **ClientDatabaseService**: Complete CRUD operations for client management
- **UIDGenerationService**: Unique identifier generation with business rules
- **ClientValidationService**: Application-level validation with Arabic error messages
- **FileCheckBusinessRuleService**: Algerian regulation compliance enforcement
- **ValidationService**: General validation patterns and utilities
- **DatabaseService**: Core database connection and configuration management
- **ThemeManager**: Application theme and MaterialDesign integration

### Documentation Features
- **Method Signatures**: Complete parameter and return type documentation
- **Error Conditions**: Detailed exception handling with Arabic error messages
- **Usage Examples**: Practical code examples for every service method
- **Integration Patterns**: ServiceLocator dependency injection examples
- **Best Practices**: Async/await patterns, transaction management, and error handling
- **MVVM Integration**: Complete ViewModel examples following UFU2 patterns

### Arabic Localization
All user-facing error messages and notifications are documented in Arabic, ensuring proper localization support throughout the application.

### Performance Considerations
The documentation includes performance logging patterns, transaction management best practices, and optimization strategies for database operations.

This API reference serves as the definitive guide for developers working with UFU2 services, ensuring consistent implementation patterns and proper integration with the application's architectural standards.
