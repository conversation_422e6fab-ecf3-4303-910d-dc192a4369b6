---
type: "manual"
description: "SQLite database standards for UFU2 project"
---
# UFU2 SQLite Database Standards

This document outlines the required standards for all SQLite database operations in the UFU2 project. These standards ensure optimal performance, maintainability, and reliability of database interactions.

## 1. Connection Management

1. **Single Connection Per ViewModel**
   - Maintain one connection per window/ViewModel
   - Never share connections across threads
   - Avoid creating new connections for each query
   - Example:
     ```csharp
     // Good - Single connection in ViewModel
     public class ClientViewModel : ViewModelBase
     {
         private readonly SqliteConnection _connection;
         
         public ClientViewModel()
         {
             _connection = DatabaseService.CreateConnection();
         }
         
         protected override void Dispose(bool disposing)
         {
             if (disposing)
             {
                 _connection?.Dispose();
             }
             base.Dispose(disposing);
         }
     }
     ```

2. **Proper Connection Disposal**
   - Always use `using` statements or explicitly call `Dispose()`
   - Implement `IDisposable` in classes that manage connections
   - Example:
     ```csharp
     // Using statement approach
     public async Task<ClientData> GetClientDataAsync(int clientId)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         return await connection.QueryFirstOrDefaultAsync<ClientData>(
             "SELECT * FROM Clients WHERE Id = @Id", 
             new { Id = clientId }
         );
     }
     ```

3. **Optimal PRAGMA Configuration**
   - Configure database settings once at application startup
   - Store configuration in a central location
   - Example:
     ```csharp
     public static void ConfigureDatabase(SqliteConnection connection)
     {
         var pragmas = new[]
         {
             "PRAGMA journal_mode = WAL;",           // Write-Ahead Logging for better concurrency
             "PRAGMA synchronous = NORMAL;",         // Balance between safety and performance
             "PRAGMA cache_size = 10000;",           // Allocate more memory for caching
             "PRAGMA temp_store = MEMORY;",          // Store temporary tables in memory
             "PRAGMA auto_vacuum = INCREMENTAL;",    // Reclaim space without full vacuum
             "PRAGMA foreign_keys = ON;"             // Enforce referential integrity
         };
         
         using var command = connection.CreateCommand();
         foreach (var pragma in pragmas)
         {
             command.CommandText = pragma;
             command.ExecuteNonQuery();
             LoggingService.LogDebug($"Executed: {pragma}", "DatabaseService");
         }
     }
     ```

## 2. Query Best Practices

1. **Parameterized Queries Only**
   - Always use parameters instead of string concatenation
   - Never include user input directly in SQL strings
   - Example:
     ```csharp
     // Good - Parameterized query
     public async Task<ClientData> FindClientAsync(string name, string idNumber)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         return await connection.QueryFirstOrDefaultAsync<ClientData>(
             "SELECT * FROM Clients WHERE Name = @Name AND IdNumber = @IdNumber",
             new { Name = name, IdNumber = idNumber }
         );
     }
     
     // Bad - SQL Injection vulnerability
     // public async Task<ClientData> FindClientAsync(string name, string idNumber)
     // {
     //     using var connection = DatabaseService.CreateConnection();
     //     await connection.OpenAsync();
     //     
     //     return await connection.QueryFirstOrDefaultAsync<ClientData>(
     //         $"SELECT * FROM Clients WHERE Name = '{name}' AND IdNumber = '{idNumber}'"
     //     );
     // }
     ```

2. **Specific Column Selection**
   - Specify exact columns instead of using `SELECT *`
   - Reduces memory usage and I/O operations
   - Example:
     ```csharp
     // Good - Specific columns
     var clients = await connection.QueryAsync<ClientSummary>(
         "SELECT Id, Name, PhoneNumber, LastVisitDate FROM Clients WHERE IsActive = 1"
     );
     
     // Bad - Excessive data retrieval
     // var clients = await connection.QueryAsync<Client>("SELECT * FROM Clients WHERE IsActive = 1");
     ```

3. **Pagination Implementation**
   - Implement pagination for large result sets
   - Use LIMIT and OFFSET for basic pagination
   - Example:
     ```csharp
     public async Task<List<ClientSummary>> GetClientsPageAsync(int pageNumber, int pageSize)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         int offset = (pageNumber - 1) * pageSize;
         return (await connection.QueryAsync<ClientSummary>(
             "SELECT Id, Name, PhoneNumber FROM Clients ORDER BY Name LIMIT @PageSize OFFSET @Offset",
             new { PageSize = pageSize, Offset = offset }
         )).ToList();
     }
     ```

4. **Appropriate Data Types**
   - Use INTEGER PRIMARY KEY for auto-increment IDs
   - Match SQLite types with appropriate .NET types
   - Example:
     ```csharp
     // Good schema design
     private const string CreateClientsTableSql = @"
         CREATE TABLE IF NOT EXISTS Clients (
             Id INTEGER PRIMARY KEY,
             Name TEXT NOT NULL,
             PhoneNumber TEXT,
             IsActive INTEGER NOT NULL DEFAULT 1,
             Balance REAL NOT NULL DEFAULT 0.0,
             RegistrationDate TEXT NOT NULL
         )";
     ```

## 3. Transactions & Bulk Operations

1. **Transaction Wrapping**
   - Always wrap multiple operations in a transaction
   - Use try-catch-finally with explicit Commit/Rollback
   - Example:
     ```csharp
     public async Task<bool> SaveClientWithAddressesAsync(Client client)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         using var transaction = connection.BeginTransaction();
         
         try
         {
             // Insert client
             int clientId = await connection.ExecuteScalarAsync<int>(
                 "INSERT INTO Clients (Name, PhoneNumber) VALUES (@Name, @PhoneNumber); SELECT last_insert_rowid();",
                 client, transaction
             );
             
             // Insert addresses
             foreach (var address in client.Addresses)
             {
                 address.ClientId = clientId;
                 await connection.ExecuteAsync(
                     "INSERT INTO Addresses (ClientId, Street, City) VALUES (@ClientId, @Street, @City)",
                     address, transaction
                 );
             }
             
             transaction.Commit();
             return true;
         }
         catch (Exception ex)
         {
             transaction.Rollback();
             ErrorManager.HandleError(ex, "فشل في حفظ بيانات العميل", "خطأ في الحفظ", 
                                     LogLevel.Error, "ClientViewModel");
             return false;
         }
     }
     ```

2. **Optimal Batch Size**
   - Use 500-1,000 operations per transaction
   - Balance between memory usage and performance
   - Example:
     ```csharp
     public async Task ImportClientsAsync(List<Client> clients, IProgress<double> progress)
     {
         const int batchSize = 500;
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         for (int i = 0; i < clients.Count; i += batchSize)
         {
             using var transaction = connection.BeginTransaction();
             try
             {
                 var batch = clients.Skip(i).Take(batchSize).ToList();
                 foreach (var client in batch)
                 {
                     await connection.ExecuteAsync(
                         "INSERT INTO Clients (Name, PhoneNumber) VALUES (@Name, @PhoneNumber)",
                         client, transaction
                     );
                 }
                 
                 transaction.Commit();
                 progress?.Report((double)(i + batch.Count) / clients.Count);
             }
             catch (Exception ex)
             {
                 transaction.Rollback();
                 ErrorManager.HandleError(ex, "فشل في استيراد البيانات", "خطأ في الاستيراد", 
                                         LogLevel.Error, "ImportViewModel");
                 throw;
             }
         }
     }
     ```

3. **Prepared Statement Reuse**
   - Reuse prepared statements for repeated operations
   - Prepare once, execute many times
   - Example:
     ```csharp
     public async Task BulkInsertAddressesAsync(List<Address> addresses)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         using var transaction = connection.BeginTransaction();
         
         try
         {
             using var cmd = connection.CreateCommand();
             cmd.CommandText = "INSERT INTO Addresses (ClientId, Street, City) VALUES (@ClientId, @Street, @City)";
             
             var clientIdParam = cmd.CreateParameter();
             clientIdParam.ParameterName = "@ClientId";
             cmd.Parameters.Add(clientIdParam);
             
             var streetParam = cmd.CreateParameter();
             streetParam.ParameterName = "@Street";
             cmd.Parameters.Add(streetParam);
             
             var cityParam = cmd.CreateParameter();
             cityParam.ParameterName = "@City";
             cmd.Parameters.Add(cityParam);
             
             // Prepare once
             cmd.Prepare();
             
             foreach (var address in addresses)
             {
                 clientIdParam.Value = address.ClientId;
                 streetParam.Value = address.Street;
                 cityParam.Value = address.City;
                 await cmd.ExecuteNonQueryAsync();
             }
             
             transaction.Commit();
         }
         catch (Exception ex)
         {
             transaction.Rollback();
             ErrorManager.HandleError(ex, "فشل في إضافة العناوين", "خطأ في الإضافة", 
                                     LogLevel.Error, "AddressViewModel");
             throw;
         }
     }
     ```

## 4. Indexing Strategy

1. **Strategic Index Creation**
   - Create indexes on columns used in WHERE, JOIN, and ORDER BY clauses
   - Index all foreign key columns
   - Example:
     ```csharp
     private const string CreateIndexesSql = @"
         -- Index for client search by name
         CREATE INDEX IF NOT EXISTS idx_clients_name ON Clients(Name);
         
         -- Index for foreign key relationship
         CREATE INDEX IF NOT EXISTS idx_addresses_client_id ON Addresses(ClientId);
         
         -- Index for filtering active clients
         CREATE INDEX IF NOT EXISTS idx_clients_active ON Clients(IsActive);
     ";
     ```

2. **Compound Indexes**
   - Use compound indexes for multi-column conditions
   - Order columns from most to least selective
   - Example:
     ```csharp
     // Compound index for client search
     await connection.ExecuteAsync(
         "CREATE INDEX IF NOT EXISTS idx_clients_name_phone ON Clients(Name, PhoneNumber)"
     );
     ```

3. **Partial Indexes**
   - Implement partial indexes for filtered queries
   - Reduces index size and improves performance
   - Example:
     ```csharp
     // Partial index for active clients
     await connection.ExecuteAsync(
         "CREATE INDEX IF NOT EXISTS idx_active_clients_last_visit ON Clients(LastVisitDate) WHERE IsActive = 1"
     );
     ```

4. **Index Performance Balance**
   - Balance index benefits against write performance
   - Monitor and measure impact before adding indexes
   - Example:
     ```csharp
     // Document index creation decisions
     /* 
      * Index Creation Decision:
      * - Added idx_clients_name for client search performance
      * - Avoided indexing LastModified as it changes frequently
      * - Benchmark showed 85% query improvement with minimal write impact
      */
     ```

## 5. Async & UI Responsiveness

1. **Background Thread Operations**
   - Move all database operations off the UI thread
   - Use async/await pattern consistently
   - Example:
     ```csharp
     // Good - Async operation
     public async Task LoadClientsAsync()
     {
         IsLoading = true;
         try
         {
             // Run database operation on background thread
             var clients = await Task.Run(async () => 
             {
                 using var connection = DatabaseService.CreateConnection();
                 await connection.OpenAsync();
                 return await connection.QueryAsync<Client>("SELECT * FROM Clients WHERE IsActive = 1");
             });
             
             Clients = new ObservableCollection<Client>(clients);
         }
         catch (Exception ex)
         {
             ErrorManager.HandleError(ex, "فشل في تحميل بيانات العملاء", "خطأ في التحميل", 
                                     LogLevel.Error, "ClientListViewModel");
         }
         finally
         {
             IsLoading = false;
         }
     }
     ```

2. **Progress Reporting**
   - Implement progress reporting for long-running operations
   - Use IProgress<T> for thread-safe progress updates
   - Example:
     ```csharp
     public async Task ImportDataAsync(string filePath)
     {
         IsImporting = true;
         Progress = 0;
         
         try
         {
             var progress = new Progress<double>(value => 
             {
                 Progress = value;
             });
             
             await Task.Run(() => ProcessImportFile(filePath, progress));
             
             ErrorManager.ShowUserInfo("تم استيراد البيانات بنجاح", "استيراد البيانات");
         }
         catch (Exception ex)
         {
             ErrorManager.HandleError(ex, "فشل في استيراد البيانات", "خطأ في الاستيراد", 
                                     LogLevel.Error, "ImportViewModel");
         }
         finally
         {
             IsImporting = false;
         }
     }
     
     private void ProcessImportFile(string filePath, IProgress<double> progress)
     {
         // Implementation with progress reporting
     }
     ```

## 6. Caching & Paging

1. **Memory Caching Implementation**
   - Implement memory caching for frequently accessed data
   - Use expiration policies appropriate to data change frequency
   - Example:
     ```csharp
     private readonly MemoryCache _cache = new(new MemoryCacheOptions { SizeLimit = 1000 });
     
     public async Task<List<LookupItem>> GetLookupItemsAsync(string category)
     {
         string cacheKey = $"lookup_{category}";
         
         if (!_cache.TryGetValue(cacheKey, out List<LookupItem> items))
         {
             using var connection = DatabaseService.CreateConnection();
             await connection.OpenAsync();
             
             items = (await connection.QueryAsync<LookupItem>(
                 "SELECT Id, Name FROM LookupItems WHERE Category = @Category ORDER BY Name",
                 new { Category = category }
             )).ToList();
             
             _cache.Set(cacheKey, items, TimeSpan.FromHours(24));
         }
         
         return items;
     }
     ```

2. **Efficient Pagination**
   - Use keyset pagination for better performance
   - Implement virtual scrolling for large datasets
   - Example:
     ```csharp
     public async Task<List<ClientSummary>> GetNextClientPageAsync(int lastClientId, int pageSize)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         return (await connection.QueryAsync<ClientSummary>(
             @"SELECT Id, Name, PhoneNumber 
               FROM Clients 
               WHERE Id > @LastId 
               ORDER BY Id 
               LIMIT @PageSize",
             new { LastId = lastClientId, PageSize = pageSize }
         )).ToList();
     }
     ```

## 7. Common Pitfalls to Avoid

1. **❌ Connection Mismanagement**
   - Never open/close connections repeatedly
   - Don't share connections across threads
   - Example of what to avoid:
     ```csharp
     // BAD PRACTICE - Don't do this!
     public void ProcessEachClient()
     {
         var clients = GetAllClients();
         foreach (var client in clients)
         {
             using var connection = new SqliteConnection(_connectionString); // BAD: New connection for each client
             connection.Open();
             // Process client...
         }
     }
     ```

2. **❌ SQL Injection Risks**
   - Never execute queries without parameters
   - Don't concatenate user input into SQL strings
   - Example of what to avoid:
     ```csharp
     // BAD PRACTICE - Don't do this!
     public Client FindClient(string name)
     {
         using var connection = DatabaseService.CreateConnection();
         connection.Open();
         
         // DANGEROUS: SQL Injection vulnerability
         return connection.QueryFirstOrDefault<Client>(
             $"SELECT * FROM Clients WHERE Name = '{name}'"
         );
     }
     ```

3. **❌ Missing Transactions**
   - Don't perform multiple related operations without a transaction
   - Example of what to avoid:
     ```csharp
     // BAD PRACTICE - Don't do this!
     public void SaveClientWithAddresses(Client client, List<Address> addresses)
     {
         using var connection = DatabaseService.CreateConnection();
         connection.Open();
         
         // BAD: No transaction for related operations
         connection.Execute("INSERT INTO Clients (Name) VALUES (@Name)", client);
         int clientId = connection.ExecuteScalar<int>("SELECT last_insert_rowid()");
         
         foreach (var address in addresses)
         {
             address.ClientId = clientId;
             connection.Execute("INSERT INTO Addresses (ClientId, Street) VALUES (@ClientId, @Street)", address);
             // If this fails, client record remains without addresses
         }
     }
     ```

4. **❌ Inefficient Text Search**
   - Don't use `LIKE '%term%'` for text search
   - Use SQLite FTS5 (Full-Text Search) instead
   - Example:
     ```csharp
     // Good - FTS5 virtual table
     private const string CreateFtsTableSql = @"
         CREATE VIRTUAL TABLE IF NOT EXISTS ClientsSearch 
         USING fts5(Name, PhoneNumber, Notes, content='Clients');
     ";
     
     // Good - FTS5 search query
     public async Task<List<Client>> SearchClientsAsync(string searchTerm)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         return (await connection.QueryAsync<Client>(
             @"SELECT c.* FROM Clients c
               JOIN ClientsSearch s ON c.Id = s.rowid
               WHERE ClientsSearch MATCH @SearchTerm
               ORDER BY rank",
             new { SearchTerm = searchTerm }
         )).ToList();
     }
     ```

5. **❌ UI Thread Blocking**
   - Don't make synchronous database calls from UI thread
   - Example of what to avoid:
     ```csharp
     // BAD PRACTICE - Don't do this!
     private void LoadButton_Click(object sender, RoutedEventArgs e)
     {
         // BAD: Blocks UI thread
         var clients = _connection.Query<Client>("SELECT * FROM Clients");
         ClientsListView.ItemsSource = clients;
     }
     ```

## 8. Maintenance & Monitoring

1. **Regular Database Maintenance**
   - Implement scheduled maintenance tasks
   - Run VACUUM and ANALYZE periodically
   - Example:
     ```csharp
     public async Task PerformMaintenanceAsync()
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         LoggingService.LogInfo("Starting database maintenance", "DatabaseService");
         
         await connection.ExecuteAsync("VACUUM");
         LoggingService.LogInfo("VACUUM completed", "DatabaseService");
         
         await connection.ExecuteAsync("ANALYZE");
         LoggingService.LogInfo("ANALYZE completed", "DatabaseService");
     }
     ```

2. **Query Performance Monitoring**
   - Use EXPLAIN QUERY PLAN to analyze query performance
   - Log slow queries for optimization
   - Example:
     ```csharp
     public async Task<List<Client>> FindClientsWithPerformanceMonitoringAsync(string searchTerm)
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         string query = "SELECT * FROM Clients WHERE Name LIKE @SearchPattern";
         var parameters = new { SearchPattern = $"%{searchTerm}%" };
         
         // Log query plan
         var plan = await connection.QueryAsync<string>(
             $"EXPLAIN QUERY PLAN {query}", 
             parameters
         );
         LoggingService.LogDebug($"Query plan: {string.Join("\n", plan)}", "DatabaseService");
         
         // Execute with timing
         var stopwatch = Stopwatch.StartNew();
         var result = (await connection.QueryAsync<Client>(query, parameters)).ToList();
         stopwatch.Stop();
         
         // Log slow queries
         if (stopwatch.ElapsedMilliseconds > 100)
         {
             LoggingService.LogWarning(
                 $"Slow query ({stopwatch.ElapsedMilliseconds}ms): {query}", 
                 "DatabaseService"
             );
         }
         
         return result;
     }
     ```

3. **Error Logging Integration**
   - Integrate with UFU2 error handling system
   - Log all database exceptions with context
   - Example:
     ```csharp
     public async Task<bool> TrySaveDataAsync(ClientData data)
     {
         try
         {
             using var connection = DatabaseService.CreateConnection();
             await connection.OpenAsync();
             
             await connection.ExecuteAsync(
                 "INSERT INTO Clients (Name, PhoneNumber) VALUES (@Name, @PhoneNumber)",
                 data
             );
             
             return true;
         }
         catch (SqliteException ex) when (ex.SqliteErrorCode == SQLite.SQLiteErrorCode.Constraint)
         {
             // Handle specific error type
             ErrorManager.HandleError(ex, "البيانات المدخلة مكررة", "خطأ في الإدخال", 
                                     LogLevel.Warning, "ClientViewModel");
             return false;
         }
         catch (Exception ex)
         {
             // General error handling
             ErrorManager.HandleError(ex, "فشل في حفظ البيانات", "خطأ في الحفظ", 
                                     LogLevel.Error, "ClientViewModel");
             return false;
         }
     }
     ```

## 9. Quick Performance Wins

1. **WAL Mode Enablement**
   - Enable Write-Ahead Logging for better concurrency
   - Configure at application startup
   - Example:
     ```csharp
     public static void EnableWalMode(SqliteConnection connection)
     {
         using var command = connection.CreateCommand();
         command.CommandText = "PRAGMA journal_mode = WAL;";
         string result = (string)command.ExecuteScalar();
         
         if (result.Equals("wal", StringComparison.OrdinalIgnoreCase))
         {
             LoggingService.LogInfo("WAL mode enabled successfully", "DatabaseService");
         }
         else
         {
             LoggingService.LogWarning($"Failed to enable WAL mode, current mode: {result}", "DatabaseService");
         }
     }
     ```

2. **Index Optimization**
   - Add missing indexes based on query analysis
   - Example:
     ```csharp
     public async Task OptimizeIndexesAsync()
     {
         using var connection = DatabaseService.CreateConnection();
         await connection.OpenAsync();
         
         // Analyze slow queries from logs
         var slowQueries = await GetSlowQueriesFromLogsAsync();
         
         foreach (var query in slowQueries)
         {
             // Get query plan
             var plan = await connection.QueryAsync<string>($"EXPLAIN QUERY PLAN {query.Sql}");
             
             // Log recommendations
             if (plan.Any(p => p.Contains("SCAN TABLE")))
             {
                 LoggingService.LogInfo($"Consider adding index for query: {query.Sql}", "DatabaseOptimizer");
             }
         }
     }
     ```

3. **Background Processing**
   - Move all database operations to background threads
   - Example:
     ```csharp
     public void InitializeBackgroundProcessing()
     {
         // Set up background processing queue
         _processingQueue = new BlockingCollection<DatabaseTask>();
         
         // Start background worker
         Task.Run(() => ProcessDatabaseTasks());
     }
     
     private async Task ProcessDatabaseTasks()
     {
         foreach (var task in _processingQueue.GetConsumingEnumerable())
         {
             try
             {
                 await task.ExecuteAsync();
             }
             catch (Exception ex)
             {
                 ErrorManager.HandleError(ex, "فشل في تنفيذ مهمة قاعدة البيانات", "خطأ في المعالجة", 
                                         LogLevel.Error, "DatabaseService");
             }
         }
     }
     ```

4. **Memory Caching**
   - Implement caching for lookup tables and reference data
   - Example:
     ```csharp
     public class DatabaseCacheService
     {
         private readonly MemoryCache _cache = new(new MemoryCacheOptions());
         private readonly SemaphoreSlim _cacheLock = new(1, 1);
         
         public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
         {
             if (_cache.TryGetValue(key, out T cachedResult))
             {
                 return cachedResult;
             }
             
             await _cacheLock.WaitAsync();
             try
             {
                 // Double-check after acquiring lock
                 if (_cache.TryGetValue(key, out cachedResult))
                 {
                     return cachedResult;
                 }
                 
                 // Create item
                 var result = await factory();
                 
                 // Cache item
                 var cacheOptions = new MemoryCacheEntryOptions();
                 if (expiration.HasValue)
                 {
                     cacheOptions.AbsoluteExpirationRelativeToNow = expiration;
                 }
                 else
                 {
                     cacheOptions.SlidingExpiration = TimeSpan.FromMinutes(30);
                 }
                 
                 _cache.Set(key, result, cacheOptions);
                 return result;
             }
             finally
             {
                 _cacheLock.Release();
             }
         }
     }
     ```

## 10. Recommended NuGet Packages

1. **Core Database Packages**
   - Microsoft.Data.Sqlite: Core SQLite functionality for .NET
   - Dapper: Lightweight ORM for better query performance
   - Example:
     ```csharp
     // Using Microsoft.Data.Sqlite with Dapper
     using Dapper;
     using Microsoft.Data.Sqlite;
     
     public class DatabaseService
     {
         private readonly string _connectionString;
         
         public DatabaseService(string dbPath)
         {
             _connectionString = new SqliteConnectionStringBuilder
             {
                 DataSource = dbPath,
                 Mode = SqliteOpenMode.ReadWriteCreate,
                 Cache = SqliteCacheMode.Shared
             }.ToString();
         }
         
         public SqliteConnection CreateConnection()
         {
             return new SqliteConnection(_connectionString);
         }
         
         public async Task<List<Client>> GetActiveClientsAsync()
         {
             using var connection = CreateConnection();
             await connection.OpenAsync();
             
             return (await connection.QueryAsync<Client>(
                 "SELECT Id, Name, PhoneNumber FROM Clients WHERE IsActive = 1 ORDER BY Name"
             )).ToList();
         }
     }
     ```

2. **Additional Recommended Packages**
   - Microsoft.Extensions.Caching.Memory: Efficient in-memory caching
   - SQLitePCLRaw.bundle_e_sqlite3: SQLite native libraries with extensions
   - Example:
     ```csharp
     // In Startup.cs or equivalent
     public void ConfigureServices(IServiceCollection services)
     {
         // Register database services
         services.AddSingleton<DatabaseService>(provider => 
             new DatabaseService(Path.Combine(AppContext.BaseDirectory, "ufu2.db")));
         
         // Register caching
         services.AddMemoryCache();
         services.AddSingleton<DatabaseCacheService>();
         
         // Configure SQLite
         SQLitePCL.Batteries_V2.Init();
     }
     ```

> **Pro Tip**: Always benchmark before and after optimization. Use `EXPLAIN QUERY PLAN` to identify bottlenecks and verify that your changes actually improve performance in your specific scenario.