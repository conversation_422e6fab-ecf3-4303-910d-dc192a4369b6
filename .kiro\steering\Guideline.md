---
type: "always_apply"
---

---
type: "always_apply"---
Before responding to any query, thoroughly scan the provided or existing WPF/C# codebase for duplicates, redundancies, or repeated patterns. Perform comprehensive analysis across:

**DUPLICATION DETECTION SCOPE:**
- Similar classes, methods, XAML elements, or business logic
- Repeated validation logic patterns across ViewModels
- Duplicate XAML styling, templates, or resource definitions
- Redundant service methods or data access patterns
- Similar ViewModel implementations or property patterns
- Repeated error handling or logging implementations
- Duplicate converter logic or utility methods

If repetition is detected, refactor or reference existing implementations efficiently without recreating. Only then proceed with generating new code or advice, ensuring all output is non-redundant and builds directly on existing UFU2 architecture.

**CRITICAL RESTRICTIONS - NO TESTING:**
- **NO TEST FILES**: Never create, suggest, or reference test files, testing infrastructure, unit tests, integration tests, or any testing-related code
- **NO TEST UTILITIES**: Do not create mock objects, test data files, test configuration files, or testing helper classes
- **NO TESTING FRAMEWORKS**: Do not mention or implement testing frameworks, test projects, or test runners
- **PRODUCTION CODE ONLY**: Focus exclusively on production code that directly serves UFU2's business functionality (client management, activity tracking, document management, etc.)

**UFU2 ARCHITECTURE COMPLIANCE:**
- Follow established MVVM patterns with BaseViewModel inheritance
- Maintain MaterialDesign styling consistency using DynamicResource
- Support Arabic RTL layout requirements in all UI components
- Use existing service patterns (LoggingService, ErrorManager, DatabaseService)
- Implement proper dependency injection through ServiceLocator pattern
- Follow established folder structure and namespace conventions

**CODE QUALITY STANDARDS:**
- Use ErrorManager for all exception handling with Arabic user messages
- Implement logging through LoggingService with appropriate severity levels
- Apply performance optimizations (debouncing, async operations, background threading)
- Ensure proper resource disposal and memory management
- Maintain thread safety for UI operations using Dispatcher
- Follow established database patterns with proper connection management

Focus on clean, maintainable WPF/C# practices for production code only, ensuring all implementations integrate seamlessly with existing UFU2 patterns and enhance the application's core business functionality.

