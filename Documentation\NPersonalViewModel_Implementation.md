# NPersonalViewModel Implementation Guide

## Overview

The `NPersonalViewModel` is a specialized MVVM ViewModel component designed for managing personal information in the UFU2 client registration system. It serves as the data binding layer for personal information forms, providing real-time validation, debounced synchronization with phone number collections, and Arabic RTL support for client data entry.

This ViewModel implements the `IDataErrorInfo` interface for comprehensive validation feedback and inherits from `BaseViewModel` to leverage UFU2's smart batching system for optimal UI performance.

## What the Code Does

### Core Functionality

The `NPersonalViewModel` manages the following personal information fields:
- **French Name (NameFr)**: Required field with validation
- **Arabic Name (NameAr)**: Optional field for Arabic client names
- **Phone Number**: Primary contact with collection synchronization
- **Birth Date**: Flexible date format (DD/MM/YYYY or partial dates)
- **Birth Place**: Optional location information
- **Address**: Optional residential address
- **National ID**: Optional identification number
- **Gender**: Binary selection (Male=0, Female=1) for profile images

### Key Features

1. **Debounced Synchronization**: 300ms debouncing for phone number updates
2. **Real-time Validation**: IDataErrorInfo implementation with Arabic error messages
3. **Collection Integration**: Bidirectional sync with PhoneNumbersCollectionModel
4. **Performance Optimization**: Smart batching through BaseViewModel inheritance
5. **Resource Management**: Proper disposal of timers and event subscriptions

## Architecture Integration

```mermaid
graph TB
    subgraph "UFU2 MVVM Architecture"
        V[NPersonalView.xaml] --> VM[NPersonalViewModel]
        VM --> BVM[BaseViewModel]
        VM --> IDI[IDataErrorInfo]
    end

    subgraph "Service Layer"
        VM --> PIVS[PersonalInfoValidationService]
        VM --> LS[LoggingService]
    end

    subgraph "Model Layer"
        VM --> PNCM[PhoneNumbersCollectionModel]
        VM --> VR[ValidationResult]
    end

    subgraph "UI Components"
        V --> TB1[NameFr TextBox]
        V --> TB2[NameAr TextBox]
        V --> TB3[PhoneNumber TextBox]
        V --> TB4[BirthDate TextBox]
        V --> CB[Gender ComboBox]
    end

    BVM --> SB[Smart Batching System]
    IDI --> VE[Validation Errors]
    PNCM --> PE[PropertyChanged Events]

    subgraph "Component ViewModels"
        VM --> PVM[PersonalInformationViewModel]
        VM --> CVM[ContactInformationViewModel]
        PVM --> PVMD[PersonalInfo Disposal]
        CVM --> CVMD[ContactInfo Disposal]
    end

    subgraph "Memory Management"
        VM --> ST[Sync Timer]
        VM --> ES[Event Subscriptions]
        ST --> TD[Timer Disposal]
        ES --> EU[Event Unsubscription]
    end
```

## How It's Used

### Basic Usage Pattern

```csharp
// Initialize the ViewModel
var personalViewModel = new NPersonalViewModel();

// Set up phone numbers collection for synchronization
var phoneCollection = new PhoneNumbersCollectionModel();
personalViewModel.PhoneNumbersCollection = phoneCollection;

// Bind to UI properties
personalViewModel.NameFr = "Ahmed Benali";
personalViewModel.NameAr = "أحمد بن علي";
personalViewModel.PhoneNumber = "0555123456";
personalViewModel.Gender = 0; // Male
personalViewModel.BirthDate = "15/03/1985";
```

### XAML Data Binding

```xml
<UserControl x:Class="UFU2.Views.NPersonalView"
             FlowDirection="RightToLeft">
    <Grid>
        <!-- French Name (Required) -->
        <TextBox Text="{Binding NameFr, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource MaterialDesignTextBox}"
                 materialDesign:HintAssist.Hint="الاسم بالفرنسية *" />

        <!-- Arabic Name (Optional) -->
        <TextBox Text="{Binding NameAr, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource MaterialDesignTextBox}"
                 materialDesign:HintAssist.Hint="الاسم بالعربية" />

        <!-- Phone Number with Validation -->
        <TextBox Text="{Binding PhoneNumber, ValidatesOnDataErrors=True, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource MaterialDesignTextBox}"
                 materialDesign:HintAssist.Hint="رقم الهاتف" />

        <!-- Gender Selection -->
        <ComboBox SelectedIndex="{Binding Gender}"
                  Style="{StaticResource MaterialDesignComboBox}">
            <ComboBoxItem Content="ذكر" />
            <ComboBoxItem Content="أنثى" />
        </ComboBox>

        <!-- Birth Date -->
        <TextBox Text="{Binding BirthDate, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource MaterialDesignTextBox}"
                 materialDesign:HintAssist.Hint="تاريخ الميلاد (DD/MM/YYYY)" />
    </Grid>
</UserControl>
```

### Advanced Usage with Validation

```csharp
public class ClientRegistrationService
{
    public async Task<bool> ValidateAndSaveClientAsync(NPersonalViewModel personalVM)
    {
        try
        {
            // Perform comprehensive validation
            var validationResult = personalVM.ValidateAll();
            
            if (!validationResult.IsValid)
            {
                // Display Arabic error messages
                foreach (var error in validationResult.Errors)
                {
                    ErrorManager.HandleErrorToast(
                        new ValidationException(error.Value),
                        error.Value,
                        "خطأ في البيانات",
                        LogLevel.Warning,
                        "ClientRegistration"
                    );
                }
                return false;
            }

            // Force immediate synchronization before saving
            personalVM.ForceImmediateSync();

            // Create client data object
            var clientData = new ClientCreationData
            {
                NameFr = personalVM.NameFr,
                NameAr = personalVM.NameAr,
                PhoneNumber = personalVM.PhoneNumber,
                Gender = personalVM.Gender,
                BirthDate = personalVM.BirthDate,
                BirthPlace = personalVM.BirthPlace,
                Address = personalVM.Address,
                NationalId = personalVM.NationalId
            };

            // Save to database
            var clientService = ServiceLocator.GetService<ClientDatabaseService>();
            var clientUid = await clientService.CreateClientAsync(clientData);

            LoggingService.LogInfo($"Client created successfully: {clientUid}", "ClientRegistration");
            return true;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error saving client: {ex.Message}", "ClientRegistration");
            ErrorManager.HandleErrorToast(ex, 
                "حدث خطأ أثناء حفظ بيانات العميل", 
                "خطأ في الحفظ", 
                LogLevel.Error, 
                "ClientRegistration");
            return false;
        }
    }
}
```

### Phone Number Collection Synchronization

```csharp
public class PhoneNumberManagementExample
{
    public void SetupPhoneNumberSync(NPersonalViewModel personalVM)
    {
        // Create phone numbers collection
        var phoneCollection = new PhoneNumbersCollectionModel();
        
        // Add multiple phone numbers
        phoneCollection.AddPhoneNumber("0555123456", PhoneType.Mobile, true);  // Primary
        phoneCollection.AddPhoneNumber("021456789", PhoneType.Home, false);   // Secondary
        
        // Assign to ViewModel - triggers automatic synchronization
        personalVM.PhoneNumbersCollection = phoneCollection;
        
        // The primary phone number is automatically synced to personalVM.PhoneNumber
        Console.WriteLine($"Synced Phone: {personalVM.PhoneNumber}"); // Output: 0555123456
    }

    public void HandlePhoneNumberUpdates(NPersonalViewModel personalVM)
    {
        // Update phone number in ViewModel
        personalVM.PhoneNumber = "0666789012";
        
        // Debounced sync occurs after 300ms
        // To force immediate sync:
        personalVM.RefreshToCollection();
        
        // Verify synchronization
        var primaryPhone = personalVM.PhoneNumbersCollection?.PrimaryPhoneNumber;
        Console.WriteLine($"Collection Phone: {primaryPhone}"); // Output: 0666789012
    }
}
```

## Integration with UFU2 Architecture

### Component ViewModel Pattern

The `NPersonalViewModel` implements a composite pattern with component ViewModels for better separation of concerns:

```csharp
public class NPersonalViewModel : BaseViewModel, IDataErrorInfo
{
    /// <summary>
    /// Gets the personal information management component.
    /// This provides compatibility with the refactored binding structure.
    /// </summary>
    public PersonalInformationViewModel PersonalInfo { get; }

    /// <summary>
    /// Gets the contact information management component.
    /// This provides compatibility with the refactored binding structure.
    /// </summary>
    public ContactInformationViewModel ContactInfo { get; }

    public NPersonalViewModel()
    {
        // Initialize component ViewModels for compatibility
        PersonalInfo = new PersonalInformationViewModel();
        ContactInfo = new ContactInformationViewModel();

        // Synchronize properties between this ViewModel and the component ViewModels
        SetupPropertySynchronization();
    }
}
```

#### Property Synchronization Pattern

The ViewModel maintains bidirectional synchronization between its properties and component ViewModels:

```csharp
private void SetupPropertySynchronization()
{
    // Subscribe to component property changes
    PersonalInfo.PropertyChanged += PersonalInfo_PropertyChanged;
    ContactInfo.PropertyChanged += ContactInfo_PropertyChanged;

    // Subscribe to this ViewModel's property changes
    PropertyChanged += NPersonalViewModel_PropertyChanged;
}

private void PersonalInfo_PropertyChanged(object? sender, PropertyChangedEventArgs e)
{
    // Sync from PersonalInfo to this ViewModel
    switch (e.PropertyName)
    {
        case nameof(PersonalInformationViewModel.NameFr):
            if (_nameFr != PersonalInfo.NameFr)
            {
                _nameFr = PersonalInfo.NameFr;
                OnPropertyChanged(nameof(NameFr));
            }
            break;
        // ... other properties
    }
}
```

### BaseViewModel Integration

The `NPersonalViewModel` leverages UFU2's sophisticated `BaseViewModel` smart batching system:

```csharp
// Property changes are automatically batched for optimal performance
public string NameFr
{
    get => _nameFr;
    set => SetProperty(ref _nameFr, value); // Uses BaseViewModel's smart batching
}

// Critical properties can bypass batching when needed
public string PhoneNumber
{
    get => _phoneNumber;
    set
    {
        if (SetProperty(ref _phoneNumber, value))
        {
            // Trigger debounced synchronization
            DebouncedSyncToCollection();
        }
    }
}
```

### Service Locator Integration

```csharp
public class NPersonalViewModelFactory
{
    public static NPersonalViewModel CreateWithServices()
    {
        var viewModel = new NPersonalViewModel();
        
        // Services are automatically injected through constructor
        // PersonalInfoValidationService is created internally
        // LoggingService is accessed statically
        
        return viewModel;
    }
}
```

### Error Handling Integration

```csharp
// Consistent error handling using UFU2's ErrorManager
private void SyncToCollection()
{
    try
    {
        // Synchronization logic
        PhoneNumbersCollection.SetPrimaryPhoneNumber(phoneNumber);
    }
    catch (Exception ex)
    {
        // UFU2 standard error logging
        LoggingService.LogError($"Error syncing phone number: {ex.Message}", "NPersonalViewModel");
        
        // Could also use ErrorManager for user notifications
        ErrorManager.HandleErrorToast(ex, 
            "حدث خطأ في مزامنة رقم الهاتف", 
            "خطأ في المزامنة", 
            LogLevel.Warning, 
            "NPersonalViewModel");
    }
}
```

## Performance Considerations

### Debounced Synchronization

The ViewModel implements a 300ms debouncing mechanism to prevent excessive synchronization calls:

```mermaid
sequenceDiagram
    participant UI as UI Input
    participant VM as NPersonalViewModel
    participant Timer as DispatcherTimer
    participant Collection as PhoneNumbersCollection

    UI->>VM: PhoneNumber = "0555..."
    VM->>Timer: Stop & Start (300ms)
    UI->>VM: PhoneNumber = "0555123..."
    VM->>Timer: Stop & Start (300ms)
    UI->>VM: PhoneNumber = "0555123456"
    VM->>Timer: Stop & Start (300ms)
    
    Note over Timer: 300ms delay
    Timer->>VM: Tick Event
    VM->>Collection: SetPrimaryPhoneNumber("0555123456")
    Collection-->>VM: Success
```

### Memory Management

The NPersonalViewModel implements comprehensive resource disposal to prevent memory leaks and ensure proper cleanup of component ViewModels:

```csharp
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        try
        {
            // Stop and dispose sync timer
            if (_syncTimer != null)
            {
                _syncTimer.Stop();
                _syncTimer.Tick -= SyncTimer_Tick;
            }

            // Unsubscribe from collection events
            if (_phoneNumbersCollection != null)
            {
                _phoneNumbersCollection.PropertyChanged -= PhoneNumbersCollection_PropertyChanged;
            }

            // Dispose component ViewModels and unsubscribe from their events
            if (PersonalInfo != null)
            {
                PersonalInfo.PropertyChanged -= PersonalInfo_PropertyChanged;
                PersonalInfo.Dispose();
            }

            if (ContactInfo != null)
            {
                ContactInfo.PropertyChanged -= ContactInfo_PropertyChanged;
                ContactInfo.Dispose();
            }

            // Unsubscribe from own property changes to break circular references
            PropertyChanged -= NPersonalViewModel_PropertyChanged;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error disposing NPersonalViewModel: {ex.Message}", "NPersonalViewModel");
        }
    }
    
    base.Dispose(disposing); // Call BaseViewModel disposal
}
```

#### Memory Management Features

1. **Component Disposal**: Properly disposes PersonalInformationViewModel and ContactInformationViewModel
2. **Event Unsubscription**: Removes all event handlers to prevent memory leaks
3. **Timer Cleanup**: Stops and disposes the debouncing timer
4. **Circular Reference Breaking**: Unsubscribes from own PropertyChanged events
5. **Exception Safety**: Wraps disposal logic in try-catch to prevent disposal failures

#### Memory Management Flow

```mermaid
sequenceDiagram
    participant App as Application
    participant VM as NPersonalViewModel
    participant PVM as PersonalInformationViewModel
    participant CVM as ContactInformationViewModel
    participant Timer as SyncTimer
    participant Collection as PhoneNumbersCollection

    App->>VM: Dispose()
    VM->>Timer: Stop()
    VM->>Timer: Unsubscribe Tick Event
    VM->>Collection: Unsubscribe PropertyChanged
    VM->>PVM: Unsubscribe PropertyChanged
    VM->>PVM: Dispose()
    PVM-->>VM: Disposed
    VM->>CVM: Unsubscribe PropertyChanged
    VM->>CVM: Dispose()
    CVM-->>VM: Disposed
    VM->>VM: Unsubscribe own PropertyChanged
    VM->>App: Call base.Dispose()
    
    Note over VM,Collection: All event subscriptions removed
    Note over VM,PVM: Component ViewModels properly disposed
    Note over VM,Timer: Timer stopped and cleaned up
```

### Validation Performance

```csharp
// Efficient validation with early returns
private string? GetValidationError(string propertyName)
{
    switch (propertyName)
    {
        case nameof(NameFr):
            // Only validate required field
            return _validationService.ValidateProperty(propertyName, NameFr);
        default:
            // Skip validation for optional fields
            return null;
    }
}
```

## Data Flow Architecture

```mermaid
flowchart TD
    subgraph "User Input Layer"
        UI1[NameFr TextBox]
        UI2[PhoneNumber TextBox]
        UI3[Gender ComboBox]
    end

    subgraph "ViewModel Layer"
        VM[NPersonalViewModel]
        VT[Validation Timer]
        ST[Sync Timer]
    end

    subgraph "Validation Layer"
        PIVS[PersonalInfoValidationService]
        IDI[IDataErrorInfo]
        VR[ValidationResult]
    end

    subgraph "Model Layer"
        PNCM[PhoneNumbersCollectionModel]
        CD[ClientCreationData]
    end

    subgraph "Service Layer"
        CDS[ClientDatabaseService]
        LS[LoggingService]
    end

    UI1 --> VM
    UI2 --> VM
    UI3 --> VM

    VM --> VT
    VM --> ST
    VM --> PIVS
    VM --> IDI

    PIVS --> VR
    IDI --> VR

    VM --> PNCM
    VM --> CD

    CD --> CDS
    VM --> LS

    ST -.->|300ms Debounce| PNCM
    VT -.->|Real-time| IDI
```

## State Management

### Synchronization States

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> Synchronizing : Property Changed
    Synchronizing --> Debouncing : Start Timer
    Debouncing --> Synchronizing : Timer Tick
    Synchronizing --> Idle : Sync Complete
    
    Idle --> ForcedSync : RefreshFromCollection()
    ForcedSync --> Idle : Immediate Sync
    
    Synchronizing --> Error : Exception
    Error --> Idle : Error Handled
    
    note right of Debouncing
        300ms delay prevents
        excessive sync calls
    end note
    
    note right of ForcedSync
        Bypasses debouncing
        for immediate sync
    end note
```

### Validation States

```mermaid
stateDiagram-v2
    [*] --> Valid
    
    Valid --> Validating : Property Changed
    Validating --> Valid : No Errors
    Validating --> Invalid : Validation Errors
    Invalid --> Validating : Property Changed
    
    Valid --> ForceValidation : ValidateAll()
    ForceValidation --> Valid : All Valid
    ForceValidation --> Invalid : Has Errors
    
    note right of Invalid
        Arabic error messages
        displayed to user
    end note
```

## Error Handling Patterns

### Validation Error Handling

```csharp
public string? this[string columnName]
{
    get
    {
        try
        {
            // Clear previous errors
            _validationErrors.Remove(columnName);

            // Validate property
            string? errorMessage = GetValidationError(columnName);

            if (!string.IsNullOrEmpty(errorMessage))
            {
                _validationErrors[columnName] = errorMessage;
                return errorMessage; // Displayed in UI
            }

            return null; // Valid
        }
        catch (Exception ex)
        {
            // Log error but don't crash UI
            LoggingService.LogError($"Validation error for {columnName}: {ex.Message}", "NPersonalViewModel");
            return null; // Fail gracefully
        }
    }
}
```

### Synchronization Error Handling

```csharp
private void SyncToCollection()
{
    if (_isSynchronizing || PhoneNumbersCollection == null)
        return; // Prevent recursive calls

    try
    {
        _isSynchronizing = true;
        
        // Perform synchronization
        var phoneNumber = PhoneNumber?.Trim() ?? string.Empty;
        PhoneNumbersCollection.SetPrimaryPhoneNumber(phoneNumber);
    }
    catch (Exception ex)
    {
        // Log error for debugging
        LoggingService.LogError($"Sync error: {ex.Message}", "NPersonalViewModel");
        
        // Could notify user if critical
        // ErrorManager.HandleErrorToast(...);
    }
    finally
    {
        _isSynchronizing = false; // Always reset flag
    }
}
```

## Testing Considerations

### Unit Testing Example

```csharp
[TestClass]
public class NPersonalViewModelTests
{
    [TestMethod]
    public void PhoneNumber_WhenChanged_TriggersDebounceSync()
    {
        // Arrange
        var viewModel = new NPersonalViewModel();
        var phoneCollection = new PhoneNumbersCollectionModel();
        viewModel.PhoneNumbersCollection = phoneCollection;

        // Act
        viewModel.PhoneNumber = "0555123456";

        // Assert - immediate check (before debounce)
        Assert.AreNotEqual("0555123456", phoneCollection.PrimaryPhoneNumber);

        // Wait for debounce
        Thread.Sleep(350);

        // Assert - after debounce
        Assert.AreEqual("0555123456", phoneCollection.PrimaryPhoneNumber);
    }

    [TestMethod]
    public void ValidateAll_WithEmptyNameFr_ReturnsError()
    {
        // Arrange
        var viewModel = new NPersonalViewModel();
        viewModel.NameFr = string.Empty;

        // Act
        var result = viewModel.ValidateAll();

        // Assert
        Assert.IsFalse(result.IsValid);
        Assert.IsTrue(result.Errors.ContainsKey(nameof(viewModel.NameFr)));
    }
}
```

## Best Practices

### 1. Property Binding
```csharp
// ✅ Good: Use SetProperty for automatic change notification
public string NameFr
{
    get => _nameFr;
    set => SetProperty(ref _nameFr, value);
}

// ❌ Bad: Manual property change notification
public string NameFr
{
    get => _nameFr;
    set
    {
        _nameFr = value;
        OnPropertyChanged(nameof(NameFr));
    }
}
```

### 2. Resource Management
```csharp
// ✅ Good: Comprehensive disposal with component cleanup
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        try
        {
            // Stop and dispose sync timer
            if (_syncTimer != null)
            {
                _syncTimer.Stop();
                _syncTimer.Tick -= SyncTimer_Tick;
            }

            // Unsubscribe from collection events
            if (_phoneNumbersCollection != null)
            {
                _phoneNumbersCollection.PropertyChanged -= PhoneNumbersCollection_PropertyChanged;
            }

            // Dispose component ViewModels and unsubscribe from their events
            if (PersonalInfo != null)
            {
                PersonalInfo.PropertyChanged -= PersonalInfo_PropertyChanged;
                PersonalInfo.Dispose();
            }

            if (ContactInfo != null)
            {
                ContactInfo.PropertyChanged -= ContactInfo_PropertyChanged;
                ContactInfo.Dispose();
            }

            // Unsubscribe from own property changes to break circular references
            PropertyChanged -= NPersonalViewModel_PropertyChanged;
        }
        catch (Exception ex)
        {
            LoggingService.LogError($"Error disposing NPersonalViewModel: {ex.Message}", "NPersonalViewModel");
        }
    }
    base.Dispose(disposing);
}
```

### 3. Error Handling
```csharp
// ✅ Good: Comprehensive error handling with logging
private void SyncToCollection()
{
    try
    {
        // Synchronization logic
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error: {ex.Message}", "NPersonalViewModel");
        // Handle gracefully
    }
    finally
    {
        _isSynchronizing = false;
    }
}
```

## Conclusion

The `NPersonalViewModel` demonstrates UFU2's commitment to performance, maintainability, and user experience. Its debounced synchronization, comprehensive validation, and proper resource management make it a robust component for personal information management in the Algerian business registration system.

Key strengths include:
- **Performance**: Debounced updates and smart batching
- **Reliability**: Comprehensive error handling and logging
- **Usability**: Arabic RTL support and real-time validation
- **Maintainability**: Clean MVVM architecture and proper disposal
- **Integration**: Seamless integration with UFU2's service architecture