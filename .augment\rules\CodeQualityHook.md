---
type: "always_apply"
---

"Analyze the Codebase for potential improvements. Focus on:

1. **Code Quality Analysis**:
- Identify code smells and anti-patterns   
- Check for SOLID principle violations   
- Review naming conventions and consistency 
- Assess method complexity and length

2. **WPF/MVVM Best Practices**:
- Verify proper MVVM pattern implementation 
- Check data binding practices 
- Review command usage and implementation   
- Assess view-viewmodel separation

3. **Performance Optimizations**:  
- Identify potential memory leaks   
- Review database query efficiency   
- Check for unnecessary object allocations   
- Assess UI thread blocking operations

4. **Maintainability Improvements**:
- Suggest better error handling patterns    
- Recommend code documentation improvements    
- Identify opportunities for code reuse    
- Propose refactoring for better readability  

5. **UFU2-Specific Considerations**:    
- Ensure consistency with existing architecture patterns    
- Verify proper use of ServiceLocator and dependency injection    
- Check Material Design theme compliance    
- Review database service usage patterns  

**Guidelines**: 
- Implement the simplest solution that meets requirements 
- Leverage existing patterns and code before creating new solutions 
- Provide comprehensive documentation for all suggested changes 
- Analyze root causes thoroughly before proposing fixes 
- Provide minimal, precise solutions 
- Document issue and resolution clearly  

Provide specific, actionable recommendations with code examples where appropriate. Maintain the existing functionality while improving code quality.