# ClientValidationService Documentation

## Overview

The `ClientValidationService` provides comprehensive validation for client data, activity types, phone numbers, and business rules in UFU2. It ensures data integrity at the application level with Arabic error messages and integrates with the database validation layer.

## What the Code Does

The service validates:
- **Client Data**: Personal information, required fields, format validation
- **Activity Types**: Business activity validation with type-specific rules
- **Phone Numbers**: Format validation, uniqueness checking, primary phone rules
- **File Check Requirements**: Activity type-specific file check validation
- **Business Rules**: Complex validation logic for UFU2 business requirements

## How It's Used

### Basic Client Validation

```csharp
// Get service from ServiceLocator
var validationService = ServiceLocator.GetService<ClientValidationService>();

// Validate client creation data
var clientData = new ClientCreationData
{
    FirstName = "أحمد",
    LastName = "محمد",
    Gender = "Male",
    BirthDate = DateTime.Now.AddYears(-25)
};

var validationResult = await validationService.ValidateClientDataAsync(clientData);

if (!validationResult.IsValid)
{
    foreach (var error in validationResult.Errors)
    {
        // Display Arabic error messages to user
        MessageBox.Show(error.ErrorMessage);
    }
}
```

### Activity Type Validation

```csharp
public async Task ValidateActivityAsync(ActivityCreationData activityData)
{
    var validationService = ServiceLocator.GetService<ClientValidationService>();
    
    // Validate activity data
    var result = await validationService.ValidateActivityDataAsync(activityData);
    
    if (!result.IsValid)
    {
        var errorMessages = string.Join("\n", result.Errors.Select(e => e.ErrorMessage));
        ErrorManager.HandleError(new ValidationException(errorMessages), "خطأ في بيانات النشاط");
        return;
    }
    
    // Validate file check requirements for activity type
    var fileCheckResult = await validationService.ValidateFileCheckRequirementsAsync(
        activityData.ActivityType, 
        activityData.FileCheckStates
    );
    
    if (!fileCheckResult.IsValid)
    {
        foreach (var error in fileCheckResult.Errors)
        {
            LoggingService.LogWarning($"File check validation: {error.ErrorMessage}");
        }
    }
}
```

### Phone Number Validation

```csharp
public async Task ValidatePhoneNumbersAsync(string clientId, List<PhoneNumberData> phoneNumbers)
{
    var validationService = ServiceLocator.GetService<ClientValidationService>();
    
    foreach (var phoneData in phoneNumbers)
    {
        // Validate phone number format
        var formatResult = validationService.ValidatePhoneNumberFormat(phoneData.PhoneNumber);
        
        if (!formatResult.IsValid)
        {
            throw new ValidationException($"رقم الهاتف غير صحيح: {phoneData.PhoneNumber}");
        }
        
        // Check for duplicates
        var uniquenessResult = await validationService.ValidatePhoneNumberUniquenessAsync(
            clientId, 
            phoneData.PhoneNumber
        );
        
        if (!uniquenessResult.IsValid)
        {
            throw new ValidationException("رقم الهاتف مستخدم من قبل عميل آخر");
        }
    }
    
    // Validate primary phone rules
    var primaryPhoneResult = validationService.ValidatePrimaryPhoneRules(phoneNumbers);
    
    if (!primaryPhoneResult.IsValid)
    {
        throw new ValidationException("يجب تحديد رقم هاتف أساسي واحد فقط");
    }
}
```

### Comprehensive Validation with Error Aggregation

```csharp
public async Task<ClientValidationResult> ValidateCompleteClientDataAsync(ClientCreationData clientData)
{
    var validationService = ServiceLocator.GetService<ClientValidationService>();
    var aggregatedResult = new ClientValidationResult();
    
    try
    {
        // Validate basic client information
        var clientResult = await validationService.ValidateClientDataAsync(clientData);
        aggregatedResult.AddValidationResult(clientResult);
        
        // Validate each activity
        foreach (var activity in clientData.Activities ?? new List<ActivityCreationData>())
        {
            var activityResult = await validationService.ValidateActivityDataAsync(activity);
            aggregatedResult.AddValidationResult(activityResult);
            
            // Validate file check requirements
            var fileCheckResult = await validationService.ValidateFileCheckRequirementsAsync(
                activity.ActivityType, 
                activity.FileCheckStates
            );
            aggregatedResult.AddValidationResult(fileCheckResult);
        }
        
        // Validate phone numbers
        if (clientData.PhoneNumbers?.Any() == true)
        {
            var phoneResult = await validationService.ValidatePhoneNumberCollectionAsync(
                clientData.PhoneNumbers
            );
            aggregatedResult.AddValidationResult(phoneResult);
        }
        
        // Validate business rules
        var businessRuleResult = await validationService.ValidateBusinessRulesAsync(clientData);
        aggregatedResult.AddValidationResult(businessRuleResult);
        
        return aggregatedResult;
    }
    catch (Exception ex)
    {
        ErrorManager.HandleError(ex, "خطأ في التحقق من صحة البيانات");
        aggregatedResult.AddError("حدث خطأ أثناء التحقق من صحة البيانات");
        return aggregatedResult;
    }
}
```

### Custom Validation Rules

```csharp
public async Task<ValidationResult> ValidateCustomBusinessRuleAsync(ClientCreationData clientData)
{
    var validationService = ServiceLocator.GetService<ClientValidationService>();
    
    // Example: Validate that craft activities have appropriate age requirements
    var result = new ValidationResult();
    
    foreach (var activity in clientData.Activities?.Where(a => a.ActivityType == "Craft") ?? Enumerable.Empty<ActivityCreationData>())
    {
        var age = DateTime.Now.Year - clientData.BirthDate?.Year ?? 0;
        
        if (age < 18)
        {
            result.AddError("يجب أن يكون عمر صاحب النشاط الحرفي 18 سنة على الأقل");
        }
        
        // Validate craft-specific requirements
        var craftValidation = await validationService.ValidateCraftActivityRequirementsAsync(activity);
        result.AddValidationResult(craftValidation);
    }
    
    return result;
}
```

## Integration with UFU2 Architecture

The service integrates with UFU2's established patterns:

- **ServiceLocator Pattern**: Registered for dependency injection
- **MVVM Integration**: Designed to work with ViewModel validation patterns
- **Error Handling**: Uses `ErrorManager` for consistent Arabic error messages
- **Logging**: Integrates with `LoggingService` for validation tracking
- **Database Integration**: Works with database constraints for dual-layer validation
- **Localization**: Provides Arabic error messages for user-facing validation

## Performance Considerations

- **Async Validation**: All database-dependent validations are asynchronous
- **Caching**: Caches validation rules and lookup data for performance
- **Batch Validation**: Supports validating multiple items efficiently
- **Early Exit**: Stops validation on critical errors to improve performance
- **Lazy Loading**: Loads validation data only when needed
- **Memory Efficient**: Minimal memory footprint for validation operations

## Mermaid Diagram

```mermaid
graph TB
    subgraph "Validation Service Architecture"
        CVS[ClientValidationService]
        VR[Validation Rules]
        VE[Validation Engine]
        ER[Error Reporter]
    end
    
    subgraph "Validation Types"
        CVS --> CDV[Client Data Validation]
        CVS --> ADV[Activity Data Validation]
        CVS --> PNV[Phone Number Validation]
        CVS --> FCV[File Check Validation]
        CVS --> BRV[Business Rule Validation]
    end
    
    subgraph "Data Sources"
        VR --> DB[(Database Constraints)]
        VR --> BR[Business Rules]
        VR --> FR[Format Rules]
        VR --> UR[Uniqueness Rules]
    end
    
    subgraph "Integration Points"
        CVS --> DS[DatabaseService]
        CVS --> EM[ErrorManager]
        CVS --> LS[LoggingService]
        ER --> UI[User Interface]
    end
    
    subgraph "Validation Flow"
        Input[Input Data] --> VE
        VE --> VR
        VR --> Result[Validation Result]
        Result --> ER
        ER --> Output[Error Messages]
    end
    
    style CVS fill:#e1f5fe
    style Result fill:#e8f5e8
    style Output fill:#ffebee
    style UI fill:#f3e5f5
```

## Validation Process Flow

```mermaid
sequenceDiagram
    participant VM as ViewModel
    participant CVS as ClientValidationService
    participant VE as Validation Engine
    participant DS as DatabaseService
    participant EM as ErrorManager
    participant UI as User Interface

    VM->>CVS: ValidateClientDataAsync(clientData)
    CVS->>VE: Start Validation Process
    
    VE->>VE: Validate Required Fields
    VE->>VE: Validate Data Formats
    VE->>DS: Check Uniqueness Constraints
    DS->>VE: Return Uniqueness Results
    VE->>VE: Apply Business Rules
    
    VE->>CVS: Return Validation Results
    
    alt Validation Successful
        CVS->>VM: Return Success Result
        VM->>UI: Proceed with Operation
    else Validation Failed
        CVS->>EM: Format Arabic Error Messages
        EM->>CVS: Return Formatted Messages
        CVS->>VM: Return Error Result
        VM->>UI: Display Error Messages
    end
```

## Validation State Machine

```mermaid
stateDiagram-v2
    [*] --> Initializing: Start Validation
    Initializing --> ValidatingRequired: Check Required Fields
    
    ValidatingRequired --> ValidatingFormat: Required Fields OK
    ValidatingRequired --> ValidationFailed: Missing Required Fields
    
    ValidatingFormat --> ValidatingUniqueness: Format Valid
    ValidatingFormat --> ValidationFailed: Invalid Format
    
    ValidatingUniqueness --> ValidatingBusinessRules: Uniqueness OK
    ValidatingUniqueness --> ValidationFailed: Duplicate Found
    
    ValidatingBusinessRules --> ValidationSucceeded: Rules Passed
    ValidatingBusinessRules --> ValidationFailed: Rules Violated
    
    ValidationSucceeded --> [*]: Return Success
    ValidationFailed --> [*]: Return Errors
```