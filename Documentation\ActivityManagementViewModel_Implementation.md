# ActivityManagementViewModel Implementation Guide

## Overview

The `ActivityManagementViewModel` is a specialized MVVM component extracted from the larger `NewClientViewModel` to handle client activity management in the UFU2 application. This component follows the single responsibility principle by focusing exclusively on managing business activities across four distinct types: Main Commercial, Secondary Commercial, Craft, and Professional activities. It provides comprehensive tab-based activity management with data persistence, CPI location handling, payment year tracking, and file check state management.

## What the Code Does

The ActivityManagementViewModel manages the following core functionalities:

### Core Activity Management
- **Multi-Activity Type Support**: Handles MainCommercial, SecondaryCommercial, Craft, and Professional activities
- **Tab-Based Data Persistence**: Maintains separate data models for each activity type with seamless tab switching
- **Activity Status Management**: Tracks and updates activity status with default values
- **Multiple Activities Support**: Manages collections of activities for Commercial activity types

### CPI Location Management
- **Wilaya/Daira Cascading Selection**: Implements hierarchical location selection with proper validation
- **Location Synchronization**: Automatically synchronizes location selections with current activity data
- **Data Loading**: Asynchronously loads CPI location data from the database service

### Payment Year Tracking
- **G12Check Payment Years**: Manages G12 payment year selections per activity type
- **BisCheck Payment Years**: Handles BIS payment year tracking with display text formatting
- **Tab-Specific Persistence**: Maintains separate payment year collections for each activity type

### File Check State Management
- **Regulatory Compliance**: Tracks file check completion status for different document types
- **Activity-Specific Rules**: Applies different file check requirements based on activity type
- **State Persistence**: Maintains file check states independently across activity tabs

## How It's Used

### Basic Usage Pattern

```csharp
// Initialize the ViewModel
var activityVM = new ActivityManagementViewModel();

// Set activity type and basic information
activityVM.SelectedActivityType = "MainCommercial";
activityVM.CurrentActivity.ActivityStatus = "نشط";
activityVM.CurrentActivity.CommercialRegister = "123456789";
activityVM.CurrentActivity.NifNumber = "NIF123456";
activityVM.CurrentActivity.NisNumber = "NIS789012";

// Set CPI location
activityVM.SelectedCpiWilaya = availableWilayas.First(w => w.NameAr == "الجزائر");
activityVM.SelectedCpiDaira = availableDairas.First(d => d.NameAr == "سيدي أمحمد");

// Configure payment years
activityVM.G12SelectedYears = new List<int> { 2022, 2023, 2024 };
activityVM.BISSelectedYears = new List<int> { 2023, 2024 };

// Validate the activity data
if (activityVM.IsValid())
{
    LoggingService.LogInfo("Activity data is valid", "ActivityManagement");
}
```

### Tab Switching with Data Persistence

```csharp
// Switch between activity types while preserving data
public void DemonstrateTabSwitching(ActivityManagementViewModel activityVM)
{
    // Configure Main Commercial activity
    activityVM.SelectedActivityType = "MainCommercial";
    activityVM.CurrentActivity.CommercialRegister = "MC123456";
    activityVM.CurrentActivity.NifNumber = "NIF001";
    
    // Switch to Craft activity - Main Commercial data is preserved
    activityVM.SelectedActivityType = "Craft";
    activityVM.CurrentActivity.ArtNumber = "ART789012";
    activityVM.CurrentActivity.ActivityStatus = "نشط";
    
    // Switch back to Main Commercial - data is still there
    activityVM.SelectedActivityType = "MainCommercial";
    // activityVM.CurrentActivity.CommercialRegister is still "MC123456"
    
    LoggingService.LogInfo("Tab switching completed with data persistence", "ActivityManagement");
}
```

### CPI Location Management

```csharp
// Handle CPI location selection with cascading updates
public async Task ConfigureCpiLocationAsync(ActivityManagementViewModel activityVM)
{
    try
    {
        // Load available wilayas (automatically done in constructor)
        await Task.Delay(100); // Wait for async loading to complete
        
        // Select a wilaya - this triggers daira loading
        var algerWilaya = activityVM.CpiWilayas.FirstOrDefault(w => w.NameAr == "الجزائر");
        if (algerWilaya != null)
        {
            activityVM.SelectedCpiWilaya = algerWilaya;
            
            // Wait for dairas to load
            await Task.Delay(500);
            
            // Select a daira
            var sidiAmhamedDaira = activityVM.CpiDairas.FirstOrDefault(d => d.NameAr == "سيدي أمحمد");
            if (sidiAmhamedDaira != null)
            {
                activityVM.SelectedCpiDaira = sidiAmhamedDaira;
                
                // Verify the current activity has been updated
                Debug.Assert(activityVM.CurrentActivity.CpiWilaya == "الجزائر");
                Debug.Assert(activityVM.CurrentActivity.CpiDaira == "سيدي أمحمد");
            }
        }
        
        LoggingService.LogInfo("CPI location configured successfully", "ActivityManagement");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error configuring CPI location: {ex.Message}", "ActivityManagement");
        throw;
    }
}
```

### Payment Years Management

```csharp
// Configure payment years for different activity types
public void ConfigurePaymentYears(ActivityManagementViewModel activityVM)
{
    // Configure Main Commercial payment years
    activityVM.SelectedActivityType = "MainCommercial";
    activityVM.G12SelectedYears = new List<int> { 2020, 2021, 2022, 2023 };
    activityVM.BISSelectedYears = new List<int> { 2022, 2023, 2024 };
    
    // Verify display text is updated
    Debug.Assert(activityVM.G12DisplayText == "2020, 2021, 2022, 2023");
    Debug.Assert(activityVM.BISDisplayText == "2022, 2023, 2024");
    
    // Configure Craft activity payment years
    activityVM.SelectedActivityType = "Craft";
    activityVM.G12SelectedYears = new List<int> { 2023, 2024 };
    activityVM.BISSelectedYears = new List<int> { 2024 };
    
    // Switch back to Main Commercial - original years are preserved
    activityVM.SelectedActivityType = "MainCommercial";
    Debug.Assert(activityVM.G12SelectedYears.Count == 4);
    Debug.Assert(activityVM.BISSelectedYears.Count == 3);
    
    LoggingService.LogInfo("Payment years configured for multiple activity types", "ActivityManagement");
}
```

### File Check State Management

```csharp
// Configure file check states based on activity type
public void ConfigureFileCheckStates(ActivityManagementViewModel activityVM)
{
    // Configure Main Commercial file checks
    activityVM.SelectedActivityType = "MainCommercial";
    var commercialFileChecks = activityVM.CurrentFileCheckStates;
    commercialFileChecks.CasFileCheck = true;
    commercialFileChecks.NifFileCheck = true;
    commercialFileChecks.NisFileCheck = true;
    commercialFileChecks.RcFileCheck = true;  // Required for Commercial
    commercialFileChecks.DexFileCheck = false;
    
    // Configure Craft activity file checks
    activityVM.SelectedActivityType = "Craft";
    var craftFileChecks = activityVM.CurrentFileCheckStates;
    craftFileChecks.CasFileCheck = true;
    craftFileChecks.NifFileCheck = true;
    craftFileChecks.NisFileCheck = true;
    craftFileChecks.ArtFileCheck = true;  // Required for Craft
    craftFileChecks.DexFileCheck = false;
    
    // Configure Professional activity file checks
    activityVM.SelectedActivityType = "Professional";
    var professionalFileChecks = activityVM.CurrentFileCheckStates;
    professionalFileChecks.CasFileCheck = true;
    professionalFileChecks.NifFileCheck = true;
    professionalFileChecks.NisFileCheck = true;
    professionalFileChecks.AgrFileCheck = true;  // Required for Professional
    professionalFileChecks.DexFileCheck = false;
    
    LoggingService.LogInfo("File check states configured for all activity types", "ActivityManagement");
}
```

### Command Usage and Event Handling

```csharp
// Handle activity management commands
public class ActivityManagementService
{
    private readonly ActivityManagementViewModel _activityVM;
    
    public ActivityManagementService()
    {
        _activityVM = new ActivityManagementViewModel();
        
        // Subscribe to events
        _activityVM.EditStatusRequested += OnEditStatusRequested;
    }
    
    public void ExecuteActivityCommands()
    {
        // Execute add activity command
        if (_activityVM.AddActivityCommand.CanExecute(null))
        {
            _activityVM.AddActivityCommand.Execute(null);
        }
        
        // Execute edit status command
        if (_activityVM.EditStatusCommand.CanExecute(null))
        {
            _activityVM.EditStatusCommand.Execute(null);
        }
        
        // Execute payment year selection commands
        if (_activityVM.SelectG12YearsCommand.CanExecute(null))
        {
            _activityVM.SelectG12YearsCommand.Execute(null);
        }
        
        if (_activityVM.SelectBISYearsCommand.CanExecute(null))
        {
            _activityVM.SelectBISYearsCommand.Execute(null);
        }
    }
    
    private void OnEditStatusRequested()
    {
        // Handle edit status request - typically opens a dialog
        LoggingService.LogInfo("Edit status dialog should be opened", "ActivityManagementService");
        
        // Example: Open ActivityStatusUpdateDialog
        // var dialog = new ActivityStatusUpdateDialog(_activityVM.CurrentActivity);
        // dialog.ShowDialog();
    }
}
```

### Advanced Usage with Complete Activity Setup

```csharp
public async Task<bool> SetupCompleteActivityAsync(ActivityManagementViewModel activityVM, string activityType)
{
    try
    {
        // Set activity type
        activityVM.SelectedActivityType = activityType;
        
        // Configure basic activity information
        var currentActivity = activityVM.CurrentActivity;
        currentActivity.ActivityStatus = "نشط";
        currentActivity.ActivityStartDate = DateTime.Now.ToString("dd/MM/yyyy");
        
        // Configure activity-specific fields
        switch (activityType)
        {
            case "MainCommercial":
                currentActivity.CommercialRegister = "RC123456789";
                currentActivity.NifNumber = "NIF001234567";
                currentActivity.NisNumber = "NIS987654321";
                break;
                
            case "Craft":
                currentActivity.ArtNumber = "ART456789012";
                currentActivity.NifNumber = "NIF001234567";
                currentActivity.NisNumber = "NIS987654321";
                break;
                
            case "Professional":
                currentActivity.NifNumber = "NIF001234567";
                currentActivity.NisNumber = "NIS987654321";
                // AGR number would be set through professional-specific dialog
                break;
        }
        
        // Configure CPI location
        await ConfigureCpiLocationAsync(activityVM);
        
        // Configure payment years
        activityVM.G12SelectedYears = new List<int> { 2023, 2024 };
        activityVM.BISSelectedYears = new List<int> { 2024 };
        
        // Configure file check states based on activity type
        ConfigureFileCheckStatesByType(activityVM, activityType);
        
        // Validate the complete setup
        if (!activityVM.IsValid())
        {
            LoggingService.LogWarning($"Activity setup validation failed for {activityType}", "ActivityManagement");
            return false;
        }
        
        LoggingService.LogInfo($"Complete activity setup successful for {activityType}", "ActivityManagement");
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error setting up activity {activityType}: {ex.Message}", "ActivityManagement");
        ErrorManager.HandleErrorToast(ex,
            $"حدث خطأ أثناء إعداد نشاط {GetActivityTypeArabicName(activityType)}",
            "خطأ في إعداد النشاط",
            LogLevel.Error,
            "ActivityManagement");
        return false;
    }
}

private void ConfigureFileCheckStatesByType(ActivityManagementViewModel activityVM, string activityType)
{
    var fileChecks = activityVM.CurrentFileCheckStates;
    
    // Common file checks for all activity types
    fileChecks.CasFileCheck = true;
    fileChecks.NifFileCheck = true;
    fileChecks.NisFileCheck = true;
    
    // Activity-specific file checks
    switch (activityType)
    {
        case "MainCommercial":
        case "SecondaryCommercial":
            fileChecks.RcFileCheck = true;
            fileChecks.ArtFileCheck = false;
            fileChecks.AgrFileCheck = false;
            break;
            
        case "Craft":
            fileChecks.RcFileCheck = false;
            fileChecks.ArtFileCheck = true;
            fileChecks.AgrFileCheck = false;
            break;
            
        case "Professional":
            fileChecks.RcFileCheck = false;
            fileChecks.ArtFileCheck = false;
            fileChecks.AgrFileCheck = true;
            break;
    }
    
    fileChecks.DexFileCheck = false; // Usually set later in the process
}

private string GetActivityTypeArabicName(string activityType)
{
    return activityType switch
    {
        "MainCommercial" => "التجاري الرئيسي",
        "SecondaryCommercial" => "التجاري الثانوي",
        "Craft" => "الحرفي",
        "Professional" => "المهني",
        _ => "غير محدد"
    };
}
```

### XAML Data Binding Example

```xml
<UserControl x:Class="UFU2.Views.UserControls.ActivityManagementUserControl"
             FlowDirection="RightToLeft">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Activity Type Selection -->
        <ComboBox Grid.Row="0"
                  SelectedValue="{Binding SelectedActivityType}"
                  materialDesign:HintAssist.Hint="نوع النشاط"
                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                  Margin="0,5">
            <ComboBoxItem Content="التجاري الرئيسي" Tag="MainCommercial"/>
            <ComboBoxItem Content="التجاري الثانوي" Tag="SecondaryCommercial"/>
            <ComboBoxItem Content="الحرفي" Tag="Craft"/>
            <ComboBoxItem Content="المهني" Tag="Professional"/>
        </ComboBox>
        
        <!-- Activity Status -->
        <TextBox Grid.Row="1"
                 Text="{Binding CurrentActivity.ActivityStatus, UpdateSourceTrigger=PropertyChanged}"
                 materialDesign:HintAssist.Hint="حالة النشاط"
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 Margin="0,5"/>
        
        <!-- CPI Wilaya Selection -->
        <ComboBox Grid.Row="2"
                  ItemsSource="{Binding CpiWilayas}"
                  SelectedItem="{Binding SelectedCpiWilaya}"
                  DisplayMemberPath="NameAr"
                  materialDesign:HintAssist.Hint="الولاية"
                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                  Margin="0,5"/>
        
        <!-- CPI Daira Selection -->
        <ComboBox Grid.Row="3"
                  ItemsSource="{Binding CpiDairas}"
                  SelectedItem="{Binding SelectedCpiDaira}"
                  DisplayMemberPath="NameAr"
                  materialDesign:HintAssist.Hint="الدائرة"
                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                  Margin="0,5"/>
        
        <!-- Payment Years Display -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" Margin="0,10">
            <Button Content="اختيار سنوات G12"
                    Command="{Binding SelectG12YearsCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"/>
            <TextBlock Text="{Binding G12DisplayText}"
                       VerticalAlignment="Center"
                       Style="{StaticResource MaterialDesignBody2TextBlock}"/>
        </StackPanel>
        
        <StackPanel Grid.Row="5" Orientation="Horizontal" Margin="0,5">
            <Button Content="اختيار سنوات BIS"
                    Command="{Binding SelectBISYearsCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"/>
            <TextBlock Text="{Binding BISDisplayText}"
                       VerticalAlignment="Center"
                       Style="{StaticResource MaterialDesignBody2TextBlock}"/>
        </StackPanel>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="6" Orientation="Horizontal" Margin="0,15">
            <Button Content="إضافة نشاط"
                    Command="{Binding AddActivityCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0"/>
            <Button Content="تعديل الحالة"
                    Command="{Binding EditStatusCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"/>
        </StackPanel>
    </Grid>
</UserControl>
```

## Integration with UFU2 Architecture

### MVVM Pattern Compliance

The ActivityManagementViewModel follows UFU2's MVVM architecture:

```csharp
// Inherits from BaseViewModel for smart batching
public class ActivityManagementViewModel : BaseViewModel
{
    // Uses SetProperty for optimized property notifications
    public string SelectedActivityType
    {
        get => _selectedActivityType;
        set
        {
            if (SetProperty(ref _selectedActivityType, value))
            {
                // Trigger related property updates
                OnPropertyChanged(nameof(CurrentActivity));
                OnPropertyChanged(nameof(CurrentFileCheckStates));
                // ... other related properties
            }
        }
    }
}
```

### Service Integration

```csharp
// Integration with UFU2 services through ServiceLocator
public class ActivityManagementViewModel : BaseViewModel
{
    private readonly ActivityTypeBaseService? _activityTypeService;
    private readonly CraftTypeBaseService? _craftTypeService;
    private readonly CpiLocationService? _cpiLocationService;
    
    public ActivityManagementViewModel()
    {
        // Service injection through ServiceLocator pattern
        _activityTypeService = ServiceLocator.GetService<ActivityTypeBaseService>();
        _craftTypeService = ServiceLocator.GetService<CraftTypeBaseService>();
        _cpiLocationService = ServiceLocator.GetService<CpiLocationService>();
        
        // Initialize with async data loading
        _ = LoadCpiLocationDataAsync();
    }
}
```

### Integration with NewClientViewModel

```csharp
public class NewClientViewModel : BaseViewModel
{
    private ActivityManagementViewModel _activityManagement;
    
    public ActivityManagementViewModel ActivityManagement
    {
        get => _activityManagement;
        set => SetProperty(ref _activityManagement, value);
    }
    
    public NewClientViewModel()
    {
        ActivityManagement = new ActivityManagementViewModel();
        
        // Subscribe to activity management events
        ActivityManagement.EditStatusRequested += OnActivityEditStatusRequested;
        ActivityManagement.PropertyChanged += OnActivityManagementPropertyChanged;
    }
    
    private void OnActivityEditStatusRequested()
    {
        // Handle edit status request - open dialog
        var dialog = new ActivityStatusUpdateDialog(ActivityManagement.CurrentActivity);
        dialog.ShowDialog();
    }
    
    private void OnActivityManagementPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        // React to activity management changes
        if (e.PropertyName == nameof(ActivityManagement.SelectedActivityType))
        {
            // Update UI or perform validation
            ValidateClientData();
        }
    }
}
```

### Error Handling Integration

```csharp
// Uses UFU2's ErrorManager for consistent error handling
private void HandleActivityError(Exception ex, string context)
{
    ErrorManager.HandleErrorToast(ex,
        "حدث خطأ في إدارة الأنشطة",
        "خطأ في الأنشطة",
        LogLevel.Error,
        context);
}
```

## Performance Considerations

### Memory Management

The ViewModel implements proper resource disposal:

```csharp
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        // Clear collections to prevent memory leaks
        _mainCommercialActivities?.Clear();
        _secondaryCommercialActivities?.Clear();
        _cpiWilayas?.Clear();
        _cpiDairas?.Clear();
        
        LoggingService.LogDebug("ActivityManagementViewModel disposed", "ActivityManagementViewModel");
    }
    base.Dispose(disposing);
}
```

### Smart Property Batching

Inherits BaseViewModel's smart batching system for optimal UI performance:

- **Normal Priority**: Standard property changes batched at 16ms intervals (60 FPS)
- **High Priority**: Frequent changes batched at 8ms intervals (120 FPS)  
- **Critical Priority**: Immediate notifications bypass batching

### Efficient Data Loading

Asynchronous data loading prevents UI blocking:

```csharp
// Non-blocking CPI location data loading
private async Task LoadCpiLocationDataAsync()
{
    try
    {
        if (_cpiLocationService == null) return;
        
        var wilayas = await _cpiLocationService.GetAllWilayasAsync();
        _cpiWilayas.Clear();
        _cpiWilayas.AddRange(wilayas);
        
        LoggingService.LogInfo($"Loaded {wilayas.Count} CPI Wilayas", "ActivityManagementViewModel");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error loading CPI location data: {ex.Message}", "ActivityManagementViewModel");
    }
}
```

### Optimized Tab Switching

Tab switching uses efficient property switching without data recreation:

```csharp
// Efficient tab switching with preserved data
public ActivityModel CurrentActivity
{
    get
    {
        return SelectedActivityType switch
        {
            "MainCommercial" => _mainCommercialActivity,
            "SecondaryCommercial" => _secondaryCommercialActivity,
            "Craft" => _craftActivity,
            "Professional" => _professionalActivity,
            _ => _mainCommercialActivity // Default fallback
        };
    }
}
```

## Component Architecture Diagram

```mermaid
classDiagram
    class ActivityManagementViewModel {
        -string _selectedActivityType
        -bool _activityIdFormatEnabled
        -ActivityModel _mainCommercialActivity
        -ActivityModel _secondaryCommercialActivity
        -ActivityModel _craftActivity
        -ActivityModel _professionalActivity
        -FileCheckStatesModel _mainCommercialFileCheckStates
        -FileCheckStatesModel _secondaryCommercialFileCheckStates
        -FileCheckStatesModel _craftFileCheckStates
        -FileCheckStatesModel _professionalFileCheckStates
        -List~ActivityTypeBaseModel~ _mainCommercialActivities
        -List~ActivityTypeBaseModel~ _secondaryCommercialActivities
        -List~CpiWilaya~ _cpiWilayas
        -ObservableCollection~CpiDaira~ _cpiDairas
        -CpiWilaya _selectedCpiWilaya
        -CpiDaira _selectedCpiDaira
        -List~int~ _mainCommercialG12SelectedYears
        -List~int~ _mainCommercialBISSelectedYears
        -List~int~ _secondaryCommercialG12SelectedYears
        -List~int~ _secondaryCommercialBISSelectedYears
        -List~int~ _craftG12SelectedYears
        -List~int~ _craftBISSelectedYears
        -List~int~ _professionalG12SelectedYears
        -List~int~ _professionalBISSelectedYears
        -string _mainCommercialG12DisplayText
        -string _mainCommercialBISDisplayText
        -string _secondaryCommercialG12DisplayText
        -string _secondaryCommercialBISDisplayText
        -string _craftG12DisplayText
        -string _craftBISDisplayText
        -string _professionalG12DisplayText
        -string _professionalBISDisplayText
        -ActivityTypeBaseService _activityTypeService
        -CraftTypeBaseService _craftTypeService
        -CpiLocationService _cpiLocationService
        
        +string SelectedActivityType
        +bool ActivityIdFormatEnabled
        +ActivityModel CurrentActivity
        +List~ActivityTypeBaseModel~ CurrentMultipleActivities
        +FileCheckStatesModel CurrentFileCheckStates
        +List~CpiWilaya~ CpiWilayas
        +ObservableCollection~CpiDaira~ CpiDairas
        +CpiWilaya SelectedCpiWilaya
        +CpiDaira SelectedCpiDaira
        +List~int~ G12SelectedYears
        +List~int~ BISSelectedYears
        +string G12DisplayText
        +string BISDisplayText
        +ICommand AddActivityCommand
        +ICommand EditStatusCommand
        +ICommand SelectG12YearsCommand
        +ICommand SelectBISYearsCommand
        +event Action EditStatusRequested
        
        +bool IsValid()
        +void Clear()
        -void EnsureDefaultActivityStatus(string)
        -void SynchronizeCpiLocationSelections()
        -Task LoadCpiLocationDataAsync()
        -Task UpdateCpiDairasForSelectedWilayaAsync()
        -void UpdateG12DisplayText()
        -void UpdateBISDisplayText()
        -void AddActivity()
        -void EditStatus()
        -void SelectG12Years()
        -void SelectBISYears()
        +void Dispose(bool)
    }
    
    class BaseViewModel {
        <<abstract>>
        +PropertyPriority enum
        +UIState enum
        +BatchingStrategy enum
        #void SetProperty(ref T, T, string, PropertyPriority)
        #virtual void OnPropertyChanged(string, PropertyPriority)
        +abstract void Dispose(bool)
    }
    
    class ActivityModel {
        +string ActivityStatus
        +string ActivityStartDate
        +string CommercialRegister
        +string NifNumber
        +string NisNumber
        +string ArtNumber
        +string CpiWilaya
        +string CpiDaira
    }
    
    class FileCheckStatesModel {
        +bool CasFileCheck
        +bool NifFileCheck
        +bool NisFileCheck
        +bool RcFileCheck
        +bool DexFileCheck
        +bool ArtFileCheck
        +bool AgrFileCheck
    }
    
    class ActivityTypeBaseModel {
        +string ActivityCode
        +string ActivityNameAr
        +string ActivityNameFr
    }
    
    class CpiWilaya {
        +string Code
        +string NameAr
        +string NameFr
    }
    
    class CpiDaira {
        +string Code
        +string NameAr
        +string NameFr
        +string WilayaCode
    }
    
    class RelayCommand {
        +bool CanExecute(object)
        +void Execute(object)
        +event EventHandler CanExecuteChanged
    }
    
    class ActivityTypeBaseService {
        +Task~List~ActivityTypeBaseModel~~ GetActivitiesByTypeAsync(string)
        +Task~ActivityTypeBaseModel~ GetActivityByCodeAsync(string)
    }
    
    class CraftTypeBaseService {
        +Task~List~CraftTypeBaseModel~~ GetAllCraftTypesAsync()
        +Task~CraftTypeBaseModel~ GetCraftTypeByCodeAsync(string)
    }
    
    class CpiLocationService {
        +Task~List~CpiWilaya~~ GetAllWilayasAsync()
        +Task~List~CpiDaira~~ GetDairasByWilayaCodeAsync(string)
    }
    
    class LoggingService {
        <<static>>
        +void LogDebug(string, string)
        +void LogInfo(string, string)
        +void LogWarning(string, string)
        +void LogError(string, string)
    }
    
    class ErrorManager {
        <<static>>
        +void HandleErrorToast(Exception, string, string, LogLevel, string)
    }
    
    ActivityManagementViewModel --|> BaseViewModel : inherits
    ActivityManagementViewModel --> ActivityModel : contains
    ActivityManagementViewModel --> FileCheckStatesModel : contains
    ActivityManagementViewModel --> ActivityTypeBaseModel : manages
    ActivityManagementViewModel --> CpiWilaya : uses
    ActivityManagementViewModel --> CpiDaira : uses
    ActivityManagementViewModel --> RelayCommand : contains
    ActivityManagementViewModel --> ActivityTypeBaseService : uses
    ActivityManagementViewModel --> CraftTypeBaseService : uses
    ActivityManagementViewModel --> CpiLocationService : uses
    ActivityManagementViewModel --> LoggingService : uses
    ActivityManagementViewModel --> ErrorManager : uses
    
    note for ActivityManagementViewModel "Extracted from NewClientViewModel\nfor better separation of concerns\nManages 4 activity types with\ntab-based data persistence"
    note for BaseViewModel "Provides smart property batching\nand optimized UI performance"
```

## Data Flow Diagram

```mermaid
flowchart TD
    A[User Selects Activity Type] --> B[SelectedActivityType Property]
    B --> C{Activity Type Switch}
    C -->|MainCommercial| D[Load Main Commercial Data]
    C -->|SecondaryCommercial| E[Load Secondary Commercial Data]
    C -->|Craft| F[Load Craft Data]
    C -->|Professional| G[Load Professional Data]
    
    D --> H[Update CurrentActivity Property]
    E --> H
    F --> H
    G --> H
    
    H --> I[Update CurrentFileCheckStates]
    H --> J[Update Payment Years Properties]
    H --> K[Synchronize CPI Location]
    
    L[User Selects CPI Wilaya] --> M[SelectedCpiWilaya Property]
    M --> N[Update CurrentActivity.CpiWilaya]
    M --> O[Load Dairas for Wilaya]
    O --> P[Update CpiDairas Collection]
    
    Q[User Selects CPI Daira] --> R[SelectedCpiDaira Property]
    R --> S[Validate Daira belongs to Wilaya]
    S -->|Valid| T[Update CurrentActivity.CpiDaira]
    S -->|Invalid| U[Clear Selection & Log Warning]
    
    V[User Configures Payment Years] --> W[G12SelectedYears/BISSelectedYears]
    W --> X[Update Tab-Specific Collections]
    X --> Y[Update Display Text]
    Y --> Z[Notify UI Property Changed]
    
    AA[User Executes Commands] --> BB{Command Type}
    BB -->|AddActivity| CC[AddActivity Method]
    BB -->|EditStatus| DD[EditStatus Method]
    BB -->|SelectG12Years| EE[SelectG12Years Method]
    BB -->|SelectBISYears| FF[SelectBISYears Method]
    
    DD --> GG[Raise EditStatusRequested Event]
    GG --> HH[View Handles Event]
    HH --> II[Open Status Update Dialog]
    
    JJ[Initialize ViewModel] --> KK[Load Services from ServiceLocator]
    KK --> LL[Load CPI Location Data Async]
    LL --> MM[Set Default Activity Status]
    MM --> NN[Ready for User Interaction]
    
    style B fill:#e1f5fe
    style H fill:#f3e5f5
    style M fill:#e8f5e8
    style W fill:#fff3e0
    style GG fill:#ffebee
```

## State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> Initialized : Constructor
    
    Initialized --> MainCommercial : SelectedActivityType = "MainCommercial"
    Initialized --> SecondaryCommercial : SelectedActivityType = "SecondaryCommercial"
    Initialized --> Craft : SelectedActivityType = "Craft"
    Initialized --> Professional : SelectedActivityType = "Professional"
    
    MainCommercial --> SecondaryCommercial : Tab Switch
    MainCommercial --> Craft : Tab Switch
    MainCommercial --> Professional : Tab Switch
    
    SecondaryCommercial --> MainCommercial : Tab Switch
    SecondaryCommercial --> Craft : Tab Switch
    SecondaryCommercial --> Professional : Tab Switch
    
    Craft --> MainCommercial : Tab Switch
    Craft --> SecondaryCommercial : Tab Switch
    Craft --> Professional : Tab Switch
    
    Professional --> MainCommercial : Tab Switch
    Professional --> SecondaryCommercial : Tab Switch
    Professional --> Craft : Tab Switch
    
    MainCommercial --> DataEntry : User Input
    SecondaryCommercial --> DataEntry : User Input
    Craft --> DataEntry : User Input
    Professional --> DataEntry : User Input
    
    DataEntry --> CpiLocationSelection : Select Wilaya/Daira
    CpiLocationSelection --> PaymentYearSelection : Configure Years
    PaymentYearSelection --> FileCheckConfiguration : Set File States
    FileCheckConfiguration --> Validation : IsValid() called
    
    Validation --> Valid : All data valid
    Validation --> Invalid : Missing/invalid data
    
    Invalid --> DataEntry : User corrects data
    Valid --> Saving : Save operation
    
    Saving --> Saved : Success
    Saving --> Error : Exception occurred
    Error --> DataEntry : Retry after error
    
    DataEntry --> CommandExecution : User clicks buttons
    CommandExecution --> DialogOpen : AddActivity/EditStatus/SelectYears
    DialogOpen --> DataEntry : Dialog closed
    
    Saved --> Cleared : Clear() called
    DataEntry --> Cleared : Clear() called
    Cleared --> Initialized : Ready for new data
    
    Valid --> Disposed : Dispose() called
    DataEntry --> Disposed : Dispose() called
    Disposed --> [*]
```

## Integration Examples

### Integration with NewClientViewModel

```csharp
public class NewClientViewModel : BaseViewModel
{
    private ActivityManagementViewModel _activityManagement;
    
    public ActivityManagementViewModel ActivityManagement
    {
        get => _activityManagement;
        set => SetProperty(ref _activityManagement, value);
    }
    
    public NewClientViewModel()
    {
        ActivityManagement = new ActivityManagementViewModel();
        
        // Subscribe to activity management events
        ActivityManagement.EditStatusRequested += OnActivityEditStatusRequested;
        ActivityManagement.PropertyChanged += OnActivityManagementPropertyChanged;
    }
    
    private void OnActivityEditStatusRequested()
    {
        // Handle edit status request - open dialog
        var dialog = new ActivityStatusUpdateDialog(ActivityManagement.CurrentActivity);
        if (dialog.ShowDialog() == true)
        {
            // Status was updated, refresh UI
            OnPropertyChanged(nameof(ActivityManagement));
        }
    }
    
    private void OnActivityManagementPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        // React to activity management changes
        switch (e.PropertyName)
        {
            case nameof(ActivityManagement.SelectedActivityType):
                ValidateClientData();
                UpdateFileCheckRequirements();
                break;
                
            case nameof(ActivityManagement.CurrentActivity):
                ValidateActivityData();
                break;
        }
    }
    
    private bool ValidateClientData()
    {
        return ActivityManagement.IsValid() && ValidateOtherSections();
    }
}
```

### Integration with Client Database Operations

```csharp
public class ClientCreationService
{
    public async Task<string> CreateClientWithActivitiesAsync(
        PersonalInformationViewModel personalInfo,
        ActivityManagementViewModel activityManagement,
        ContactInformationViewModel contactInfo)
    {
        // Validate all components
        if (!personalInfo.IsValid() || !activityManagement.IsValid() || !contactInfo.IsValid())
        {
            throw new ValidationException("Client data validation failed");
        }
        
        // Create client data transfer object
        var clientData = new ClientCreationData
        {
            NameFr = personalInfo.NameFr,
            NameAr = personalInfo.NameAr,
            BirthDate = personalInfo.BirthDate,
            BirthPlace = personalInfo.BirthPlace,
            Gender = personalInfo.Gender,
            Address = personalInfo.Address,
            NationalId = personalInfo.NationalId
        };
        
        // Use UFU2's database service
        var databaseService = ServiceLocator.GetService<ClientDatabaseService>();
        var clientUid = await databaseService.CreateClientAsync(clientData);
        
        // Create activity data
        var activityData = new ActivityCreationData
        {
            ClientUid = clientUid,
            ActivityType = activityManagement.SelectedActivityType,
            ActivityStatus = activityManagement.CurrentActivity.ActivityStatus,
            ActivityStartDate = activityManagement.CurrentActivity.ActivityStartDate,
            CommercialRegister = activityManagement.CurrentActivity.CommercialRegister,
            NifNumber = activityManagement.CurrentActivity.NifNumber,
            NisNumber = activityManagement.CurrentActivity.NisNumber,
            ArtNumber = activityManagement.CurrentActivity.ArtNumber,
            CpiWilaya = activityManagement.CurrentActivity.CpiWilaya,
            CpiDaira = activityManagement.CurrentActivity.CpiDaira
        };
        
        // Save activity
        var activityUid = await databaseService.CreateActivityAsync(activityData);
        
        // Save file check states
        await SaveFileCheckStatesAsync(activityUid, activityManagement.CurrentFileCheckStates);
        
        // Save payment years
        await SavePaymentYearsAsync(activityUid, activityManagement.G12SelectedYears, activityManagement.BISSelectedYears);
        
        // Save phone numbers
        await SavePhoneNumbersAsync(clientUid, contactInfo.PhoneNumbers);
        
        return clientUid;
    }
    
    private async Task SaveFileCheckStatesAsync(string activityUid, FileCheckStatesModel fileCheckStates)
    {
        var fileCheckData = new FileCheckStatesCreationData
        {
            ActivityUid = activityUid,
            CasFileCheck = fileCheckStates.CasFileCheck,
            NifFileCheck = fileCheckStates.NifFileCheck,
            NisFileCheck = fileCheckStates.NisFileCheck,
            RcFileCheck = fileCheckStates.RcFileCheck,
            DexFileCheck = fileCheckStates.DexFileCheck,
            ArtFileCheck = fileCheckStates.ArtFileCheck,
            AgrFileCheck = fileCheckStates.AgrFileCheck
        };
        
        var databaseService = ServiceLocator.GetService<ClientDatabaseService>();
        await databaseService.CreateFileCheckStatesAsync(fileCheckData);
    }
    
    private async Task SavePaymentYearsAsync(string activityUid, List<int> g12Years, List<int> bisYears)
    {
        var databaseService = ServiceLocator.GetService<ClientDatabaseService>();
        
        // Save G12 payment years
        foreach (var year in g12Years)
        {
            var g12Data = new G12CheckPaymentYearCreationData
            {
                ActivityUid = activityUid,
                PaymentYear = year
            };
            await databaseService.CreateG12CheckPaymentYearAsync(g12Data);
        }
        
        // Save BIS payment years
        foreach (var year in bisYears)
        {
            var bisData = new BisCheckPaymentYearCreationData
            {
                ActivityUid = activityUid,
                PaymentYear = year
            };
            await databaseService.CreateBisCheckPaymentYearAsync(bisData);
        }
    }
}
```

This documentation provides comprehensive coverage of the ActivityManagementViewModel component, demonstrating its integration with UFU2's architecture, performance considerations, and practical usage patterns with proper error handling and Arabic localization support.