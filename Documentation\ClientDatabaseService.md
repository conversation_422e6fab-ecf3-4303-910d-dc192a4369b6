# ClientDatabaseService Documentation

## Overview

The `ClientDatabaseService` is a comprehensive data access service that handles all CRUD operations for client management in UFU2. It manages clients, activities, phone numbers, file check states, notes, and payment year tracking with proper UID generation and relationship handling.

## What the Code Does

The service provides complete database operations for:
- **Client Management**: Create, read, update, delete client records
- **Activity Management**: Handle business activities with proper UID generation
- **Phone Number Management**: Support multiple phone numbers with primary phone validation
- **File Check States**: Manage document verification states with activity type-specific rules
- **Notes Management**: Handle client notes with flag types and priorities
- **Payment Year Tracking**: Manage G12Check and BisCheck payment years

All operations are wrapped in transactions for data integrity and include comprehensive error handling.

## How It's Used

### Basic Client Operations

```csharp
// Get service from ServiceLocator
var clientService = ServiceLocator.GetService<ClientDatabaseService>();

// Create a new client
var clientData = new ClientCreationData
{
    FirstName = "أحمد",
    LastName = "محمد",
    Gender = "Male",
    BirthDate = DateTime.Now.AddYears(-30),
    // UID will be generated automatically
};

string clientId = await clientService.CreateClientAsync(clientData);

// Retrieve client by ID
var client = await clientService.GetClientByIdAsync(clientId);

// Update client information
client.FirstName = "أحمد المحدث";
await clientService.UpdateClientAsync(client);
```

### Activity Management

```csharp
// Add activity to client
var activityData = new ActivityCreationData
{
    ClientId = clientId,
    ActivityType = "MainCommercial",
    ActivityCode = "47.11.10",
    Description = "تجارة التجزئة"
};

string activityId = await clientService.AddActivityAsync(activityData);

// Get all activities for a client
var activities = await clientService.GetClientActivitiesAsync(clientId);
```

### Phone Number Management

```csharp
// Add phone number
var phoneData = new PhoneNumberData
{
    ClientId = clientId,
    PhoneNumber = "0555123456",
    PhoneType = "Mobile",
    IsPrimary = true
};

await clientService.AddPhoneNumberAsync(phoneData);

// Get all phone numbers for client
var phoneNumbers = await clientService.GetClientPhoneNumbersAsync(clientId);
```

### File Check State Management

```csharp
// Update file check states for an activity
var fileCheckStates = new Dictionary<string, bool>
{
    ["CAS"] = true,
    ["NIF"] = true,
    ["NIS"] = false,
    ["RC"] = true,  // Required for MainCommercial
    ["DEX"] = false
};

await clientService.UpdateFileCheckStatesAsync(activityId, fileCheckStates);
```

### Advanced Usage with Transaction Management

```csharp
try
{
    var clientService = ServiceLocator.GetService<ClientDatabaseService>();
    
    // Create client with complete data in a single transaction
    var clientData = new ClientCreationData
    {
        FirstName = "فاطمة",
        LastName = "أحمد",
        Gender = "Female",
        Activities = new List<ActivityCreationData>
        {
            new ActivityCreationData
            {
                ActivityType = "Craft",
                ActivityCode = "13.10.00",
                Description = "صناعة النسيج"
            }
        },
        PhoneNumbers = new List<PhoneNumberData>
        {
            new PhoneNumberData
            {
                PhoneNumber = "0661234567",
                PhoneType = "Mobile",
                IsPrimary = true
            }
        }
    };
    
    string clientId = await clientService.CreateClientWithDetailsAsync(clientData);
    
    LoggingService.LogInformation($"Client created successfully: {clientId}");
}
catch (Exception ex)
{
    ErrorManager.HandleError(ex, "خطأ في إنشاء بيانات العميل");
}
```

## Integration with UFU2 Architecture

The service follows UFU2's established architectural patterns:

- **ServiceLocator Pattern**: Registered and accessed through dependency injection
- **MVVM Integration**: Designed to work seamlessly with ViewModels
- **Error Handling**: Uses `ErrorManager` for consistent Arabic error messages
- **Logging**: Integrates with `LoggingService` for operation tracking
- **Database Access**: Built on top of `DatabaseService` with Dapper ORM
- **UID Generation**: Integrates with `UIDGenerationService` for unique identifiers
- **Validation**: Works with `ClientValidationService` for business rule enforcement

## Performance Considerations

- **Async Operations**: All database operations use async/await patterns
- **Transaction Management**: Multi-step operations wrapped in database transactions
- **Connection Pooling**: Leverages DatabaseService connection management
- **Batch Operations**: Supports bulk operations for improved performance
- **Lazy Loading**: Implements efficient data loading strategies
- **Index Utilization**: Designed to work with database indexes for optimal query performance

## Mermaid Diagram

```mermaid
graph TB
    subgraph "UFU2 Architecture"
        VM[ViewModel] --> SL[ServiceLocator]
        SL --> CDS[ClientDatabaseService]
    end
    
    subgraph "ClientDatabaseService Dependencies"
        CDS --> DS[DatabaseService]
        CDS --> UGS[UIDGenerationService]
        CDS --> CVS[ClientValidationService]
        CDS --> EM[ErrorManager]
        CDS --> LS[LoggingService]
    end
    
    subgraph "Database Operations"
        DS --> SQLite[(SQLite Database)]
        SQLite --> Tables[("clients<br/>activities<br/>phone_numbers<br/>file_check_states<br/>notes<br/>payment_years")]
    end
    
    subgraph "Data Flow"
        CDS --> Create[Create Operations]
        CDS --> Read[Read Operations]
        CDS --> Update[Update Operations]
        CDS --> Delete[Delete Operations]
        
        Create --> TX1[Transaction Wrapper]
        Update --> TX2[Transaction Wrapper]
        Delete --> TX3[Transaction Wrapper]
    end
    
    style CDS fill:#e1f5fe
    style SQLite fill:#f3e5f5
    style VM fill:#e8f5e8
```

## Service Operation Flow

```mermaid
sequenceDiagram
    participant VM as ViewModel
    participant CDS as ClientDatabaseService
    participant UGS as UIDGenerationService
    participant CVS as ClientValidationService
    participant DS as DatabaseService
    participant DB as SQLite Database

    VM->>CDS: CreateClientAsync(clientData)
    CDS->>CVS: ValidateClientData(clientData)
    CVS->>CDS: ValidationResult
    
    alt Validation Successful
        CDS->>UGS: GenerateClientUIDAsync()
        UGS->>CDS: Return Client UID
        CDS->>DS: BeginTransactionAsync()
        CDS->>DS: ExecuteAsync(INSERT client)
        DS->>DB: Insert client record
        
        loop For each activity
            CDS->>UGS: GenerateActivityUIDAsync(clientUID)
            UGS->>CDS: Return Activity UID
            CDS->>DS: ExecuteAsync(INSERT activity)
            DS->>DB: Insert activity record
        end
        
        loop For each phone number
            CDS->>DS: ExecuteAsync(INSERT phone_number)
            DS->>DB: Insert phone record
        end
        
        CDS->>DS: CommitTransactionAsync()
        CDS->>VM: Return Client ID
    else Validation Failed
        CDS->>VM: Throw ValidationException
    end
```