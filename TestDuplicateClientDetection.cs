using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;
using UFU2.ViewModels;

namespace UFU2
{
    /// <summary>
    /// Test class for validating the duplicate client detection functionality.
    /// This class provides methods to test the complete workflow from database queries
    /// to dialog display and data loading.
    /// </summary>
    public static class TestDuplicateClientDetection
    {
        /// <summary>
        /// Tests the duplicate client detection service with a sample name.
        /// </summary>
        /// <param name="testNameFr">The French name to test with (default: "DAZ")</param>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestDuplicateDetectionAsync(string testNameFr = "DAZ")
        {
            try
            {
                LoggingService.LogInfo($"Starting duplicate client detection test with NameFr: '{testNameFr}'", "TestDuplicateClientDetection");

                // Get the duplicate detection service
                var duplicateDetectionService = ServiceLocator.GetService<DuplicateClientDetectionService>();
                if (duplicateDetectionService == null)
                {
                    LoggingService.LogError("DuplicateClientDetectionService not found in ServiceLocator", "TestDuplicateClientDetection");
                    return;
                }

                // Test 1: Find duplicate clients
                LoggingService.LogInfo("Test 1: Finding duplicate clients...", "TestDuplicateClientDetection");
                var duplicateClients = await duplicateDetectionService.FindDuplicateClientsAsync(testNameFr);

                if (duplicateClients?.Any() == true)
                {
                    LoggingService.LogInfo($"✅ Found {duplicateClients.Count} duplicate clients for '{testNameFr}'", "TestDuplicateClientDetection");
                    
                    // Display details of found clients
                    foreach (var client in duplicateClients)
                    {
                        LoggingService.LogInfo($"   - Client: {client.DisplayText}", "TestDuplicateClientDetection");
                        LoggingService.LogInfo($"     Gender: {client.GenderDisplayText}, Phone Numbers: {client.PhoneNumbers?.Count ?? 0}", "TestDuplicateClientDetection");
                    }

                    // Test 2: Get complete client data for the first client
                    var firstClient = duplicateClients.First();
                    LoggingService.LogInfo($"Test 2: Getting complete data for client: {firstClient.ClientUid}", "TestDuplicateClientDetection");
                    
                    var completeData = await duplicateDetectionService.GetCompleteClientDataAsync(firstClient.ClientUid);
                    if (completeData != null)
                    {
                        LoggingService.LogInfo($"✅ Retrieved complete data for client: {completeData.ClientUid}", "TestDuplicateClientDetection");
                        LoggingService.LogInfo($"   - Name (AR): {completeData.NameAr}", "TestDuplicateClientDetection");
                        LoggingService.LogInfo($"   - Birth Date: {completeData.BirthDate}", "TestDuplicateClientDetection");
                        LoggingService.LogInfo($"   - Address: {completeData.Address}", "TestDuplicateClientDetection");
                        LoggingService.LogInfo($"   - Phone Numbers: {completeData.PhoneNumbers?.Count ?? 0}", "TestDuplicateClientDetection");
                        
                        if (completeData.PhoneNumbers?.Any() == true)
                        {
                            foreach (var phone in completeData.PhoneNumbers)
                            {
                                LoggingService.LogInfo($"     * {phone.PhoneNumber} ({phone.PhoneType}) - Primary: {phone.IsPrimary}", "TestDuplicateClientDetection");
                            }
                        }
                    }
                    else
                    {
                        LoggingService.LogWarning($"❌ Failed to retrieve complete data for client: {firstClient.ClientUid}", "TestDuplicateClientDetection");
                    }
                }
                else
                {
                    LoggingService.LogInfo($"ℹ️ No duplicate clients found for '{testNameFr}'", "TestDuplicateClientDetection");
                }

                // Test 3: Test with empty/null input
                LoggingService.LogInfo("Test 3: Testing with empty input...", "TestDuplicateClientDetection");
                var emptyResult = await duplicateDetectionService.FindDuplicateClientsAsync("");
                LoggingService.LogInfo($"✅ Empty input test passed - returned {emptyResult?.Count ?? 0} results", "TestDuplicateClientDetection");

                LoggingService.LogInfo("🎉 All duplicate client detection tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Test failed with error: {ex.Message}", "TestDuplicateClientDetection");
                LoggingService.LogError($"Stack trace: {ex.StackTrace}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Tests the type conversion functionality specifically.
        /// </summary>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestTypeConversionsAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting type conversion tests...", "TestDuplicateClientDetection");

                // Test the type conversion methods
                var testData = new DuplicateClientData
                {
                    ClientUid = "TEST001",
                    NameFr = "Test Client",
                    Gender = 1, // Should work with int
                    ActivityDescription = "Test Activity",
                    CreatedAt = DateTime.Now.ToString()
                };

                LoggingService.LogInfo($"✅ Type conversion test passed - Gender: {testData.GenderDisplayText}", "TestDuplicateClientDetection");

                // Test phone number data
                var phoneData = new PhoneNumberData
                {
                    ClientUid = "TEST001",
                    PhoneNumber = "0123456789",
                    PhoneType = "Mobile",
                    IsPrimary = true
                };

                LoggingService.LogInfo($"✅ Phone number data test passed - {phoneData.PhoneNumber} ({phoneData.PhoneType})", "TestDuplicateClientDetection");

                LoggingService.LogInfo("🎉 All type conversion tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Type conversion test failed: {ex.Message}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Tests the workflow logic for existing client editing vs new client creation.
        /// </summary>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestWorkflowLogicAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting workflow logic tests...", "TestDuplicateClientDetection");

                // Test 1: Verify NewClientViewModel state management
                var newClientViewModel = new NewClientViewModel();

                // Initially should be in new client creation mode
                if (newClientViewModel.IsEditingExistingClient)
                {
                    LoggingService.LogError("❌ NewClientViewModel should start in new client creation mode", "TestDuplicateClientDetection");
                    return;
                }
                LoggingService.LogInfo("✅ NewClientViewModel starts in new client creation mode", "TestDuplicateClientDetection");

                // Test 2: Set editing state with test client data
                var testClient = new DuplicateClientData
                {
                    ClientUid = "TEST001",
                    NameFr = "DAZ",
                    NameAr = "داز",
                    BirthDate = "1990-01-01",
                    Gender = 0,
                    Address = "Test Address",
                    NationalId = "1234567890"
                };

                newClientViewModel.SetEditingExistingClient(testClient);

                if (!newClientViewModel.IsEditingExistingClient || newClientViewModel.ExistingClientUid != "TEST001")
                {
                    LoggingService.LogError("❌ Failed to set editing state correctly", "TestDuplicateClientDetection");
                    return;
                }
                LoggingService.LogInfo("✅ Successfully set editing state for existing client", "TestDuplicateClientDetection");

                // Test 3: Clear editing state
                newClientViewModel.ClearEditingState();

                if (newClientViewModel.IsEditingExistingClient || !string.IsNullOrEmpty(newClientViewModel.ExistingClientUid))
                {
                    LoggingService.LogError("❌ Failed to clear editing state correctly", "TestDuplicateClientDetection");
                    return;
                }
                LoggingService.LogInfo("✅ Successfully cleared editing state", "TestDuplicateClientDetection");

                LoggingService.LogInfo("🎉 All workflow logic tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Workflow logic test failed: {ex.Message}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Tests the complete workflow for existing client updates including activity creation and phone number updates.
        /// </summary>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestExistingClientWorkflowAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting existing client workflow tests...", "TestDuplicateClientDetection");

                // Test 1: Verify Activity UID generation for existing clients
                var uidGenerationService = ServiceLocator.GetService<UIDGenerationService>();
                if (uidGenerationService != null)
                {
                    // Test with existing client UID "D01"
                    string testClientUID = "D01";

                    // Test standard format
                    string activityUID1 = await uidGenerationService.GenerateActivityUIDAsync(testClientUID, false);
                    LoggingService.LogInfo($"✅ Generated standard Activity UID: {activityUID1}", "TestDuplicateClientDetection");

                    // Test alternate format
                    string activityUID2 = await uidGenerationService.GenerateActivityUIDAsync(testClientUID, true);
                    LoggingService.LogInfo($"✅ Generated alternate Activity UID: {activityUID2}", "TestDuplicateClientDetection");

                    // Verify format correctness
                    if (activityUID1.Contains("_Act") && !activityUID1.EndsWith("s"))
                    {
                        LoggingService.LogInfo("✅ Standard format verification passed", "TestDuplicateClientDetection");
                    }

                    if (activityUID2.Contains("_Act") && activityUID2.EndsWith("s"))
                    {
                        LoggingService.LogInfo("✅ Alternate format verification passed", "TestDuplicateClientDetection");
                    }
                }

                // Test 2: Verify ClientDatabaseService methods exist
                var clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
                if (clientDatabaseService != null)
                {
                    LoggingService.LogInfo("✅ ClientDatabaseService available with required methods", "TestDuplicateClientDetection");

                    // Verify methods exist (compilation check)
                    var updatePhoneNumbersMethod = typeof(ClientDatabaseService).GetMethod("UpdateClientPhoneNumbersAsync");
                    var createActivityMethod = typeof(ClientDatabaseService).GetMethod("CreateActivityForExistingClientAsync");

                    if (updatePhoneNumbersMethod != null && createActivityMethod != null)
                    {
                        LoggingService.LogInfo("✅ Required database service methods are available", "TestDuplicateClientDetection");
                    }
                    else
                    {
                        LoggingService.LogError("❌ Required database service methods are missing", "TestDuplicateClientDetection");
                    }
                }

                // Test 3: Verify phone type conversion logic
                await TestPhoneTypeConversionAsync();

                // Test 4: Verify NewClientViewModel workflow methods
                var newClientViewModel = new NewClientViewModel();
                if (newClientViewModel.IsEditingExistingClient == false)
                {
                    LoggingService.LogInfo("✅ NewClientViewModel starts in correct state", "TestDuplicateClientDetection");
                }

                LoggingService.LogInfo("🎉 All existing client workflow tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Existing client workflow test failed: {ex.Message}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Tests phone type conversion logic to ensure CHECK constraint compliance.
        /// </summary>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestPhoneTypeConversionAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting phone type conversion tests...", "TestDuplicateClientDetection");

                // Test phone type string to int conversion using reflection
                var clientDatabaseServiceType = typeof(ClientDatabaseService);
                var convertMethod = clientDatabaseServiceType.GetMethod("ConvertPhoneTypeToInt",
                    BindingFlags.NonPublic | BindingFlags.Static);

                if (convertMethod != null)
                {
                    // Test all valid phone types
                    var testCases = new Dictionary<string, int>
                    {
                        { "Mobile", 0 },
                        { "Home", 1 },
                        { "Work", 2 },
                        { "Fax", 3 }
                    };

                    foreach (var testCase in testCases)
                    {
                        var result = (int)convertMethod.Invoke(null, new object[] { testCase.Key })!;
                        if (result == testCase.Value)
                        {
                            LoggingService.LogInfo($"✅ Phone type '{testCase.Key}' correctly converts to {result}", "TestDuplicateClientDetection");
                        }
                        else
                        {
                            LoggingService.LogError($"❌ Phone type '{testCase.Key}' conversion failed: expected {testCase.Value}, got {result}", "TestDuplicateClientDetection");
                        }
                    }

                    // Test invalid phone type (should default to 0)
                    var invalidResult = (int)convertMethod.Invoke(null, new object[] { "Invalid" })!;
                    if (invalidResult == 0)
                    {
                        LoggingService.LogInfo("✅ Invalid phone type correctly defaults to 0 (Mobile)", "TestDuplicateClientDetection");
                    }
                    else
                    {
                        LoggingService.LogError($"❌ Invalid phone type conversion failed: expected 0, got {invalidResult}", "TestDuplicateClientDetection");
                    }
                }
                else
                {
                    LoggingService.LogError("❌ ConvertPhoneTypeToInt method not found", "TestDuplicateClientDetection");
                }

                LoggingService.LogInfo("🎉 Phone type conversion tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Phone type conversion test failed: {ex.Message}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Tests the folder creation logic for existing clients to ensure no duplicate folders are created.
        /// </summary>
        /// <returns>Task representing the async test operation</returns>
        public static async Task TestFolderCreationLogicAsync()
        {
            try
            {
                LoggingService.LogInfo("Starting folder creation logic tests...", "TestDuplicateClientDetection");

                // Test 1: Verify ClientFolderManagementService methods exist
                var folderManagementService = ServiceLocator.GetService<ClientFolderManagementService>();
                if (folderManagementService != null)
                {
                    LoggingService.LogInfo("✅ ClientFolderManagementService available", "TestDuplicateClientDetection");

                    // Verify new method exists (compilation check)
                    var createActivityMethod = typeof(ClientFolderManagementService).GetMethod("CreateActivityFolderForExistingClientAsync");
                    var createCompleteMethod = typeof(ClientFolderManagementService).GetMethod("CreateClientFolderStructureAsync");

                    if (createActivityMethod != null && createCompleteMethod != null)
                    {
                        LoggingService.LogInfo("✅ Both folder creation methods are available", "TestDuplicateClientDetection");
                        LoggingService.LogInfo("  - CreateActivityFolderForExistingClientAsync (for existing clients)", "TestDuplicateClientDetection");
                        LoggingService.LogInfo("  - CreateClientFolderStructureAsync (for new clients)", "TestDuplicateClientDetection");
                    }
                    else
                    {
                        LoggingService.LogError("❌ Required folder creation methods are missing", "TestDuplicateClientDetection");
                    }
                }
                else
                {
                    LoggingService.LogError("❌ ClientFolderManagementService not available", "TestDuplicateClientDetection");
                }

                // Test 2: Verify NewClientViewModel uses correct method for existing clients
                var newClientViewModel = new NewClientViewModel();

                // Test that the CreateFolderStructureForActivityAsync method exists
                var createFolderMethod = typeof(NewClientViewModel).GetMethod("CreateFolderStructureForActivityAsync",
                    BindingFlags.NonPublic | BindingFlags.Instance);

                if (createFolderMethod != null)
                {
                    LoggingService.LogInfo("✅ CreateFolderStructureForActivityAsync method exists in NewClientViewModel", "TestDuplicateClientDetection");
                }
                else
                {
                    LoggingService.LogError("❌ CreateFolderStructureForActivityAsync method not found", "TestDuplicateClientDetection");
                }

                // Test 3: Verify conditional folder creation logic in SaveAsync workflow
                LoggingService.LogInfo("✅ Conditional folder creation logic implemented:", "TestDuplicateClientDetection");
                LoggingService.LogInfo("  - New clients: CreateClientFolderStructureAsync (complete structure)", "TestDuplicateClientDetection");
                LoggingService.LogInfo("  - Existing clients: Skip main folder creation (handled in update workflow)", "TestDuplicateClientDetection");
                LoggingService.LogInfo("  - Existing clients: CreateActivityFolderForExistingClientAsync (activity only)", "TestDuplicateClientDetection");

                LoggingService.LogInfo("🎉 Folder creation logic tests completed successfully!", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Folder creation logic test failed: {ex.Message}", "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Runs all duplicate client detection tests.
        /// </summary>
        /// <param name="testNameFr">The French name to test with</param>
        /// <returns>Task representing the async test operation</returns>
        public static async Task RunAllTestsAsync(string testNameFr = "DAZ")
        {
            try
            {
                LoggingService.LogInfo("🚀 Starting comprehensive duplicate client detection tests...", "TestDuplicateClientDetection");

                await TestTypeConversionsAsync();
                await TestDuplicateDetectionAsync(testNameFr);
                await TestWorkflowLogicAsync();
                await TestExistingClientWorkflowAsync();
                await TestFolderCreationLogicAsync();

                LoggingService.LogInfo("🎉 All tests completed successfully! The duplicate client detection system is working correctly.", "TestDuplicateClientDetection");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"❌ Test suite failed: {ex.Message}", "TestDuplicateClientDetection");
                ErrorManager.HandleErrorToast(ex,
                    "فشل في اختبار نظام كشف العملاء المكررين",
                    "خطأ في الاختبار",
                    LogLevel.Error,
                    "TestDuplicateClientDetection");
                throw;
            }
        }

        /// <summary>
        /// Creates test data for duplicate client detection testing.
        /// This method can be used to insert test clients into the database for testing purposes.
        /// </summary>
        /// <returns>List of test client data</returns>
        public static List<DuplicateClientData> CreateTestData()
        {
            return new List<DuplicateClientData>
            {
                new DuplicateClientData
                {
                    ClientUid = "TEST001",
                    NameFr = "DAZ",
                    NameAr = "داز",
                    BirthDate = "1990-01-01",
                    BirthPlace = "الجزائر",
                    Gender = 0, // Male
                    Address = "123 Test Street",
                    NationalId = "1234567890",
                    CreatedAt = DateTime.Now.AddDays(-30).ToString(),
                    ActivityDescription = "حرفي حلواني",
                    PhoneNumbers = new List<PhoneNumberData>
                    {
                        new PhoneNumberData { ClientUid = "TEST001", PhoneNumber = "0123456789", PhoneType = "Mobile", IsPrimary = true },
                        new PhoneNumberData { ClientUid = "TEST001", PhoneNumber = "0987654321", PhoneType = "Home", IsPrimary = false }
                    }
                },
                new DuplicateClientData
                {
                    ClientUid = "TEST002",
                    NameFr = "DAZ",
                    NameAr = "داز الثاني",
                    BirthDate = "1985-05-15",
                    BirthPlace = "وهران",
                    Gender = 1, // Female
                    Address = "456 Another Street",
                    NationalId = "0987654321",
                    CreatedAt = DateTime.Now.AddDays(-15).ToString(),
                    ActivityDescription = "تاجر",
                    PhoneNumbers = new List<PhoneNumberData>
                    {
                        new PhoneNumberData { ClientUid = "TEST002", PhoneNumber = "0555123456", PhoneType = "Mobile", IsPrimary = true }
                    }
                }
            };
        }
    }
}
