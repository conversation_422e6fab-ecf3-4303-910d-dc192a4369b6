# NotesManagementViewModel Implementation Guide

## Overview

The `NotesManagementViewModel` is a specialized MVVM component extracted from the larger `NewClientViewModel` to handle client notes management in the UFU2 application. This component follows the single responsibility principle by focusing exclusively on managing client notes, including adding, editing, deleting, and displaying notes associated with a client. It provides comprehensive note management functionality with validation, search capabilities, and seamless integration with the UFU2 architecture.

## What the Code Does

The NotesManagementViewModel manages the following core functionalities:

### Core Notes Management
- **Notes Collection Management**: Handles a collection of notes associated with a client
- **Display Text Generation**: Provides formatted display text for UI components showing the most recent note
- **Note Validation**: Ensures all notes have valid content before allowing operations
- **Collection State Tracking**: Monitors note count and existence for UI binding

### Note Operations
- **Add Notes**: Creates new notes with content and priority settings
- **Update Notes**: Modifies existing note content and importance flags
- **Delete Notes**: Removes notes from the collection with proper cleanup
- **Search Functionality**: Provides content-based search across all notes

### UI Integration
- **Command Pattern**: Implements RelayCommand for all user interactions
- **Property Notifications**: Uses BaseViewModel's smart batching for optimal UI performance
- **Display Properties**: Provides computed properties for UI binding (NotesDisplayText, NotesCount, HasNotes)
- **Arabic Localization**: Supports Arabic text content and fallback messages

### Data Persistence
- **Collection Persistence**: Maintains notes data throughout the client creation process
- **Change Tracking**: Monitors collection changes and updates display properties automatically
- **Memory Management**: Proper resource disposal and cleanup

## How It's Used

### Basic Usage Pattern

```csharp
// Initialize the ViewModel
var notesVM = new NotesManagementViewModel();

// Add a basic note
notesVM.AddNoteWithContent("عميل جديد - تم التحقق من الوثائق", false);

// Add an important note
notesVM.AddNoteWithContent("يحتاج متابعة خاصة للملف التجاري", true);

// Check if notes exist
if (notesVM.HasNotes)
{
    Console.WriteLine($"Total notes: {notesVM.NotesCount}");
    Console.WriteLine($"Display text: {notesVM.NotesDisplayText}");
}

// Validate notes
if (notesVM.IsValid())
{
    LoggingService.LogInfo("Notes data is valid", "NotesManagement");
}
```

### Adding and Managing Notes

```csharp
// Add notes with different priorities
public void AddClientNotes(NotesManagementViewModel notesVM)
{
    try
    {
        // Add a regular note
        notesVM.AddNoteWithContent("تم استلام الوثائق الأساسية", false);
        
        // Add an important note that requires attention
        notesVM.AddNoteWithContent("نقص في وثيقة السجل التجاري - يحتاج متابعة", true);
        
        // Add a follow-up note
        notesVM.AddNoteWithContent("تم الاتصال بالعميل لطلب الوثائق المفقودة", false);
        
        // Verify notes were added
        Debug.Assert(notesVM.NotesCount == 3);
        Debug.Assert(notesVM.HasNotes == true);
        
        LoggingService.LogInfo($"Added {notesVM.NotesCount} notes for client", "NotesManagement");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error adding client notes: {ex.Message}", "NotesManagement");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء إضافة الملاحظات",
            "خطأ في الملاحظات",
            LogLevel.Error,
            "NotesManagement");
    }
}
```

### Updating and Editing Notes

```csharp
// Update existing notes
public void UpdateClientNotes(NotesManagementViewModel notesVM)
{
    try
    {
        // Get all notes sorted by date
        var notes = notesVM.GetNotesSortedByDate();
        
        if (notes.Any())
        {
            // Update the most recent note
            var latestNote = notes.First();
            notesVM.UpdateNote(latestNote, 
                "تم تحديث الملاحظة - استلام الوثائق المطلوبة", 
                false);
            
            LoggingService.LogInfo("Latest note updated successfully", "NotesManagement");
        }
        
        // Find and update a specific note by content
        var specificNote = notes.FirstOrDefault(n => 
            n.Content?.Contains("السجل التجاري") == true);
        
        if (specificNote != null)
        {
            notesVM.UpdateNote(specificNote,
                "تم استلام وثيقة السجل التجاري - مكتملة",
                false); // No longer important since resolved
            
            LoggingService.LogInfo("Specific note updated successfully", "NotesManagement");
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error updating notes: {ex.Message}", "NotesManagement");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء تحديث الملاحظات",
            "خطأ في التحديث",
            LogLevel.Error,
            "NotesManagement");
    }
}
```

### Searching and Filtering Notes

```csharp
// Search and filter notes
public void SearchClientNotes(NotesManagementViewModel notesVM)
{
    try
    {
        // Search for notes containing specific terms
        var documentNotes = notesVM.SearchNotes("وثائق");
        var followUpNotes = notesVM.SearchNotes("متابعة");
        var commercialNotes = notesVM.SearchNotes("تجاري");
        
        LoggingService.LogInfo($"Found {documentNotes.Count} notes about documents", "NotesManagement");
        LoggingService.LogInfo($"Found {followUpNotes.Count} notes requiring follow-up", "NotesManagement");
        LoggingService.LogInfo($"Found {commercialNotes.Count} notes about commercial activities", "NotesManagement");
        
        // Get all important notes
        var importantNotes = notesVM.GetImportantNotes();
        
        if (importantNotes.Any())
        {
            LoggingService.LogInfo($"Found {importantNotes.Count} important notes requiring attention", "NotesManagement");
            
            foreach (var note in importantNotes)
            {
                LoggingService.LogWarning($"Important note: {note.Content}", "NotesManagement");
            }
        }
        
        // Get all notes sorted by date for display
        var sortedNotes = notesVM.GetNotesSortedByDate();
        
        foreach (var note in sortedNotes.Take(5)) // Show latest 5 notes
        {
            var priority = note.IsImportant ? "مهم" : "عادي";
            LoggingService.LogInfo($"Note ({priority}): {note.Content}", "NotesManagement");
        }
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error searching notes: {ex.Message}", "NotesManagement");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء البحث في الملاحظات",
            "خطأ في البحث",
            LogLevel.Error,
            "NotesManagement");
    }
}
```

### Command Usage and Event Handling

```csharp
// Handle notes management commands
public class NotesManagementService
{
    private readonly NotesManagementViewModel _notesVM;
    
    public NotesManagementService()
    {
        _notesVM = new NotesManagementViewModel();
        
        // Subscribe to property changes
        _notesVM.PropertyChanged += OnNotesPropertyChanged;
    }
    
    public void ExecuteNotesCommands()
    {
        // Execute add note command
        if (_notesVM.AddNoteCommand.CanExecute(null))
        {
            _notesVM.AddNoteCommand.Execute(null);
        }
        
        // Execute manage notes command
        if (_notesVM.ManageNotesCommand.CanExecute(null))
        {
            _notesVM.ManageNotesCommand.Execute(null);
        }
        
        // Execute edit command (only if a note is selected)
        if (_notesVM.EditNoteCommand.CanExecute(null))
        {
            _notesVM.EditNoteCommand.Execute(null);
        }
        
        // Execute delete command (only if a note is selected)
        if (_notesVM.DeleteNoteCommand.CanExecute(null))
        {
            _notesVM.DeleteNoteCommand.Execute(null);
        }
    }
    
    private void OnNotesPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(NotesManagementViewModel.NotesDisplayText):
                // Update UI display when notes change
                UpdateNotesDisplay();
                break;
                
            case nameof(NotesManagementViewModel.NotesCount):
                // Update count indicators
                UpdateNotesCountDisplay();
                break;
                
            case nameof(NotesManagementViewModel.HasNotes):
                // Show/hide notes section based on existence
                UpdateNotesVisibility();
                break;
        }
    }
    
    private void UpdateNotesDisplay()
    {
        LoggingService.LogInfo($"Notes display updated: {_notesVM.NotesDisplayText}", "NotesManagementService");
    }
    
    private void UpdateNotesCountDisplay()
    {
        LoggingService.LogInfo($"Notes count updated: {_notesVM.NotesCount}", "NotesManagementService");
    }
    
    private void UpdateNotesVisibility()
    {
        LoggingService.LogInfo($"Notes visibility updated: {_notesVM.HasNotes}", "NotesManagementService");
    }
}
```

### Advanced Usage with Complete Notes Management

```csharp
public async Task<bool> ManageCompleteNotesWorkflowAsync(NotesManagementViewModel notesVM, ClientNotesData notesData)
{
    try
    {
        // Clear any existing notes
        notesVM.Clear();
        
        // Add initial notes from data
        foreach (var noteContent in notesData.InitialNotes)
        {
            notesVM.AddNoteWithContent(noteContent, false);
        }
        
        // Add important notes that require attention
        foreach (var importantNote in notesData.ImportantNotes)
        {
            notesVM.AddNoteWithContent(importantNote, true);
        }
        
        // Process follow-up notes based on client status
        if (notesData.RequiresFollowUp)
        {
            var followUpNote = GenerateFollowUpNote(notesData.ClientStatus);
            notesVM.AddNoteWithContent(followUpNote, true);
        }
        
        // Add document status notes
        if (notesData.DocumentStatus?.Any() == true)
        {
            foreach (var docStatus in notesData.DocumentStatus)
            {
                var statusNote = $"وثيقة {docStatus.DocumentType}: {docStatus.Status}";
                var isImportant = docStatus.Status.Contains("مفقود") || docStatus.Status.Contains("منتهي");
                notesVM.AddNoteWithContent(statusNote, isImportant);
            }
        }
        
        // Validate all notes
        if (!notesVM.IsValid())
        {
            LoggingService.LogWarning("Notes validation failed during complete workflow", "NotesManagement");
            return false;
        }
        
        // Generate summary
        var summary = GenerateNotesSummary(notesVM);
        LoggingService.LogInfo($"Notes workflow completed: {summary}", "NotesManagement");
        
        return true;
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error in complete notes workflow: {ex.Message}", "NotesManagement");
        ErrorManager.HandleErrorToast(ex,
            "حدث خطأ أثناء إدارة الملاحظات",
            "خطأ في سير العمل",
            LogLevel.Error,
            "NotesManagement");
        return false;
    }
}

private string GenerateFollowUpNote(string clientStatus)
{
    return clientStatus switch
    {
        "PendingDocuments" => "العميل في انتظار تقديم الوثائق المطلوبة",
        "UnderReview" => "الملف قيد المراجعة - يحتاج متابعة",
        "RequiresCorrection" => "يحتاج تصحيح في البيانات المقدمة",
        "ReadyForApproval" => "الملف جاهز للموافقة النهائية",
        _ => "يحتاج متابعة عامة"
    };
}

private string GenerateNotesSummary(NotesManagementViewModel notesVM)
{
    var totalNotes = notesVM.NotesCount;
    var importantNotes = notesVM.GetImportantNotes().Count;
    var regularNotes = totalNotes - importantNotes;
    
    return $"إجمالي الملاحظات: {totalNotes} (مهمة: {importantNotes}, عادية: {regularNotes})";
}

// Supporting data classes
public class ClientNotesData
{
    public List<string> InitialNotes { get; set; } = new();
    public List<string> ImportantNotes { get; set; } = new();
    public bool RequiresFollowUp { get; set; }
    public string ClientStatus { get; set; } = string.Empty;
    public List<DocumentStatusInfo> DocumentStatus { get; set; } = new();
}

public class DocumentStatusInfo
{
    public string DocumentType { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}
```

### Integration with NewClientViewModel

```csharp
// Example of how NotesManagementViewModel integrates with NewClientViewModel
public class NewClientViewModel : BaseViewModel
{
    public NotesManagementViewModel NotesManagement { get; }
    
    public NewClientViewModel()
    {
        NotesManagement = new NotesManagementViewModel();
        
        // Subscribe to property changes
        NotesManagement.PropertyChanged += OnNotesManagementPropertyChanged;
    }
    
    private void OnNotesManagementPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // React to notes management changes
        switch (e.PropertyName)
        {
            case nameof(NotesManagementViewModel.NotesDisplayText):
                // Update main view display
                OnPropertyChanged(nameof(NotesDisplayText));
                break;
                
            case nameof(NotesManagementViewModel.HasNotes):
                // Update save button state or validation
                UpdateCanSave();
                break;
                
            case nameof(NotesManagementViewModel.NotesCount):
                // Update UI indicators
                UpdateNotesIndicators();
                break;
        }
    }
    
    // Delegate properties for easy access
    public string NotesDisplayText => NotesManagement.NotesDisplayText;
    public int NotesCount => NotesManagement.NotesCount;
    public bool HasNotes => NotesManagement.HasNotes;
    
    private void UpdateNotesIndicators()
    {
        // Update UI elements that show notes count
        LoggingService.LogInfo($"Notes count updated in main view: {NotesCount}", "NewClientViewModel");
    }
}
```

### XAML Data Binding Example

```xml
<UserControl x:Class="UFU2.Views.UserControls.NotesManagementUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Text="إدارة الملاحظات"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,10"/>
        
        <!-- Notes Count Display -->
        <StackPanel Grid.Row="1" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,10">
            <TextBlock Text="عدد الملاحظات: "
                       Style="{StaticResource MaterialDesignBody1TextBlock}"/>
            <TextBlock Text="{Binding NotesCount}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       FontWeight="Bold"/>
            <TextBlock Text=" | "
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       Margin="5,0"/>
            <TextBlock Text="{Binding HasNotes, 
                              Converter={StaticResource BooleanToStringConverter}, 
                              ConverterParameter='يوجد ملاحظات|لا توجد ملاحظات'}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"/>
        </StackPanel>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    Margin="0,0,0,15">
            <Button Content="إضافة ملاحظة"
                    Command="{Binding AddNoteCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Margin="0,0,10,0"/>
            
            <Button Content="إدارة الملاحظات"
                    Command="{Binding ManageNotesCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Margin="0,0,10,0"/>
            
            <Button Content="تعديل"
                    Command="{Binding EditNoteCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsEnabled="{Binding Notes.SelectedNote, Converter={StaticResource NullToBooleanConverter}}"
                    Margin="0,0,10,0"/>
            
            <Button Content="حذف"
                    Command="{Binding DeleteNoteCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    IsEnabled="{Binding Notes.SelectedNote, Converter={StaticResource NullToBooleanConverter}}"
                    Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
        </StackPanel>
        
        <!-- Notes Display -->
        <materialDesign:Card Grid.Row="3"
                            Style="{StaticResource ContentCardStyle}"
                            Visibility="{Binding HasNotes, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="15">
                    <!-- Current Display Text -->
                    <TextBlock Text="آخر ملاحظة:"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Margin="0,0,0,5"/>
                    
                    <TextBlock Text="{Binding NotesDisplayText}"
                               Style="{StaticResource MaterialDesignBody1TextBlock}"
                               TextWrapping="Wrap"
                               Margin="0,0,0,15"/>
                    
                    <!-- Notes List (if needed for detailed view) -->
                    <ItemsControl ItemsSource="{Binding Notes.Notes}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                                        BorderThickness="1"
                                        CornerRadius="4"
                                        Margin="0,0,0,5"
                                        Padding="10">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Content}"
                                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                   TextWrapping="Wrap"/>
                                        
                                        <StackPanel Orientation="Horizontal" 
                                                    Margin="0,5,0,0">
                                            <TextBlock Text="{Binding CreatedDate, StringFormat='dd/MM/yyyy HH:mm'}"
                                                       Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                            
                                            <materialDesign:PackIcon Kind="Star"
                                                                    Width="12" Height="12"
                                                                    Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                                    Margin="10,0,0,0"
                                                                    Visibility="{Binding IsImportant, 
                                                                                Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>
        
        <!-- Empty State -->
        <StackPanel Grid.Row="3"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Visibility="{Binding HasNotes, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
            <materialDesign:PackIcon Kind="NoteOutline"
                                    Width="48" Height="48"
                                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                                    HorizontalAlignment="Center"/>
            <TextBlock Text="لا توجد ملاحظات بعد"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       HorizontalAlignment="Center"
                       Margin="0,10,0,0"/>
            <TextBlock Text="اضغط على 'إضافة ملاحظة' لبدء إضافة الملاحظات"
                       Style="{StaticResource MaterialDesignCaptionTextBlock}"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       HorizontalAlignment="Center"
                       Margin="0,5,0,0"/>
        </StackPanel>
    </Grid>
</UserControl>
```

## Integration with UFU2 Architecture

### MVVM Pattern Compliance

The NotesManagementViewModel exemplifies UFU2's MVVM architecture:

```csharp
// Inherits from BaseViewModel for smart batching
public class NotesManagementViewModel : BaseViewModel
{
    // Uses SetProperty for optimized property notifications
    public NotesCollectionModel Notes
    {
        get => _notes;
        private set => SetProperty(ref _notes, value);
    }
    
    // Command pattern implementation
    public ICommand AddNoteCommand { get; }
    public ICommand EditNoteCommand { get; }
    public ICommand DeleteNoteCommand { get; }
    public ICommand ManageNotesCommand { get; }
    
    // Proper resource disposal
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _notes?.Notes?.Clear();
            LoggingService.LogDebug("NotesManagementViewModel disposed", "NotesManagementViewModel");
        }
        base.Dispose(disposing);
    }
}
```

### Error Handling Integration

```csharp
// Uses UFU2's ErrorManager for consistent error handling
private void AddNote()
{
    try
    {
        LoggingService.LogInfo("Add note dialog requested", "NotesManagementViewModel");
        // Implementation logic here
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error opening add note dialog: {ex.Message}", "NotesManagementViewModel");
        ErrorManager.HandleErrorToast(ex, 
            "حدث خطأ أثناء فتح نافذة إضافة الملاحظة", 
            "خطأ في إضافة الملاحظة", 
            LogLevel.Error, 
            "NotesManagementViewModel");
    }
}
```

### Logging Integration

```csharp
// Comprehensive logging throughout the component
public void AddNoteWithContent(string content, bool isImportant = false)
{
    try
    {
        if (string.IsNullOrWhiteSpace(content))
        {
            LoggingService.LogWarning("Cannot add note with empty content", "NotesManagementViewModel");
            return;
        }

        // Add note logic
        LoggingService.LogInfo($"Note added: {content.Substring(0, Math.Min(50, content.Length))}...", "NotesManagementViewModel");
    }
    catch (Exception ex)
    {
        LoggingService.LogError($"Error adding note: {ex.Message}", "NotesManagementViewModel");
    }
}
```

## Performance Considerations

### Memory Management

The ViewModel implements proper resource disposal:

```csharp
protected override void Dispose(bool disposing)
{
    if (disposing)
    {
        // Clear notes collection to prevent memory leaks
        _notes?.Notes?.Clear();
        LoggingService.LogDebug("NotesManagementViewModel disposed", "NotesManagementViewModel");
    }
    base.Dispose(disposing);
}
```

### Smart Property Batching

Inherits BaseViewModel's smart batching system for optimal UI performance:

- **Normal Priority**: Standard property changes batched at 16ms intervals (60 FPS)
- **High Priority**: Frequent changes batched at 8ms intervals (120 FPS)
- **Critical Priority**: Immediate notifications bypass batching

### Efficient Collection Management

The component uses efficient collection operations:

```csharp
// Efficient property refresh for multiple related properties
public void RefreshNotesDisplay()
{
    OnPropertyChanged(nameof(NotesDisplayText));
    OnPropertyChanged(nameof(NotesCount));
    OnPropertyChanged(nameof(HasNotes));
}

// Optimized search with early returns
public List<NoteModel> SearchNotes(string searchTerm)
{
    if (string.IsNullOrWhiteSpace(searchTerm))
    {
        return GetNotesSortedByDate(); // Return cached sorted list
    }

    return _notes?.Notes?
        .Where(n => n.Content?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)?
        .ToList() ?? new List<NoteModel>();
}
```

### Collection Change Optimization

Automatic UI updates through collection change events:

```csharp
// Subscribe to collection changes for automatic UI updates
_notes.Notes.CollectionChanged += (s, e) => RefreshNotesDisplay();
```

## Component Architecture Diagram

```mermaid
classDiagram
    class NotesManagementViewModel {
        -NotesCollectionModel _notes
        
        +NotesCollectionModel Notes
        +string NotesDisplayText
        +int NotesCount
        +bool HasNotes
        +ICommand AddNoteCommand
        +ICommand EditNoteCommand
        +ICommand DeleteNoteCommand
        +ICommand ManageNotesCommand
        
        +void RefreshNotesDisplay()
        +bool IsValid()
        +void Clear()
        +void AddNoteWithContent(string, bool)
        +void UpdateNote(NoteModel, string, bool)
        +void RemoveNote(NoteModel)
        +List~NoteModel~ GetNotesSortedByDate()
        +List~NoteModel~ GetImportantNotes()
        +List~NoteModel~ SearchNotes(string)
        -void AddNote()
        -void EditNote()
        -void DeleteNote()
        -void ManageNotes()
        +void Dispose(bool)
    }
    
    class BaseViewModel {
        <<abstract>>
        +PropertyPriority enum
        +UIState enum
        +BatchingStrategy enum
        #void SetProperty(ref T, T, string, PropertyPriority)
        #virtual void OnPropertyChanged(string, PropertyPriority)
        +abstract void Dispose(bool)
    }
    
    class NotesCollectionModel {
        +ObservableCollection~NoteModel~ Notes
        +NoteModel SelectedNote
        +int Count
        +IEnumerable~NoteModel~ GetNotesSortedByDate()
        +void Clear()
    }
    
    class NoteModel {
        +string Content
        +int Priority
        +DateTime CreatedDate
        +DateTime ModifiedDate
        +bool IsImportant
        +string FormattedDate
    }
    
    class RelayCommand {
        +bool CanExecute(object)
        +void Execute(object)
        +event EventHandler CanExecuteChanged
    }
    
    class LoggingService {
        <<static>>
        +void LogDebug(string, string)
        +void LogInfo(string, string)
        +void LogWarning(string, string)
        +void LogError(string, string)
    }
    
    class ErrorManager {
        <<static>>
        +void HandleErrorToast(Exception, string, string, LogLevel, string)
    }
    
    NotesManagementViewModel --|> BaseViewModel : inherits
    NotesManagementViewModel --> NotesCollectionModel : contains
    NotesCollectionModel --> NoteModel : manages
    NotesManagementViewModel --> RelayCommand : contains
    NotesManagementViewModel --> LoggingService : uses
    NotesManagementViewModel --> ErrorManager : uses
    
    note for NotesManagementViewModel "Extracted from NewClientViewModel\nfor better separation of concerns\nManages client notes with\nvalidation and search capabilities"
    note for BaseViewModel "Provides smart property batching\nand optimized UI performance"
```

## Data Flow Diagram

```mermaid
flowchart TD
    A[User Action] --> B{Action Type}
    
    B -->|Add Note| C[AddNoteCommand]
    B -->|Edit Note| D[EditNoteCommand]
    B -->|Delete Note| E[DeleteNoteCommand]
    B -->|Manage Notes| F[ManageNotesCommand]
    B -->|Direct Method| G[AddNoteWithContent]
    
    C --> H[AddNote Method]
    D --> I[EditNote Method]
    E --> J[DeleteNote Method]
    F --> K[ManageNotes Method]
    
    G --> L[Create NoteModel]
    L --> M[Add to Collection]
    M --> N[RefreshNotesDisplay]
    
    H --> O[Open Add Dialog]
    I --> P[Open Edit Dialog]
    J --> Q[RemoveNote Method]
    K --> R[Open Management Dialog]
    
    Q --> S[Remove from Collection]
    S --> N
    
    N --> T[Update NotesDisplayText]
    N --> U[Update NotesCount]
    N --> V[Update HasNotes]
    
    T --> W[PropertyChanged Event]
    U --> W
    V --> W
    
    W --> X[BaseViewModel Smart Batching]
    X --> Y[UI Update]
    
    Z[Search Request] --> AA[SearchNotes Method]
    AA --> BB{Search Term Empty?}
    BB -->|Yes| CC[Return All Sorted]
    BB -->|No| DD[Filter by Content]
    CC --> EE[Return Results]
    DD --> EE
    
    FF[Validation Request] --> GG[IsValid Method]
    GG --> HH{Notes Exist?}
    HH -->|No| II[Return True]
    HH -->|Yes| JJ[Check Each Note]
    JJ --> KK{All Valid?}
    KK -->|Yes| II
    KK -->|No| LL[Return False]
    
    style B fill:#e1f5fe
    style N fill:#f3e5f5
    style X fill:#fff3e0
    style AA fill:#e8f5e8
```

## State Transition Diagram

```mermaid
stateDiagram-v2
    [*] --> Initialized : Constructor
    
    Initialized --> Empty : No Notes
    Initialized --> HasNotes : Notes Exist
    
    Empty --> HasNotes : Add First Note
    HasNotes --> Empty : Clear All Notes
    
    HasNotes --> HasNotes : Add Note
    HasNotes --> HasNotes : Update Note
    HasNotes --> HasNotes : Remove Note (Still Has Notes)
    HasNotes --> Empty : Remove Last Note
    
    state HasNotes {
        [*] --> NoSelection
        NoSelection --> NoteSelected : Select Note
        NoteSelected --> NoSelection : Deselect Note
        
        NoteSelected --> NoteSelected : Select Different Note
    }
    
    state "Command States" as CS {
        AddCommand : Always Enabled
        EditCommand : Enabled when Note Selected
        DeleteCommand : Enabled when Note Selected
        ManageCommand : Always Enabled
    }
    
    Empty --> CS : Command Evaluation
    HasNotes --> CS : Command Evaluation
    
    note right of HasNotes
        Properties Updated:
        - NotesDisplayText
        - NotesCount
        - HasNotes
    end note
    
    note right of CS
        Commands use CanExecute
        to determine availability
    end note
```

## Integration Patterns

### With NewClientViewModel

```mermaid
sequenceDiagram
    participant NCV as NewClientViewModel
    participant NMV as NotesManagementViewModel
    participant UI as User Interface
    participant DB as Database

    NCV->>NMV: Initialize Component
    NMV->>NCV: PropertyChanged Events
    
    UI->>NCV: User Adds Note
    NCV->>NMV: AddNoteWithContent()
    NMV->>NMV: Create NoteModel
    NMV->>NMV: RefreshNotesDisplay()
    NMV->>NCV: PropertyChanged(NotesDisplayText)
    NCV->>UI: Update Display
    
    UI->>NCV: Save Client
    NCV->>NMV: IsValid()
    NMV-->>NCV: true/false
    NCV->>DB: Save with Notes
    
    UI->>NCV: Clear Form
    NCV->>NMV: Clear()
    NMV->>NMV: Clear Collection
    NMV->>NCV: PropertyChanged Events
    NCV->>UI: Reset Display
```

### Error Handling Flow

```mermaid
flowchart TD
    A[User Action] --> B[Method Execution]
    B --> C{Exception Occurs?}
    
    C -->|No| D[Success Path]
    C -->|Yes| E[Catch Exception]
    
    E --> F[LoggingService.LogError]
    F --> G[ErrorManager.HandleErrorToast]
    G --> H[Show Arabic Error Message]
    
    D --> I[LoggingService.LogInfo]
    I --> J[Continue Normal Flow]
    
    H --> K[User Sees Error]
    J --> L[User Sees Success]
    
    style E fill:#ffebee
    style G fill:#fff3e0
    style I fill:#e8f5e8
```

This comprehensive documentation provides complete coverage of the NotesManagementViewModel component, including its architecture, usage patterns, integration with UFU2 systems, and performance considerations. The component successfully implements the single responsibility principle while maintaining full compatibility with UFU2's MVVM architecture and Arabic localization requirements.