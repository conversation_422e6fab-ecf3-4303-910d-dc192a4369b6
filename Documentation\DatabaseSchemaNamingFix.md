# UFU2 Database Schema Naming Convention Fix

## Problem Summary
UFU2 is experiencing SQLite Error 1: 'table clients has no column named name_fr' due to systematic naming convention mismatches between the database schema definition (PascalCase) and code implementation (snake_case).

## Root Cause Analysis
The UFU2 database schema (UFU2_Schema.sql) uses **PascalCase naming convention** for all tables and columns, but the application code uses **snake_case naming convention** in SQL queries and entity mappings.

### Schema vs Code Mismatch Examples
| Schema (Correct) | Code (Incorrect) | Status |
|------------------|------------------|---------|
| `Clients` | `clients` | ❌ Fixed |
| `NameFr` | `name_fr` | ❌ Fixed |
| `NameAr` | `name_ar` | ❌ Fixed |
| `BirthDate` | `birth_date` | ❌ Fixed |
| `PhoneNumbers` | `phone_numbers` | ❌ Fixed |
| `ClientUid` | `client_uid` | ❌ Fixed |
| `Activities` | `activities` | ⚠️ In Progress |
| `UidSequences` | `uid_sequences` | ✅ Already Fixed |

## Systematic Fix Implementation

### 1. Database Entity Models (✅ Partially Complete)
**File**: `Models/DatabaseEntities.cs`
- Updated documentation comments to reference correct PascalCase column names
- Fixed ClientEntity, PhoneNumberEntity, and UidSequenceEntity
- **Remaining**: Complete ActivityEntity and other entities

### 2. Database Service Queries (🔄 In Progress)
**File**: `Services/ClientDatabaseService.cs`
- ✅ Fixed: `InsertClientEntityAsync()` - Uses `Clients` table with PascalCase columns
- ✅ Fixed: `GetClientEntityAsync()` - Uses PascalCase column names
- ✅ Fixed: Client update queries - Uses PascalCase naming
- ✅ Fixed: Client delete operations - Simplified to use CASCADE DELETE
- ✅ Fixed: Phone number queries - Uses `PhoneNumbers` table
- ⚠️ **Remaining**: Activity-related queries, file check states, notes, payment years

### 3. Schema Validation Services (⚠️ Pending)
**Files**: 
- `Services/DatabaseValidationService.cs`
- `Services/DatabaseSchemaValidator.cs`

### 4. Critical Fixes Applied

#### Client Creation Fix
```sql
-- Before (Incorrect)
INSERT INTO clients (uid, name_fr, name_ar, birth_date, birth_place, gender, address, national_id, is_active, created_at, updated_at)

-- After (Correct)
INSERT INTO Clients (Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt)
```

#### Client Retrieval Fix
```sql
-- Before (Incorrect)
SELECT uid as Uid, name_fr as NameFr, name_ar as NameAr FROM clients WHERE uid = @ClientUID AND is_active = 1

-- After (Correct)
SELECT Uid, NameFr, NameAr, BirthDate, BirthPlace, Gender, Address, NationalId, CreatedAt, UpdatedAt FROM Clients WHERE Uid = @ClientUID
```

#### Schema Alignment Issues Resolved
1. **Removed non-existent columns**: `IsActive` column doesn't exist in Clients table
2. **Simplified delete operations**: Use CASCADE DELETE instead of soft delete
3. **Added missing UID generation**: PhoneNumbers table requires Uid primary key

## Remaining Work

### High Priority (Blocking Client Creation)
1. **Activity-related queries** in ClientDatabaseService.cs:
   - `InsertActivityEntityAsync()`
   - Activity retrieval and update queries
   - Commercial activity codes
   - Non-commercial activity descriptions

2. **File check states queries**:
   - FileCheckStates table queries
   - G12Check and BisCheck table queries

3. **Notes queries**:
   - Notes table insert and select queries

### Medium Priority (Validation & Error Handling)
1. **DatabaseValidationService.cs**: Update all validation queries
2. **DatabaseSchemaValidator.cs**: Fix table and column definitions
3. **Error handling**: Add schema mismatch detection

### Low Priority (Documentation & Testing)
1. Update entity model documentation
2. Comprehensive testing of all database operations
3. Performance validation

## Implementation Strategy

### Phase 1: Complete Critical Database Service Fixes
- Finish all ClientDatabaseService.cs queries
- Focus on client creation, activity creation, and basic CRUD operations
- Test client creation functionality

### Phase 2: Validation Services
- Update DatabaseValidationService.cs queries
- Fix DatabaseSchemaValidator.cs table definitions
- Add comprehensive error handling

### Phase 3: Testing & Validation
- Test all database operations
- Validate UID generation works correctly
- Ensure no regressions in existing functionality

## Expected Outcomes
- ✅ Client creation works without SQLite column name errors
- ✅ All database operations use consistent PascalCase naming
- ✅ Maintains existing nested transaction fixes and performance optimizations
- ✅ Preserves UFU2 architecture patterns (MVVM, Arabic RTL, ErrorManager)
- ✅ No breaking changes to public APIs

## Risk Mitigation
- ✅ Created backup files before modifications
- ✅ Systematic approach to avoid missing any queries
- ✅ Security validation with Semgrep
- ✅ Maintains backward compatibility where possible
- ✅ Comprehensive documentation of all changes
