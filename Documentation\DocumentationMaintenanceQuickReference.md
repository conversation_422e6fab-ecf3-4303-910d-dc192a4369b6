# UFU2 Documentation Maintenance Quick Reference

## 🚨 MANDATORY RULES FOR AI AGENTS

### Before Making ANY Code Changes
```
□ Identify which documentation files will be affected
□ Plan documentation updates alongside code changes
□ Verify Arabic localization requirements
□ Check security implications for examples
```

### During Code Implementation
```
□ Update documentation concurrently with code
□ Ensure all examples follow UFU2 patterns
□ Include Arabic error messages
□ Validate MaterialDesign/RTL compliance
```

### After Code Changes
```
□ Run security_check_semgrep on all new examples
□ Verify all code examples compile and run
□ Test Arabic RTL layout examples
□ Validate cross-references and links
```

---

## 📋 Change Type → Documentation Mapping

### Service Layer Changes
| Change | Primary Doc | Secondary Docs |
|--------|-------------|----------------|
| New Service | APIReference.md | ArchitectureOverview.md |
| Method Change | APIReference.md | All usage examples |
| Business Rule | APIReference.md, BusinessProcessGuide.md | - |
| Error Handling | APIReference.md | All error examples |

### UI Layer Changes
| Change | Primary Doc | Secondary Docs |
|--------|-------------|----------------|
| New ViewModel | UIArchitectureGuide.md | ArchitectureOverview.md |
| New UserControl | UIArchitectureGuide.md | Component guides |
| XAML Pattern | UIArchitectureGuide.md | ThemeManagementGuide.md |
| RTL Layout | UIArchitectureGuide.md | All Arabic examples |

### Infrastructure Changes
| Change | Primary Doc | Secondary Docs |
|--------|-------------|----------------|
| Database Schema | ArchitectureOverview.md, DeploymentGuide.md | APIReference.md |
| Configuration | DeploymentGuide.md | ArchitectureOverview.md |
| Performance | PerformanceGuide.md | ArchitectureOverview.md |
| Security | All affected docs | Security validation required |

---

## ✅ Quality Standards Checklist

### Code Examples Must Include:
```csharp
// ✅ REQUIRED: BaseViewModel inheritance
public class ExampleViewModel : BaseViewModel

// ✅ REQUIRED: ServiceLocator dependency injection
_service = ServiceLocator.GetService<ExampleService>();

// ✅ REQUIRED: RelayCommand for UI interactions
public ICommand ExampleCommand { get; }

// ✅ REQUIRED: Proper property change notification
set => SetProperty(ref _field, value);

// ✅ REQUIRED: Arabic error messages
ErrorManager.ShowUserErrorToast("رسالة عربية", "عنوان", "Source");

// ✅ REQUIRED: Parameterized queries only
await connection.QueryAsync<T>("SELECT * FROM Table WHERE ID = @ID", new { ID = id });
```

### XAML Examples Must Include:
```xaml
<!-- ✅ REQUIRED: MaterialDesign with DynamicResource -->
<Button Style="{DynamicResource MaterialDesignRaisedButton}"
        Background="{DynamicResource PrimaryBrush}" />

<!-- ✅ REQUIRED: Arabic RTL support -->
<UserControl FlowDirection="RightToLeft">
    <TextBlock Text="النص العربي" HorizontalAlignment="Right" />
</UserControl>
```

---

## 🔍 Validation Requirements

### Security Validation (MANDATORY)
```
1. Run: security_check_semgrep on all new code examples
2. Verify: Parameterized queries used exclusively
3. Check: No sensitive information in examples
4. Validate: Proper input sanitization patterns
5. Ensure: Error handling doesn't leak sensitive data
```

### Arabic Localization Validation (MANDATORY)
```
1. All user-facing error messages in Arabic
2. UI labels and hints in Arabic
3. Toast notifications with Arabic text
4. RTL layout properly implemented
5. Arabic text alignment correct
```

### Performance Validation (MANDATORY)
```
1. Document performance impact of changes
2. Update baselines if optimization implemented
3. Include before/after metrics
4. Verify monitoring guidance is current
5. Update implementation priorities if needed
```

---

## 🚀 Quick Action Guide

### New Service Method Added
```
1. Update APIReference.md with complete method documentation
2. Include usage example with ServiceLocator pattern
3. Add error handling with Arabic messages
4. Run security scan on example
5. Update ArchitectureOverview.md if new dependencies
```

### New ViewModel Created
```
1. Update UIArchitectureGuide.md with complete example
2. Show BaseViewModel inheritance pattern
3. Include ServiceLocator dependency injection
4. Add RelayCommand usage example
5. Include Arabic RTL XAML example
```

### Database Schema Modified
```
1. Update ArchitectureOverview.md database section
2. Update DeploymentGuide.md migration procedures
3. Update APIReference.md for affected services
4. Update BusinessProcessGuide.md if business rules changed
5. Document performance impact in PerformanceGuide.md
```

### Performance Optimization Implemented
```
1. Update PerformanceGuide.md with before/after metrics
2. Document implementation steps with code examples
3. Update performance baselines and targets
4. Add monitoring and profiling guidance
5. Update implementation priority rankings
```

### UI Component Added
```
1. Update UIArchitectureGuide.md with complete example
2. Include MaterialDesign styling patterns
3. Show Arabic RTL layout implementation
4. Add reusable component patterns
5. Include data binding examples
```

---

## ❌ Common Mistakes to Avoid

### Code Examples
```
❌ Dynamic SQL: "SELECT * FROM Table WHERE ID = '" + id + "'"
✅ Parameterized: "SELECT * FROM Table WHERE ID = @ID", new { ID = id }

❌ English errors: ErrorManager.ShowUserErrorToast("Error occurred", ...)
✅ Arabic errors: ErrorManager.ShowUserErrorToast("حدث خطأ", ...)

❌ Direct instantiation: new ExampleService()
✅ ServiceLocator: ServiceLocator.GetService<ExampleService>()

❌ Manual PropertyChanged: PropertyChanged?.Invoke(...)
✅ BaseViewModel: SetProperty(ref _field, value)
```

### XAML Examples
```
❌ Static resources: Background="Blue"
✅ Dynamic resources: Background="{DynamicResource PrimaryBrush}"

❌ LTR only: <TextBlock Text="Arabic text" />
✅ RTL support: <TextBlock Text="النص العربي" FlowDirection="RightToLeft" />

❌ No MaterialDesign: <Button Content="Save" />
✅ MaterialDesign: <Button Content="حفظ" Style="{DynamicResource MaterialDesignRaisedButton}" />
```

---

## 📊 Success Metrics

### Documentation Quality Targets
- **API Coverage**: 100% of public methods documented
- **Example Accuracy**: 100% of examples compile and run
- **Arabic Localization**: 100% of user-facing content translated
- **Security Compliance**: 100% of examples pass security validation
- **Performance Tracking**: All optimizations documented with metrics

### Validation Frequency
- **Per Change**: Security scan, compilation check, Arabic validation
- **Weekly**: Cross-reference validation, performance metric check
- **Monthly**: Comprehensive documentation audit
- **Quarterly**: Full architectural consistency review

---

## 🆘 Emergency Procedures

### Critical Issues (Fix Immediately)
```
🚨 Security scan failures
🚨 Compilation errors in examples
🚨 Broken cross-references
🚨 Missing Arabic translations
🚨 Architectural pattern violations
```

### Resolution Steps
```
1. Stop current work
2. Fix the critical issue
3. Re-run all validations
4. Verify fix doesn't break other documentation
5. Continue with original task
```

---

## 📞 Quick Reference Summary

**Remember**: Documentation is code. Treat it with the same rigor as your implementation.

**Golden Rules**:
1. Update documentation in the same session as code changes
2. All examples must follow UFU2 architectural patterns
3. All user-facing content must include Arabic translations
4. All code examples must pass security validation
5. Performance impacts must be documented with metrics

**When in doubt**: Over-document rather than under-document. It's easier to refine comprehensive documentation than to fill gaps later.

This quick reference ensures that UFU2's comprehensive documentation suite remains current, accurate, and valuable as the codebase evolves.
