# Database Entities Documentation

## Overview

The Database Entities are data models that represent the database schema structure for UFU2's client management system. These entities are designed for Dapper ORM mapping and provide the foundation for all database operations.

## What the Code Does

The entities represent:
- **ClientEntity**: Core client information with personal details
- **ActivityEntity**: Business activities associated with clients
- **PhoneNumberEntity**: Phone number records with type and primary designation
- **FileCheckStateEntity**: Document verification states for activities
- **NoteEntity**: Client notes with flags and priorities
- **PaymentYearEntity**: G12Check and BisCheck payment year tracking

All entities follow UFU2's database naming conventions (snake_case) and include proper nullable field support.

## How It's Used

### Basic Entity Usage with Dapper

```csharp
// Query client entity from database
var client = await connection.QueryFirstOrDefaultAsync<ClientEntity>(
    "SELECT * FROM clients WHERE client_id = @ClientId",
    new { ClientId = clientId }
);

// Map entity to business model
var clientModel = new ClientModel
{
    UID = client.client_uid,
    FirstName = client.first_name,
    LastName = client.last_name,
    Gender = client.gender,
    BirthDate = client.birth_date
};
```

### Entity Relationships

```csharp
// Query client with related activities
var clientWithActivities = await connection.QueryAsync<ClientEntity, ActivityEntity, ClientEntity>(
    @"SELECT c.*, a.*
      FROM clients c
      LEFT JOIN activities a ON c.client_id = a.client_id
      WHERE c.client_id = @ClientId",
    (client, activity) =>
    {
        client.Activities = client.Activities ?? new List<ActivityEntity>();
        if (activity != null)
        {
            client.Activities.Add(activity);
        }
        return client;
    },
    new { ClientId = clientId },
    splitOn: "activity_id"
);
```

### Entity Creation and Insertion

```csharp
public async Task<string> CreateClientEntityAsync(ClientCreationData clientData)
{
    var clientEntity = new ClientEntity
    {
        client_id = Guid.NewGuid().ToString(),
        client_uid = await _uidService.GenerateClientUIDAsync(),
        first_name = clientData.FirstName,
        last_name = clientData.LastName,
        gender = clientData.Gender,
        birth_date = clientData.BirthDate,
        created_at = DateTime.UtcNow,
        updated_at = DateTime.UtcNow
    };
    
    await connection.ExecuteAsync(
        @"INSERT INTO clients (client_id, client_uid, first_name, last_name, gender, birth_date, created_at, updated_at)
          VALUES (@client_id, @client_uid, @first_name, @last_name, @gender, @birth_date, @created_at, @updated_at)",
        clientEntity
    );
    
    return clientEntity.client_id;
}
```

### Complex Entity Queries

```csharp
public async Task<List<ClientEntity>> GetClientsWithCompleteDataAsync()
{
    var clientDictionary = new Dictionary<string, ClientEntity>();
    
    var clients = await connection.QueryAsync<ClientEntity, ActivityEntity, PhoneNumberEntity, NoteEntity, ClientEntity>(
        @"SELECT c.*, a.*, p.*, n.*
          FROM clients c
          LEFT JOIN activities a ON c.client_id = a.client_id
          LEFT JOIN phone_numbers p ON c.client_id = p.client_id
          LEFT JOIN notes n ON c.client_id = n.client_id
          ORDER BY c.created_at DESC",
        (client, activity, phone, note) =>
        {
            if (!clientDictionary.TryGetValue(client.client_id, out var existingClient))
            {
                existingClient = client;
                existingClient.Activities = new List<ActivityEntity>();
                existingClient.PhoneNumbers = new List<PhoneNumberEntity>();
                existingClient.Notes = new List<NoteEntity>();
                clientDictionary.Add(client.client_id, existingClient);
            }
            
            if (activity != null && !existingClient.Activities.Any(a => a.activity_id == activity.activity_id))
            {
                existingClient.Activities.Add(activity);
            }
            
            if (phone != null && !existingClient.PhoneNumbers.Any(p => p.phone_id == phone.phone_id))
            {
                existingClient.PhoneNumbers.Add(phone);
            }
            
            if (note != null && !existingClient.Notes.Any(n => n.note_id == note.note_id))
            {
                existingClient.Notes.Add(note);
            }
            
            return existingClient;
        },
        splitOn: "activity_id,phone_id,note_id"
    );
    
    return clientDictionary.Values.ToList();
}
```

### Entity Validation and Business Rules

```csharp
public class ClientEntity
{
    // Database fields
    public string client_id { get; set; }
    public string client_uid { get; set; }
    public string first_name { get; set; }
    public string last_name { get; set; }
    public string? gender { get; set; }
    public DateTime? birth_date { get; set; }
    
    // Navigation properties (not mapped to database)
    [Dapper.Contrib.Extensions.Write(false)]
    public List<ActivityEntity> Activities { get; set; } = new List<ActivityEntity>();
    
    [Dapper.Contrib.Extensions.Write(false)]
    public List<PhoneNumberEntity> PhoneNumbers { get; set; } = new List<PhoneNumberEntity>();
    
    // Validation methods
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(client_uid) &&
               !string.IsNullOrWhiteSpace(first_name) &&
               !string.IsNullOrWhiteSpace(last_name);
    }
    
    public string GetDisplayName()
    {
        return $"{first_name} {last_name}".Trim();
    }
}
```

## Integration with UFU2 Architecture

The entities integrate with UFU2's patterns:

- **Dapper ORM**: Designed for efficient Dapper mapping with proper attribute usage
- **Naming Conventions**: Follow database snake_case naming while supporting C# property access
- **Nullable Support**: Proper nullable reference types for optional fields
- **Navigation Properties**: Support for related entity loading with proper Dapper attributes
- **Validation**: Built-in validation methods for business rule enforcement

## Performance Considerations

- **Efficient Mapping**: Optimized for Dapper's micro-ORM performance characteristics
- **Lazy Loading**: Navigation properties loaded only when explicitly queried
- **Minimal Overhead**: Lightweight entities with minimal computational overhead
- **Index-Friendly**: Designed to work efficiently with database indexes
- **Bulk Operations**: Support for bulk insert/update operations
- **Memory Efficient**: Minimal memory footprint for large result sets

## Mermaid Diagram

```mermaid
erDiagram
    ClientEntity {
        string client_id PK
        string client_uid UK
        string first_name
        string last_name
        string gender
        datetime birth_date
        datetime created_at
        datetime updated_at
    }
    
    ActivityEntity {
        string activity_id PK
        string client_id FK
        string activity_uid UK
        string activity_type
        string activity_code
        string description
        datetime created_at
        datetime updated_at
    }
    
    PhoneNumberEntity {
        string phone_id PK
        string client_id FK
        string phone_number
        string phone_type
        boolean is_primary
        datetime created_at
        datetime updated_at
    }
    
    FileCheckStateEntity {
        string client_id FK
        string activity_id FK
        string file_check_type
        boolean is_completed
        datetime completed_at
        datetime updated_at
    }
    
    NoteEntity {
        string note_id PK
        string client_id FK
        string note_text
        string flag_type
        integer priority
        datetime created_at
        datetime updated_at
    }
    
    PaymentYearEntity {
        string payment_id PK
        string client_id FK
        string payment_type
        integer payment_year
        boolean is_completed
        datetime completed_at
        datetime created_at
        datetime updated_at
    }
    
    ClientEntity ||--o{ ActivityEntity : "has many"
    ClientEntity ||--o{ PhoneNumberEntity : "has many"
    ClientEntity ||--o{ NoteEntity : "has many"
    ClientEntity ||--o{ PaymentYearEntity : "has many"
    ActivityEntity ||--o{ FileCheckStateEntity : "has many"
```

## Entity Mapping Flow

```mermaid
sequenceDiagram
    participant Service as Database Service
    participant Dapper as Dapper ORM
    participant DB as SQLite Database
    participant Entity as Entity Objects

    Service->>Dapper: QueryAsync<ClientEntity>(sql, parameters)
    Dapper->>DB: Execute SQL Query
    DB->>Dapper: Return Raw Data Rows
    
    loop For each row
        Dapper->>Entity: Create ClientEntity instance
        Dapper->>Entity: Map database columns to properties
        Entity->>Dapper: Return populated entity
    end
    
    Dapper->>Service: Return List<ClientEntity>
    
    alt Complex Query with Joins
        Service->>Dapper: QueryAsync<Client,Activity,Phone>(sql)
        Dapper->>DB: Execute JOIN Query
        DB->>Dapper: Return Joined Data
        Dapper->>Entity: Map multiple entity types
        Dapper->>Service: Return aggregated entities
    end
```

## Entity Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Created: New Entity Instance
    Created --> Populated: Map from Database
    Created --> Initialized: Set Properties
    
    Populated --> Validated: Business Rule Check
    Initialized --> Validated: Business Rule Check
    
    Validated --> Ready: Validation Passed
    Validated --> Invalid: Validation Failed
    
    Ready --> Persisted: Save to Database
    Ready --> Updated: Modify Properties
    Updated --> Validated: Re-validate
    
    Persisted --> [*]: Entity Saved
    Invalid --> [*]: Validation Error
```