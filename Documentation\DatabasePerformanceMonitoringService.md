# DatabasePerformanceMonitoringService Documentation

## Overview

The `DatabasePerformanceMonitoringService` provides comprehensive database performance monitoring, query analysis, and optimization recommendations for UFU2. It tracks query performance, monitors index effectiveness, and provides automated database maintenance operations.

## What the Code Does

The service provides:
- **Query Performance Monitoring**: Tracks execution times and identifies slow queries
- **Index Effectiveness Analysis**: Monitors index usage and provides optimization recommendations
- **Database Maintenance**: Automated VACUUM and ANALYZE operations
- **Performance Metrics Collection**: Gathers and reports database performance statistics
- **Query Plan Analysis**: Uses EXPLAIN QUERY PLAN for optimization insights

## How It's Used

### Basic Performance Monitoring

```csharp
// Get service from ServiceLocator
var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();

// Start monitoring a query
using var monitor = perfService.StartQueryMonitoring("GetClientById");

// Execute your database operation
var client = await clientService.GetClientByIdAsync(clientId);

// Monitor automatically records performance metrics
```

### Query Performance Analysis

```csharp
public async Task AnalyzeQueryPerformanceAsync()
{
    var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();
    
    // Get slow query report
    var slowQueries = await perfService.GetSlowQueriesAsync(TimeSpan.FromSeconds(1));
    
    foreach (var query in slowQueries)
    {
        LoggingService.LogWarning($"Slow query detected: {query.QueryName} - {query.ExecutionTime}ms");
        
        // Get optimization recommendations
        var recommendations = await perfService.GetOptimizationRecommendationsAsync(query.QueryText);
        
        foreach (var recommendation in recommendations)
        {
            LoggingService.LogInformation($"Optimization suggestion: {recommendation}");
        }
    }
}
```

### Index Effectiveness Monitoring

```csharp
public async Task MonitorIndexEffectivenessAsync()
{
    var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();
    
    // Get index usage statistics
    var indexStats = await perfService.GetIndexUsageStatsAsync();
    
    foreach (var stat in indexStats)
    {
        if (stat.UsageCount == 0)
        {
            LoggingService.LogWarning($"Unused index detected: {stat.IndexName}");
        }
        else if (stat.EffectivenessRatio < 0.1)
        {
            LoggingService.LogWarning($"Low effectiveness index: {stat.IndexName} - {stat.EffectivenessRatio:P}");
        }
    }
    
    // Get recommendations for new indexes
    var indexRecommendations = await perfService.GetIndexRecommendationsAsync();
    
    foreach (var recommendation in indexRecommendations)
    {
        LoggingService.LogInformation($"Index recommendation: {recommendation.TableName}.{recommendation.ColumnName}");
    }
}
```

### Automated Database Maintenance

```csharp
public async Task PerformDatabaseMaintenanceAsync()
{
    var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();
    
    try
    {
        // Check if maintenance is needed
        var maintenanceInfo = await perfService.GetMaintenanceInfoAsync();
        
        if (maintenanceInfo.VacuumRecommended)
        {
            LoggingService.LogInformation("Starting database VACUUM operation");
            await perfService.VacuumDatabaseAsync();
            LoggingService.LogInformation("Database VACUUM completed");
        }
        
        if (maintenanceInfo.AnalyzeRecommended)
        {
            LoggingService.LogInformation("Starting database ANALYZE operation");
            await perfService.AnalyzeDatabaseAsync();
            LoggingService.LogInformation("Database ANALYZE completed");
        }
        
        // Update statistics
        await perfService.UpdateStatisticsAsync();
    }
    catch (Exception ex)
    {
        ErrorManager.HandleError(ex, "خطأ في صيانة قاعدة البيانات");
    }
}
```

### Performance Report Generation

```csharp
public async Task GeneratePerformanceReportAsync()
{
    var perfService = ServiceLocator.GetService<DatabasePerformanceMonitoringService>();
    
    // Generate comprehensive performance report
    var report = await perfService.GeneratePerformanceReportAsync(
        startDate: DateTime.Now.AddDays(-7),
        endDate: DateTime.Now
    );
    
    LoggingService.LogInformation($"Performance Report Summary:");
    LoggingService.LogInformation($"- Total Queries: {report.TotalQueries}");
    LoggingService.LogInformation($"- Average Response Time: {report.AverageResponseTime}ms");
    LoggingService.LogInformation($"- Slow Queries: {report.SlowQueryCount}");
    LoggingService.LogInformation($"- Index Hit Ratio: {report.IndexHitRatio:P}");
    
    // Save detailed report
    await perfService.SavePerformanceReportAsync(report, "performance_report.json");
}
```

## Integration with UFU2 Architecture

The service integrates with UFU2's architectural patterns:

- **ServiceLocator Pattern**: Registered for dependency injection
- **Background Processing**: Runs monitoring tasks on background threads
- **Error Handling**: Uses `ErrorManager` for consistent error management
- **Logging**: Comprehensive logging through `LoggingService`
- **Database Access**: Built on `DatabaseService` with connection pooling
- **Configuration**: Integrates with UFU2 configuration system

## Performance Considerations

- **Minimal Overhead**: Monitoring designed to have minimal impact on application performance
- **Asynchronous Operations**: All monitoring operations are non-blocking
- **Configurable Thresholds**: Customizable performance thresholds and alert levels
- **Efficient Data Collection**: Uses sampling and aggregation to minimize storage overhead
- **Background Processing**: Heavy analysis operations run on background threads
- **Resource Management**: Proper disposal and cleanup of monitoring resources

## Mermaid Diagram

```mermaid
graph TB
    subgraph "Performance Monitoring System"
        DPMS[DatabasePerformanceMonitoringService]
        QM[Query Monitor]
        IM[Index Monitor]
        MM[Maintenance Manager]
        RM[Report Manager]
    end
    
    subgraph "Data Collection"
        DPMS --> QM
        DPMS --> IM
        QM --> QP[Query Performance Data]
        IM --> IS[Index Statistics]
        MM --> MS[Maintenance Statistics]
    end
    
    subgraph "Analysis & Recommendations"
        QP --> QA[Query Analyzer]
        IS --> IA[Index Analyzer]
        QA --> OR[Optimization Recommendations]
        IA --> IR[Index Recommendations]
    end
    
    subgraph "Database Operations"
        DPMS --> DS[DatabaseService]
        DS --> SQLite[(SQLite Database)]
        MM --> Vacuum[VACUUM Operations]
        MM --> Analyze[ANALYZE Operations]
    end
    
    subgraph "Reporting & Alerts"
        RM --> PR[Performance Reports]
        OR --> Alerts[Performance Alerts]
        IR --> Alerts
    end
    
    style DPMS fill:#e1f5fe
    style SQLite fill:#f3e5f5
    style Alerts fill:#fff3e0
    style PR fill:#e8f5e8
```

## Monitoring Flow Diagram

```mermaid
sequenceDiagram
    participant App as Application
    participant DPMS as PerformanceMonitoringService
    participant QM as Query Monitor
    participant DS as DatabaseService
    participant DB as SQLite Database
    participant LS as LoggingService

    App->>DPMS: StartQueryMonitoring("QueryName")
    DPMS->>QM: Create Monitor Instance
    QM->>DPMS: Return Monitor
    DPMS->>App: Return Monitor
    
    App->>DS: ExecuteQueryAsync()
    DS->>DB: Execute SQL Query
    DB->>DS: Return Results
    DS->>App: Return Data
    
    App->>QM: Dispose() [using statement]
    QM->>QM: Calculate Execution Time
    QM->>DPMS: RecordPerformanceMetric()
    
    alt Slow Query Detected
        DPMS->>LS: LogWarning("Slow query detected")
        DPMS->>DPMS: Analyze Query Plan
        DPMS->>LS: LogInformation("Optimization recommendations")
    end
    
    DPMS->>DPMS: Update Performance Statistics
```

## Performance Monitoring State Machine

```mermaid
stateDiagram-v2
    [*] --> Idle: Service Started
    
    Idle --> Monitoring: Start Query Monitor
    Monitoring --> Collecting: Query Executing
    Collecting --> Analyzing: Query Complete
    Analyzing --> Recording: Analysis Complete
    Recording --> Idle: Metrics Recorded
    
    Analyzing --> AlertGenerated: Threshold Exceeded
    AlertGenerated --> Recording: Alert Sent
    
    Idle --> Maintenance: Scheduled Maintenance
    Maintenance --> VacuumRunning: VACUUM Needed
    Maintenance --> AnalyzeRunning: ANALYZE Needed
    VacuumRunning --> Idle: VACUUM Complete
    AnalyzeRunning --> Idle: ANALYZE Complete
    
    Idle --> ReportGeneration: Generate Report
    ReportGeneration --> Idle: Report Complete
    
    Idle --> Disposed: Service Disposed
    Disposed --> [*]
```