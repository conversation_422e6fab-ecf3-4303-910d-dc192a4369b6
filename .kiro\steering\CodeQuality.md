---
inclusion: always
---

# UFU2 Code Quality Standards

## Mandatory Architecture Patterns

### MVVM Implementation
- **Always inherit from `BaseViewModel`** for all ViewModels
- **Use `RelayCommand`** for all UI interactions - never create custom command classes
- **Keep Views lightweight** - all logic belongs in ViewModels
- **Use `ServiceLocator.GetService<T>()`** for dependency injection

### Database Access
- **Use `DatabaseService`** for all SQLite operations with Dapper
- **Always use async/await** for database calls
- **Use parameterized queries** to prevent SQL injection
- **Wrap multi-step operations in transactions**

## Code Reuse Requirements

**Before creating new code, check existing implementations:**
- ViewModels: Property patterns, validation logic, command implementations
- Services: Data access patterns, business logic
- XAML: Reusable styles in `/Resources/Styles/`
- Converters: Existing value converters in `/Converters/`
- Utilities: Helper methods in `/Common/`

**Never duplicate code** - extend or reference existing implementations.

## UI and Localization Standards

### Material Design
- **Use `DynamicResource`** for all colors and styles
- **Support Light/Dark themes** via `ThemeManager`
- **Follow existing component patterns** in `/Resources/Styles/`

### RTL Support (Critical)
- **Primary language: Arabic (RTL)**
- **All UI must support RTL text flow**
- **Test with Arabic content** for layout validation
- **Use appropriate text alignment** for RTL languages

## Error Handling (Mandatory)

- **Use `ErrorManager`** for all exception handling
- **Use `LoggingService`** for all logging operations
- **Provide Arabic error messages** for user-facing errors
- **Handle database connection failures gracefully**

## Performance Requirements

- **Use `Dispatcher.Invoke`** for UI thread safety
- **Implement `IDisposable`** for resource cleanup
- **Use async operations** for heavy work
- **Debounce user input validation**

## Production Code Only

**Create only production-ready code** - no test files, mocks, or testing infrastructure.
**Focus on core business functions:** client management, activity tracking, document management, payment processing.