# تحديثات نصوص المساعدة في واجهة المستخدم
# UI Help Text Updates

## نظرة عامة | Overview

هذا المستند يحدد التحديثات المطلوبة لنصوص المساعدة والتلميحات في واجهة المستخدم لدعم تنسيقات التاريخ المرنة الجديدة.

This document specifies required updates to help text and tooltips in the user interface to support new flexible date formats.

---

## التحديثات المطلوبة | Required Updates

### 1. نموذج المعلومات الشخصية | Personal Information Form
**الملف:** `Views/NewClient/NPersonalView.xaml`

#### تاريخ الميلاد | Birth Date Field
```xml
<!-- التحديث الحالي | Current Update -->
<TextBox x:Name="BirthDateTextBox"
         materialDesign:HintAssist.Hint="تاريخ الميلاد (DD/MM/YYYY)"
         ToolTip="أدخل تاريخ الميلاد بصيغة DD/MM/YYYY أو xx/xx/yyyy للتاريخ الجزئي أو xx/xx/xxxx للتاريخ غير المعروف" />

<!-- التحديث المقترح | Suggested Update -->
<TextBox x:Name="BirthDateTextBox"
         materialDesign:HintAssist.Hint="تاريخ الميلاد (DD/MM/YYYY)"
         ToolTip="أدخل تاريخ الميلاد:&#x0A;• DD/MM/YYYY للتاريخ الكامل (مثل: 25/07/1985)&#x0A;• xx/xx/yyyy للسنة المعروفة فقط (مثل: xx/xx/1985)&#x0A;• xx/xx/xxxx للتاريخ غير المعروف" />
```

### 2. نماذج تفاصيل النشاط | Activity Detail Forms
**الملف:** `Views/NewClient/NActivityDetailView.xaml`

#### تاريخ بداية النشاط | Activity Start Date Field
```xml
<!-- التحديث المطلوب | Required Update -->
<TextBox x:Name="ActivityStartDateTextBox"
         materialDesign:HintAssist.Hint="تاريخ بداية النشاط (DD/MM/YYYY)"
         ToolTip="أدخل تاريخ بداية النشاط:&#x0A;• DD/MM/YYYY للتاريخ المحدد (مثل: 01/01/2020)&#x0A;• xx/xx/yyyy إذا كانت السنة معروفة فقط (مثل: xx/xx/2020)&#x0A;• xx/xx/xxxx إذا كان التاريخ غير معروف" />
```

### 3. نافذة تحديث حالة النشاط | Activity Status Update Dialog
**الملف:** `Views/Dialogs/ActivityStatusUpdateDialog.xaml`

#### تاريخ التحديث | Update Date Field
```xml
<!-- التحديث الحالي | Current Update -->
<TextBox x:Name="UpdateDateTextBox"
         materialDesign:HintAssist.Hint="تاريخ التحديث (DD/MM/YYYY)"
         ToolTip="أدخل تاريخ التحديث بصيغة DD/MM/YYYY أو xx/xx/yyyy للتاريخ الجزئي أو xx/xx/xxxx للتاريخ غير المعروف" />

<!-- التحديث المحسن | Enhanced Update -->
<TextBox x:Name="UpdateDateTextBox"
         materialDesign:HintAssist.Hint="تاريخ التحديث (DD/MM/YYYY)"
         ToolTip="أدخل تاريخ التحديث:&#x0A;• DD/MM/YYYY للتاريخ المحدد (مثل: 15/12/2024)&#x0A;• xx/xx/yyyy للسنة المعروفة (مثل: xx/xx/2024)&#x0A;• xx/xx/xxxx للتاريخ غير المحدد&#x0A;&#x0A;نصيحة: اكتب الأرقام فقط وسيتم التنسيق تلقائياً" />
```

---

## تحديثات رسائل التحقق | Validation Message Updates

### 1. رسائل الخطأ المحسنة | Enhanced Error Messages

#### رسالة التنسيق غير الصحيح | Invalid Format Message
```csharp
// في ValidationMessages.cs
// In ValidationMessages.cs

// الحالي | Current
public const string InvalidDateFormat = "تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/xxxx";

// المحدث | Updated
public const string InvalidDateFormat = "تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/yyyy أو xx/xx/xxxx";
```

#### رسائل مساعدة إضافية | Additional Help Messages
```csharp
/// <summary>
/// Help text for date input formats.
/// </summary>
public const string DateFormatHelp = "استخدم: DD/MM/YYYY للتاريخ الكامل، xx/xx/yyyy للسنة المعروفة، xx/xx/xxxx للتاريخ غير المعروف";

/// <summary>
/// Quick input tip for date fields.
/// </summary>
public const string DateInputTip = "نصيحة: اكتب الأرقام فقط وسيتم التنسيق تلقائياً";
```

---

## تحديثات التلميحات التفاعلية | Interactive Tooltip Updates

### 1. تلميحات ديناميكية | Dynamic Tooltips

#### مثال للتطبيق | Implementation Example
```xml
<!-- تلميح تفاعلي محسن | Enhanced Interactive Tooltip -->
<TextBox x:Name="DateInputTextBox">
    <TextBox.ToolTip>
        <ToolTip MaxWidth="400" HasDropShadow="True">
            <StackPanel Orientation="Vertical" Margin="8">
                <TextBlock Text="تنسيقات التاريخ المدعومة" 
                          FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryBrush}" 
                          Margin="0,0,0,8"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- التاريخ الكامل | Complete Date -->
                    <materialDesign:PackIcon Grid.Row="0" Grid.Column="0" 
                                           Kind="CheckCircle" 
                                           Foreground="Green" 
                                           Margin="0,0,8,4"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" 
                              Text="DD/MM/YYYY - للتاريخ الكامل (مثل: 15/03/2023)" 
                              Margin="0,0,0,4"/>
                    
                    <!-- التاريخ الجزئي | Partial Date -->
                    <materialDesign:PackIcon Grid.Row="1" Grid.Column="0" 
                                           Kind="CheckCircle" 
                                           Foreground="Orange" 
                                           Margin="0,0,8,4"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" 
                              Text="xx/xx/yyyy - للسنة المعروفة (مثل: xx/xx/2023)" 
                              Margin="0,0,0,4"/>
                    
                    <!-- التاريخ غير المعروف | Unknown Date -->
                    <materialDesign:PackIcon Grid.Row="2" Grid.Column="0" 
                                           Kind="HelpCircle" 
                                           Foreground="Gray" 
                                           Margin="0,0,8,8"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" 
                              Text="xx/xx/xxxx - للتاريخ غير المعروف" 
                              Margin="0,0,0,8"/>
                    
                    <!-- نصيحة | Tip -->
                    <materialDesign:PackIcon Grid.Row="3" Grid.Column="0" 
                                           Kind="Lightbulb" 
                                           Foreground="{DynamicResource AccentBrush}" 
                                           Margin="0,0,8,0"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" 
                              Text="نصيحة: اكتب الأرقام فقط وسيتم التنسيق تلقائياً" 
                              FontStyle="Italic"
                              Foreground="{DynamicResource SecondaryTextBrush}"/>
                </Grid>
            </StackPanel>
        </ToolTip>
    </TextBox.ToolTip>
</TextBox>
```

---

## تحديثات نصوص المساعدة السياقية | Contextual Help Text Updates

### 1. مساعدة مخصصة حسب نوع النشاط | Activity Type-Specific Help

#### للأنشطة التجارية | For Commercial Activities
```xml
<TextBox ToolTip="تاريخ بداية النشاط التجاري:&#x0A;• استخدم التاريخ الدقيق إذا كان متاحاً&#x0A;• xx/xx/yyyy إذا كانت السنة معروفة من السجل التجاري&#x0A;• xx/xx/xxxx للأنشطة القديمة بدون تواريخ واضحة"/>
```

#### للأنشطة الحرفية | For Craft Activities
```xml
<TextBox ToolTip="تاريخ بداية النشاط الحرفي:&#x0A;• DD/MM/YYYY للتاريخ المحدد&#x0A;• xx/xx/yyyy إذا كانت السنة معروفة من شهادة الحرفة&#x0A;• xx/xx/xxxx للحرف التقليدية الموروثة"/>
```

#### للأنشطة المهنية | For Professional Activities
```xml
<TextBox ToolTip="تاريخ بداية النشاط المهني:&#x0A;• DD/MM/YYYY لتاريخ بداية الممارسة&#x0A;• xx/xx/yyyy إذا كانت السنة معروفة من الشهادة&#x0A;• xx/xx/xxxx للمهن الحرة بدون تاريخ محدد"/>
```

---

## تحديثات رسائل النجاح | Success Message Updates

### 1. رسائل الحفظ المحسنة | Enhanced Save Messages

```csharp
// في ErrorManager أو ValidationMessages
// In ErrorManager or ValidationMessages

/// <summary>
/// Success message for flexible date save.
/// </summary>
public const string FlexibleDateSaved = "تم حفظ التاريخ بنجاح بالتنسيق المرن";

/// <summary>
/// Success message with format confirmation.
/// </summary>
public static string FormatFlexibleDateSaveSuccess(string dateValue, string formatType)
{
    return $"تم حفظ التاريخ '{dateValue}' بتنسيق {formatType} بنجاح";
}
```

---

## إرشادات التطبيق | Implementation Guidelines

### 1. معايير التصميم | Design Standards

#### الألوان والأيقونات | Colors and Icons
- **التاريخ الكامل:** أخضر مع أيقونة CheckCircle
- **التاريخ الجزئي:** برتقالي مع أيقونة CheckCircle
- **التاريخ غير المعروف:** رمادي مع أيقونة HelpCircle
- **النصائح:** لون التمييز مع أيقونة Lightbulb

**Complete Date:** Green with CheckCircle icon
**Partial Date:** Orange with CheckCircle icon
**Unknown Date:** Gray with HelpCircle icon
**Tips:** Accent color with Lightbulb icon

#### التخطيط | Layout
- استخدم `&#x0A;` لفواصل الأسطر في XAML
- الحد الأقصى لعرض التلميح: 400 بكسل
- هامش داخلي: 8 بكسل
- فصل المحتوى بخطوط فارغة

**Use `&#x0A;` for line breaks in XAML**
**Maximum tooltip width: 400 pixels**
**Internal margin: 8 pixels**
**Separate content with empty lines**

### 2. اختبار التحديثات | Testing Updates

#### قائمة التحقق | Checklist
- [ ] جميع التلميحات تظهر بشكل صحيح
- [ ] النصوص العربية محاذاة لليمين
- [ ] الأيقونات تظهر بالألوان الصحيحة
- [ ] التنسيق يعمل مع الثيمات المختلفة
- [ ] لا توجد نصوص مقطوعة أو متداخلة

**All tooltips display correctly**
**Arabic text is right-aligned**
**Icons show in correct colors**
**Formatting works with different themes**
**No truncated or overlapping text**

---

## الخطوات التالية | Next Steps

### 1. تطبيق التحديثات | Apply Updates
1. تحديث ملفات XAML المحددة
2. تحديث ValidationMessages.cs
3. اختبار جميع التلميحات
4. مراجعة التصميم مع الفريق

**Update specified XAML files**
**Update ValidationMessages.cs**
**Test all tooltips**
**Review design with team**

### 2. التوثيق | Documentation
1. تحديث دليل المستخدم
2. إنشاء لقطات شاشة جديدة
3. تحديث مواد التدريب
4. إعلام المستخدمين بالتحديثات

**Update user guide**
**Create new screenshots**
**Update training materials**
**Notify users of updates**

---

*تحديثات نصوص المساعدة - نظام UFU2 | UI Help Text Updates - UFU2 System*
*إعداد: فريق التطوير | Prepared by: Development Team*
*التاريخ: يناير 2025 | Date: January 2025*
