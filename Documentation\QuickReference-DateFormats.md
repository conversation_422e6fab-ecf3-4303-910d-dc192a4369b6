# بطاقة مرجعية سريعة - تنسيقات التاريخ
# Quick Reference Card - Date Formats

---

## تنسيقات التاريخ المدعومة | Supported Date Formats

| التنسيق | Format | مثال | Example | الاستخدام | Usage |
|---------|--------|------|---------|----------|-------|
| `dd/MM/yyyy` | Complete Date | `15/03/2023` | Known full date | تاريخ كامل معروف | Known full date |
| `xx/xx/yyyy` | Partial with Year | `xx/xx/2023` | Known year only | السنة معروفة فقط | Known year only |
| `xx/xx/xxxx` | Unknown Date | `xx/xx/xxxx` | Unknown date | تاريخ غير معروف | Unknown date |

---

## الحقول المدعومة | Supported Fields

### ✅ تاريخ بداية النشاط | Activity Start Date
- **الموقع:** نماذج الأنشطة (تجاري، حرفي، مهني)
- **Location:** Activity forms (Commercial, Craft, Professional)

### ✅ تاريخ تحديث النشاط | Activity Update Date  
- **الموقع:** نافذة تحديث حالة النشاط
- **Location:** Activity Status Update Dialog

### ✅ تاريخ الميلاد | Birth Date
- **الموقع:** نموذج المعلومات الشخصية
- **Location:** Personal Information Form

---

## إدخال سريع | Quick Input

| اكتب | Type | النتيجة | Result |
|------|------|--------|--------|
| `15032023` | Numbers only | `15/03/2023` | Auto-formatted |
| `xx2023` | x + year | `xx/xx/2023` | Partial format |
| `xxxx` | All x | `xx/xx/xxxx` | Unknown format |

---

## رسائل الخطأ الشائعة | Common Error Messages

### ❌ "تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/xxxx"
**الحل:** استخدم أحد التنسيقات المدعومة
**Solution:** Use one of the supported formats

### ❌ "التاريخ المدخل غير صحيح"
**الحل:** تأكد من صحة اليوم والشهر (1-31، 1-12)
**Solution:** Ensure valid day and month (1-31, 1-12)

### ❌ "هذا الحقل مطلوب"
**الحل:** أدخل تاريخاً أو استخدم `xx/xx/xxxx`
**Solution:** Enter a date or use `xx/xx/xxxx`

---

## قواعد الإدخال | Input Rules

### ✅ الأحرف المسموحة | Allowed Characters
- **الأرقام:** 0-9
- **الحروف:** x, X (للتواريخ غير المعروفة)
- **الرموز:** / (يُضاف تلقائياً)

**Numbers:** 0-9
**Letters:** x, X (for unknown dates)
**Symbols:** / (added automatically)

### ✅ القيود | Constraints
- **الحد الأقصى:** 10 أحرف
- **نطاق السنوات:** 1900-2100
- **التنسيق التلقائي:** يُطبق أثناء الكتابة

**Maximum length:** 10 characters
**Year range:** 1900-2100
**Auto-formatting:** Applied while typing

---

## أمثلة عملية | Practical Examples

### 📋 سيناريو: تسجيل عميل جديد
**Client Registration Scenario**

| الحالة | Situation | الإدخال | Input | النتيجة | Result |
|--------|-----------|--------|-------|--------|--------|
| تاريخ ميلاد معروف | Known birth date | `25071985` | `25/07/1985` |
| سنة الميلاد فقط | Birth year only | `xx1985` | `xx/xx/1985` |
| تاريخ غير معروف | Unknown date | `xxxx` | `xx/xx/xxxx` |

### 📋 سيناريو: إنشاء نشاط تجاري
**Business Activity Creation Scenario**

| الحالة | Situation | الإدخال | Input | النتيجة | Result |
|--------|-----------|--------|-------|--------|--------|
| بداية محددة | Specific start | `01012020` | `01/01/2020` |
| بدأ في 2020 | Started in 2020 | `xx2020` | `xx/xx/2020` |
| تاريخ غير محدد | Unspecified date | `xxxx` | `xx/xx/xxxx` |

---

## اختصارات مفيدة | Useful Shortcuts

| المفتاح | Key | الوظيفة | Function |
|---------|-----|---------|----------|
| `Tab` | Tab | الحقل التالي | Next field |
| `Shift+Tab` | Shift+Tab | الحقل السابق | Previous field |
| `Ctrl+A` | Ctrl+A | تحديد الكل | Select all |
| `Delete` | Delete | مسح المحتوى | Clear content |

---

## نصائح سريعة | Quick Tips

### 💡 أفضل الممارسات | Best Practices
1. **ابدأ بالأدق:** استخدم التاريخ الكامل إذا كان متاحاً
2. **لا تترك فارغاً:** `xx/xx/xxxx` أفضل من حقل فارغ
3. **تحقق من السنة:** تأكد من نطاق 1900-2100

**Start precise:** Use complete date if available
**Don't leave empty:** `xx/xx/xxxx` is better than empty field
**Check year:** Ensure 1900-2100 range

### ⚡ إدخال سريع | Speed Input
- **للتواريخ الكاملة:** اكتب 8 أرقام متتالية
- **للسنة فقط:** اكتب `xx` ثم السنة
- **للمجهول:** اكتب `xxxx`

**For complete dates:** Type 8 consecutive numbers
**For year only:** Type `xx` then year
**For unknown:** Type `xxxx`

---

## الدعم | Support

### 📞 اتصل بنا | Contact Us
- **الهاتف:** +213-XXX-XXXX
- **البريد:** <EMAIL>
- **الساعات:** الأحد-الخميس، 8:00-17:00

**Phone:** +213-XXX-XXXX
**Email:** <EMAIL>
**Hours:** Sunday-Thursday, 8:00 AM-5:00 PM

### 🔗 روابط مفيدة | Useful Links
- [دليل المستخدم الكامل](UserTraining-FlexibleDateFormats.md)
- [شرائح التدريب](Training-DateFormats-Slides.md)
- [أسئلة شائعة](FAQ-DateFormats.md)

**[Complete User Guide](UserTraining-FlexibleDateFormats.md)**
**[Training Slides](Training-DateFormats-Slides.md)**
**[FAQ](FAQ-DateFormats.md)**

---

*بطاقة مرجعية سريعة - نظام UFU2 | Quick Reference Card - UFU2 System*
*الإصدار 1.0 - يناير 2025 | Version 1.0 - January 2025*
