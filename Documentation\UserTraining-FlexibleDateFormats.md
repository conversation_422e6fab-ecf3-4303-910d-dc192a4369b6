# دليل المستخدم - تنسيقات التاريخ المرنة في UFU2
# UFU2 User Guide - Flexible Date Format Training

## نظرة عامة | Overview

يدعم نظام UFU2 تنسيقات مرنة لإدخال التواريخ عبر جميع نماذج التطبيق، مما يتيح للمستخدمين إدخال التواريخ الكاملة أو الجزئية أو غير المعروفة حسب المعلومات المتاحة.

UFU2 supports flexible date input formats across all application forms, allowing users to enter complete, partial, or unknown dates based on available information.

---

## تنسيقات التاريخ المدعومة | Supported Date Formats

### 1. التواريخ الكاملة | Complete Dates
**التنسيق:** `dd/MM/yyyy`
**مثال:** `15/03/2023`
**الاستخدام:** عندما تكون جميع معلومات التاريخ معروفة

**Format:** `dd/MM/yyyy`
**Example:** `15/03/2023`
**Usage:** When all date information is known

### 2. التواريخ الجزئية مع السنة المعروفة | Partial Dates with Known Year
**التنسيق:** `xx/xx/yyyy`
**مثال:** `xx/xx/2023`
**الاستخدام:** عندما تكون السنة فقط معروفة

**Format:** `xx/xx/yyyy`
**Example:** `xx/xx/2023`
**Usage:** When only the year is known

### 3. التواريخ غير المعروفة | Unknown Dates
**التنسيق:** `xx/xx/xxxx`
**مثال:** `xx/xx/xxxx`
**الاستخدام:** عندما يكون التاريخ غير معروف بالكامل

**Format:** `xx/xx/xxxx`
**Example:** `xx/xx/xxxx`
**Usage:** When the date is completely unknown

---

## المجالات المدعومة | Supported Fields

### 1. تاريخ بداية النشاط | Activity Start Date
**الموقع:** نماذج إنشاء الأنشطة (تجاري رئيسي، تجاري ثانوي، حرفي، مهني)
**Location:** Activity creation forms (MainCommercial, SecondaryCommercial, Craft, Professional)

**أمثلة الاستخدام | Usage Examples:**
- `15/03/2020` - تاريخ بداية النشاط معروف بالكامل
- `xx/xx/2020` - بدأ النشاط في عام 2020 لكن الشهر واليوم غير معروفين
- `xx/xx/xxxx` - تاريخ بداية النشاط غير معروف

### 2. تاريخ تحديث النشاط | Activity Update Date
**الموقع:** نافذة تحديث حالة النشاط
**Location:** Activity Status Update Dialog

**أمثلة الاستخدام | Usage Examples:**
- `10/12/2024` - تاريخ التحديث محدد
- `xx/xx/2024` - تم التحديث في عام 2024
- `xx/xx/xxxx` - تاريخ التحديث غير محدد

### 3. تاريخ الميلاد | Birth Date
**الموقع:** نموذج المعلومات الشخصية للعميل
**Location:** Client Personal Information Form

**أمثلة الاستخدام | Usage Examples:**
- `25/07/1985` - تاريخ ميلاد كامل
- `xx/xx/1985` - سنة الميلاد معروفة فقط
- `xx/xx/xxxx` - تاريخ الميلاد غير معروف

---

## إرشادات الإدخال | Input Guidelines

### التنسيق التلقائي | Automatic Formatting
- يقوم النظام بتنسيق التاريخ تلقائياً أثناء الكتابة
- System automatically formats dates while typing
- يمكن إدخال الأرقام فقط وسيتم إضافة الشرطات المائلة تلقائياً
- Enter numbers only, slashes will be added automatically

### قواعد الإدخال | Input Rules
1. **الأرقام والرموز المسموحة:** 0-9, x, X, /
2. **الحد الأقصى للطول:** 10 أحرف
3. **نطاق السنوات المقبولة:** 1900-2100

**Allowed characters:** 0-9, x, X, /
**Maximum length:** 10 characters
**Accepted year range:** 1900-2100

### أمثلة الإدخال السريع | Quick Input Examples
- اكتب `15032023` ← يصبح `15/03/2023`
- اكتب `xx2023` ← يصبح `xx/xx/2023`
- اكتب `xxxx` ← يصبح `xx/xx/xxxx`

**Type `15032023` → becomes `15/03/2023`**
**Type `xx2023` → becomes `xx/xx/2023`**
**Type `xxxx` → becomes `xx/xx/xxxx`**

---

## رسائل الخطأ الشائعة | Common Error Messages

### رسائل التحقق | Validation Messages
- **"تنسيق التاريخ يجب أن يكون DD/MM/YYYY أو xx/xx/xxxx"**
  - السبب: تنسيق غير صحيح
  - الحل: استخدم أحد التنسيقات المدعومة

- **"التاريخ المدخل غير صحيح"**
  - السبب: تاريخ غير موجود (مثل 32/13/2023)
  - الحل: تأكد من صحة اليوم والشهر

- **"هذا الحقل مطلوب"**
  - السبب: الحقل فارغ والتاريخ مطلوب
  - الحل: أدخل تاريخاً أو استخدم xx/xx/xxxx

**"Date format must be DD/MM/YYYY or xx/xx/xxxx"**
- Cause: Incorrect format
- Solution: Use one of the supported formats

**"Invalid date entered"**
- Cause: Non-existent date (e.g., 32/13/2023)
- Solution: Ensure valid day and month

**"This field is required"**
- Cause: Empty field when date is required
- Solution: Enter a date or use xx/xx/xxxx

---

## السيناريوهات التجارية | Business Scenarios

### متى تستخدم كل تنسيق | When to Use Each Format

#### التواريخ الكاملة (dd/MM/yyyy)
- تسجيل عميل جديد بتاريخ ميلاد معروف
- بداية نشاط تجاري بتاريخ محدد
- تحديث حالة النشاط في تاريخ معين

**Complete Dates (dd/MM/yyyy)**
- Registering new client with known birth date
- Starting business activity on specific date
- Updating activity status on specific date

#### التواريخ الجزئية (xx/xx/yyyy)
- عميل يتذكر سنة الميلاد فقط
- نشاط بدأ في سنة معينة لكن التاريخ الدقيق غير معروف
- تحديث تم في سنة محددة

**Partial Dates (xx/xx/yyyy)**
- Client remembers only birth year
- Activity started in specific year but exact date unknown
- Update occurred in specific year

#### التواريخ غير المعروفة (xx/xx/xxxx)
- معلومات التاريخ غير متوفرة
- وثائق قديمة بدون تواريخ واضحة
- حالات الطوارئ أو البيانات المفقودة

**Unknown Dates (xx/xx/xxxx)**
- Date information not available
- Old documents without clear dates
- Emergency cases or missing data

---

## نصائح للاستخدام الفعال | Tips for Effective Usage

### أفضل الممارسات | Best Practices
1. **ابدأ بالتنسيق الأكثر دقة:** استخدم التاريخ الكامل إذا كان متاحاً
2. **استخدم التواريخ الجزئية للمعلومات المحدودة:** xx/xx/yyyy للسنة المعروفة
3. **احفظ البيانات حتى لو كانت ناقصة:** xx/xx/xxxx أفضل من ترك الحقل فارغاً

**Start with most precise format:** Use complete date if available
**Use partial dates for limited information:** xx/xx/yyyy for known year
**Save data even if incomplete:** xx/xx/xxxx is better than empty field

### اختصارات لوحة المفاتيح | Keyboard Shortcuts
- **Tab:** الانتقال للحقل التالي
- **Shift+Tab:** العودة للحقل السابق
- **Ctrl+A:** تحديد النص بالكامل
- **Delete/Backspace:** مسح المحتوى

**Tab:** Move to next field
**Shift+Tab:** Return to previous field
**Ctrl+A:** Select all text
**Delete/Backspace:** Clear content

---

## الدعم الفني | Technical Support

### استكشاف الأخطاء وإصلاحها | Troubleshooting
إذا واجهت مشاكل في إدخال التواريخ:
1. تأكد من استخدام التنسيق الصحيح
2. تحقق من نطاق السنوات (1900-2100)
3. استخدم الأحرف المسموحة فقط
4. اتصل بالدعم الفني إذا استمرت المشكلة

**If you encounter date input issues:**
1. Ensure correct format usage
2. Check year range (1900-2100)
3. Use only allowed characters
4. Contact technical support if issues persist

### معلومات الاتصال | Contact Information
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +213-XXX-XXXX
- **ساعات العمل:** الأحد - الخميس، 8:00 - 17:00

**Email:** <EMAIL>
**Phone:** +213-XXX-XXXX
**Business Hours:** Sunday - Thursday, 8:00 AM - 5:00 PM

---

*هذا الدليل جزء من وثائق نظام UFU2 لإدارة العملاء والأنشطة التجارية*
*This guide is part of UFU2 Client and Business Activity Management System documentation*
