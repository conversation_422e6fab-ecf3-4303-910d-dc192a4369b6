# UIDGenerationService Documentation

## Overview

The `UIDGenerationService` is a core UFU2 service responsible for generating unique identifiers for clients and activities following specific business rules. It ensures uniqueness through database-managed sequences and provides transaction safety for concurrent operations.

## What the Code Does

The service generates three types of UIDs:
- **Client UIDs**: Follow the pattern `{FirstLetter}{SequentialNumber:D2}` (e.g., "A01", "B02")
- **Activity UIDs**: Follow the pattern `{ClientUID}_Act{ActivitySequence}` (e.g., "A01_Act1", "A01_Act2")
- **Phone Number UIDs**: Follow the pattern `{ClientUID}_Phone_{8-char-guid}` (e.g., "A01_Phone_a1b2c3d4", "B02_Phone_f5e6d7c8")

The service manages database sequences to ensure uniqueness and handles concurrent operations safely through transaction management.

## How It's Used

### Basic Usage

```csharp
// Get service from ServiceLocator
var uidService = ServiceLocator.GetService<UIDGenerationService>();

// Generate a client UID
string clientUid = await uidService.GenerateClientUIDAsync();
// Result: "A01", "B02", etc.

// Generate an activity UID for a client
string activityUid = await uidService.GenerateActivityUIDAsync("A01");
// Result: "A01_Act1", "A01_Act2", etc.

// Generate a phone number UID for a client
string phoneUid = uidService.GeneratePhoneNumberUID("A01");
// Result: "A01_Phone_a1b2c3d4", "A01_Phone_f5e6d7c8", etc.
```

### Advanced Usage with Error Handling

```csharp
try
{
    var uidService = ServiceLocator.GetService<UIDGenerationService>();
    
    // Generate multiple UIDs in a transaction-safe manner
    var clientUid = await uidService.GenerateClientUIDAsync();
    var activityUid = await uidService.GenerateActivityUIDAsync(clientUid);
    
    // Use the UIDs for database operations
    await clientService.CreateClientAsync(new ClientCreationData 
    { 
        UID = clientUid,
        // ... other properties
    });
}
catch (Exception ex)
{
    ErrorManager.HandleError(ex, "خطأ في إنشاء معرف العميل");
}
```

### Integration with Client Creation

```csharp
public async Task<string> CreateNewClientAsync(ClientCreationData clientData)
{
    var uidService = ServiceLocator.GetService<UIDGenerationService>();
    
    // Generate UID automatically
    clientData.UID = await uidService.GenerateClientUIDAsync();
    
    // Create client with generated UID
    return await _clientDatabaseService.CreateClientAsync(clientData);
}
```

## Integration with UFU2 Architecture

The service integrates seamlessly with UFU2's established patterns:

- **ServiceLocator Pattern**: Registered and accessed through `ServiceLocator.GetService<UIDGenerationService>()`
- **Error Handling**: Uses `ErrorManager` for consistent error handling with Arabic messages
- **Logging**: Integrates with `LoggingService` for operation tracking
- **Database Access**: Uses `DatabaseService` for SQLite operations with Dapper
- **Async Patterns**: All operations are async/await for non-blocking UI

## Performance Considerations

- **Database Sequences**: Uses SQLite sequences for efficient UID generation
- **Transaction Safety**: Wraps operations in transactions to prevent conflicts
- **Connection Pooling**: Leverages DatabaseService connection management
- **Concurrent Operations**: Thread-safe design prevents UID collisions
- **Resource Management**: Implements `IDisposable` for proper cleanup

## Mermaid Diagram

```mermaid
sequenceDiagram
    participant VM as ViewModel
    participant SL as ServiceLocator
    participant UID as UIDGenerationService
    participant DB as DatabaseService
    participant EM as ErrorManager
    participant LS as LoggingService

    VM->>SL: GetService<UIDGenerationService>()
    SL->>VM: Return UIDGenerationService
    
    VM->>UID: GenerateClientUIDAsync()
    UID->>LS: LogInformation("Generating Client UID")
    UID->>DB: BeginTransactionAsync()
    UID->>DB: GetNextSequenceValueAsync("client_uid_seq")
    DB->>UID: Return sequence number
    UID->>UID: Format UID ({FirstLetter}{Number:D2})
    UID->>DB: CommitTransactionAsync()
    UID->>LS: LogInformation("Client UID generated: {uid}")
    UID->>VM: Return Client UID
    
    alt Error Occurs
        UID->>DB: RollbackTransactionAsync()
        UID->>EM: HandleError(ex, "خطأ في إنشاء معرف العميل")
        UID->>VM: Throw Exception
    end
```

## State Transitions

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Ready: Service Created
    
    Ready --> GeneratingClientUID: GenerateClientUIDAsync()
    GeneratingClientUID --> TransactionStarted: Begin Transaction
    TransactionStarted --> SequenceRetrieved: Get Next Sequence
    SequenceRetrieved --> UIDFormatted: Format UID String
    UIDFormatted --> TransactionCommitted: Commit Transaction
    TransactionCommitted --> Ready: Return UID
    
    Ready --> GeneratingActivityUID: GenerateActivityUIDAsync()
    GeneratingActivityUID --> ValidatingClientUID: Validate Client UID
    ValidatingClientUID --> ActivitySequenceRetrieved: Get Activity Sequence
    ActivitySequenceRetrieved --> ActivityUIDFormatted: Format Activity UID
    ActivityUIDFormatted --> Ready: Return Activity UID
    
    GeneratingClientUID --> Error: Exception Occurs
    GeneratingActivityUID --> Error: Exception Occurs
    Error --> TransactionRolledBack: Rollback Transaction
    TransactionRolledBack --> Ready: Error Handled
    
    Ready --> Disposed: Dispose()
    Disposed --> [*]
```