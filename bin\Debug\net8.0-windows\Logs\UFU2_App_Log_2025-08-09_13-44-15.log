=== UFU2 Application Session Started at 2025-08-09 13:44:15 ===
[2025-08-09 13:44:15.888]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 13:44:15.900]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 13:44:15.907]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 13:44:15.913]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 13:44:15.968]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 13:44:15.973]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 13:44:15.978]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 13:44:15.990]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 13:44:15.996]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 13:44:16.002]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 13:44:16.009]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 13:44:16.014]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 13:44:16.019]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 13:44:16.025]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 13:44:16.030]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 13:44:16.035]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 13:44:16.040]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 13:44:16.045]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 13:44:16.060]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 13:44:16.065]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 13:44:16.071]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 13:44:16.076]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 13:44:16.082]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 13:44:16.094]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 13:44:16.104]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 13:44:16.114]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 13:44:16.121]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 13:44:16.131]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 13:44:16.137]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 13:44:16.143]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 13:44:16.150]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 13:44:16.158]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 13:44:16.165]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 13:44:16.176]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 13:44:16.181]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 13:44:16.191]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 13:44:16.196]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 13:44:16.202]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 13:44:16.221]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 120.33MB working set
[2025-08-09 13:44:16.227]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 13:44:16.232]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 13:44:16.239]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 13:44:16.244]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 13:44:16.250]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 13:44:16.258]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 13:44:16.289]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 13:44:16.296]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 13:44:16.307]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 13:44:16.635]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 13:44:16.641]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 13:44:16.649]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 13:44:16.654]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 13:44:16.667]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_51489795_638903402566649772 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 13:44:16.673]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 13:44:16.681]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:16.690]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 13:44:16.721]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 13:44:16.731]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 13:44:16.738]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 13:44:16.745]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 13:44:16.752]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 13:44:16.757]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 13:44:16.762]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 13:44:16.768]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 13:44:16.773]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 13:44:16.779]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 13:44:16.787]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 13:44:16.793]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 13:44:16.845]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 13:44:16.877]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 13:44:16.909]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 13:44:16.916]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 13:44:16.923]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 13:44:16.930]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 13:44:16.936]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 13:44:16.941]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 13:44:16.946]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 13:44:16.955]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 13:44:16.962]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 13:44:16.971]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 13:44:16.985]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 13:44:16.991]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 13:44:16.998]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 13:44:17.005]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 13:44:17.011]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 13:44:17.017]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 13:44:17.023]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 13:44:17.029]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 13:44:17.035]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 13:44:17.041]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 13:44:17.048]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 13:44:17.053]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 13:44:17.059]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 13:44:17.066]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 13:44:17.073]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 13:44:17.079]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 13:44:17.095]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 13:44:17.105]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 13:44:17.111]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 13:44:17.119]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 13:44:17.124]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 13:44:17.130]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 13:44:17.136]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 13:44:17.141]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 13:44:17.147]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 13:44:17.153]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 13:44:17.159]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 13:44:17.165]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 13:44:17.171]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 13:44:17.177]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 13:44:17.187]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 13:44:17.210]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 13:44:17.218]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 13:44:17.225]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 13:44:17.237]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 13:44:17.244]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 13:44:17.251]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 13:44:17.259]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 13:44:17.267]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 1009ms
[2025-08-09 13:44:17.275]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 13:44:17.286]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 13:44:17.331]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 13:44:17.350]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 13:44:17.361]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 13:44:17.372]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 13:44:17.378]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 13:44:17.385]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 13:44:17.393]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 13:44:17.400]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 13:44:17.408]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 13:44:17.425]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 13:44:17.440]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 13:44:17.480]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 13:44:17.498]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 13:44:17.506]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 13:44:17.563]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.563]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.569]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.578]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.583]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.588]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 13:44:17.591]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 13:44:17.597]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 13:44:17.563]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.606]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 13:44:17.616]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 13:44:17.620]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 13:44:17.625]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 13:44:17.631]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 13:44:17.641]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 13:44:17.647]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 13:44:17.656]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 13:44:17.663]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 13:44:17.669]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 13:44:17.685]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 13:44:17.691]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 13:44:17.700]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 13:44:17.707]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:17.717]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 13:44:17.762]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 13:44:17.788]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 13:44:17.797]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 13:44:17.805]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 13:44:17.812]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 13:44:17.821]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 13:44:17.827]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 13:44:17.837]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 13:44:17.845]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 13:44:17.853]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 13:44:17.862]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 13:44:17.873]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 13:44:17.881]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 13:44:17.887]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 13:44:17.894]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 13:44:17.903]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 13:44:17.909]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 13:44:17.916]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 13:44:17.923]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 13:44:17.929]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 13:44:17.938]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 13:44:17.945]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 13:44:17.958]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 13:44:17.966]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 13:44:17.973]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 13:44:17.978]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 13:44:17.985]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 13:44:17.994]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 13:44:18.007]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:18.018]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 13:44:18.027]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 13:44:18.036]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:18.043]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 13:44:18.052]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 13:44:18.060]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 13:44:18.102]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 13:44:18.111]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 13:44:18.119]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 13:44:18.133]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 13:44:18.142]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 13:44:18.152]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 13:44:18.160]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 13:44:18.167]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 13:44:18.173]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:18.179]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 13:44:18.191]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 13:44:18.197]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 13:44:18.204]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 13:44:18.211]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 13:44:18.219]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 13:44:18.229]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 13:44:18.237]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 13:44:18.243]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 13:44:18.249]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 13:44:18.255]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 13:44:18.262]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 13:44:18.270]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 13:44:18.276]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 13:44:18.284]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 13:44:18.292]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 13:44:18.302]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 13:44:18.310]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 13:44:18.318]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 13:44:18.324]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:18.333]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 13:44:18.339]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 13:44:18.345]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:18.352]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 13:44:18.358]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 13:44:18.366]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 13:44:18.373]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 13:44:18.379]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 13:44:18.385]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 13:44:18.392]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 13:44:18.399]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 13:44:18.406]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 13:44:18.412]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 13:44:18.423]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 13:44:18.439]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:18.445]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 13:44:18.462]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 13:44:18.468]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 13:44:18.475]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 13:44:18.481]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 13:44:18.488]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 13:44:18.495]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 13:44:18.504]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 13:44:18.512]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 13:44:18.522]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 13:44:18.529]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 13:44:18.540]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 13:44:18.549]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 13:44:18.557]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 13:44:18.564]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 13:44:18.574]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 13:44:18.580]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 13:44:18.588]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:18.594]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 13:44:18.601]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:18.607]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 13:44:18.613]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 13:44:18.620]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 13:44:18.626]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 13:44:18.633]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 13:44:18.640]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 13:44:18.654]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 13:44:18.685]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 13:44:18.699]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 13:44:18.706]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 13:44:18.712]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 13:44:18.719]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 13:44:18.726]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 13:44:18.739]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 13:44:18.745]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 13:44:18.752]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 13:44:18.758]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 13:44:18.766]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 13:44:18.772]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 13:44:18.779]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 13:44:18.786]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 13:44:18.792]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 13:44:18.798]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 13:44:18.804]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 13:44:18.810]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 13:44:18.817]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 13:44:18.823]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 13:44:18.829]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 13:44:18.836]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 13:44:18.842]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 13:44:18.849]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 13:44:18.854]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 13:44:18.861]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 13:44:18.869]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 13:44:18.879]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 13:44:18.886]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 13:44:18.892]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 13:44:18.899]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 13:44:18.905]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 13:44:18.911]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 13:44:18.917]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 13:44:18.923]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 13:44:18.930]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 13:44:18.939]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 13:44:18.945]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 13:44:18.952]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 13:44:18.958]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 13:44:18.965]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 13:44:18.972]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 13:44:18.980]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 13:44:18.990]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:19.009]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 13:44:19.016]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 13:44:19.022]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 13:44:19.028]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 13:44:19.034]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 13:44:19.040]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 13:44:19.046]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 13:44:19.053]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 13:44:19.059]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 13:44:19.066]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 13:44:19.074]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 13:44:19.082]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 13:44:19.088]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 13:44:19.096]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:19.102]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 13:44:19.108]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:19.120]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:19.127]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:19.134]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 13:44:19.140]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:19.147]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:19.154]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 13:44:19.177]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 13:44:19.282]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 13:44:19.297]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:19.339]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 13:44:19.346]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 13:44:19.356]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:19.393]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 13:44:19.402]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 13:44:19.410]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 13:44:19.417]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 13:44:19.424]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 13:44:19.437]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 13:44:19.869]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:44:19.953]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 13:44:19.960]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 13:44:19.969]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 13:44:20.086]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 13:44:20.099]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 13:44:20.118]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 13:44:20.129]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 13:44:20.144]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 13:44:20.152]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:20.165]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 0 clients, 0 activities
[2025-08-09 13:44:20.191]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 13:44:20.196]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 13:44:20.205]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 13:44:20.214]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 13:44:20.222]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 13:44:20.226]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 13:44:20.241]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 13:44:20.249]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 13:44:20.253]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.265]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 13:44:20.273]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 13:44:20.295]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 13:44:20.309]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 13:44:20.320]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 13:44:20.330]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 13:44:20.350]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 13:44:20.355]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 13:44:20.359]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 13:44:20.367]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.378]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 13:44:20.386]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 13:44:20.395]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 13:44:20.402]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 155ms
[2025-08-09 13:44:20.407]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 13:44:20.435]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 13:44:20.447]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 13:44:20.456]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 13:44:20.463]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 13:44:20.499]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.516]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 13:44:20.523]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.530]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.538]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 13:44:20.545]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.552]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.560]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 13:44:20.567]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.574]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.580]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 13:44:20.589]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.595]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.605]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 13:44:20.613]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.620]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.627]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 13:44:20.635]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.642]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 13:44:20.650]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 193ms
[2025-08-09 13:44:20.657]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 13:44:20.685]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 13:44:20.692]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 36ms
[2025-08-09 13:44:20.702]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 13:44:20.715]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 13:44:20.727]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 26ms
[2025-08-09 13:44:20.739]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 13:44:20.747]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:20.756]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 13:44:20.768]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:20.778]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 13:44:20.785]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:20.792]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 13:44:20.799]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 13:44:20.808]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 13:44:20.815]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 13:44:20.824]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 86ms
[2025-08-09 13:44:20.834]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 13:44:20.843]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.853]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 13:44:20.860]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.869]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.877]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 13:44:20.884]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.890]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.897]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 13:44:20.904]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.910]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.918]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 13:44:20.925]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.933]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.941]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 13:44:20.948]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.957]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.964]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 13:44:20.971]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.977]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:20.984]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 13:44:20.990]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:20.996]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:21.003]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 13:44:21.009]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:21.020]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:21.036]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 13:44:21.042]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:21.053]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 13:44:21.073]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 13:44:21.109]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 13:44:21.289]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 13:44:21.407]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 574ms
[2025-08-09 13:44:21.453]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 13:44:21.522]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 13:44:21.601]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 13:44:21.618]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 13:44:21.699]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 13:44:21.851]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 13:44:21.952]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 13:44:21.993]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 13:44:22.007]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 13:44:22.022]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 13:44:22.031]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 13:44:22.045]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 13:44:22.110]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 13:44:22.237]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 13:44:22.267]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 13:44:22.274]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 13:44:22.282]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 13:44:22.455]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 13:44:22.464]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 13:44:22.479]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 13:44:22.486]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 13:44:22.497]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 13:44:22.505]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:22.513]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 13:44:22.518]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:22.526]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 13:44:22.532]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:22.620]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 13:44:22.627]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 13:44:23.310]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 13:44:23.373]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 13:44:23.383]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:23.390]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:23.396]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:23.403]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:23.411]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:23.419]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:26.034]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.042]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.049]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.056]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.063]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.071]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:26.124]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.131]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.140]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.149]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.156]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing MaximizeRestoreCommand
[2025-08-09 13:44:26.165]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.174]  	[DEBUG]		[CustomWindowChromeViewModel]	Current window state: Normal, Target operation: maximize to Maximized (Title: UFU Client Management)
[2025-08-09 13:44:26.182]  	[DEBUG]		[WindowStateManager]	Maximizing window: UFU Client Management (Current state: Normal)
[2025-08-09 13:44:26.196]  	[DEBUG]		[WindowChromeService]	Window state changed to: Maximized for window: UFU Client Management
[2025-08-09 13:44:26.211]  	[DEBUG]		[WindowChromeService]	Window maximized, border adjustments handled by template
[2025-08-09 13:44:26.220]  	[DEBUG]		[MainWindow]	MainWindow state changed to: Maximized, synchronizing ViewModel
[2025-08-09 13:44:26.228]  	[DEBUG]		[CustomWindowChromeViewModel]	IsMaximized changed to: True
[2025-08-09 13:44:26.235]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state changed to: Maximized
[2025-08-09 13:44:26.242]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 13:44:26.251]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Maximized
[2025-08-09 13:44:26.259]  	[DEBUG]		[MainWindow]	ViewModel synchronized with window state: Maximized
[2025-08-09 13:44:26.274]  	[DEBUG]		[WindowStateManager]	Window maximized: UFU Client Management (Normal -> Maximized)
[2025-08-09 13:44:26.282]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 13:44:26.290]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Maximized
[2025-08-09 13:44:26.297]  	[DEBUG]		[CustomWindowChromeViewModel]	MaximizeRestoreCommand executed successfully: maximize operation completed for window: UFU Client Management (Final state: Maximized)
[2025-08-09 13:44:26.423]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 13:44:26.535]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 306.5678ms
[2025-08-09 13:44:26.543]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:26.652]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.660]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.676]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.687]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:26.695]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:26.702]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:27.123]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:27.143]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:27.151]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:27.165]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:27.173]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:27.181]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:27.262]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 13:44:27.276]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 13:44:27.299]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 13:44:27.324]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 13:44:27.337]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_16339495_638903402673376085 (BaseViewModel) for NPersonalViewModel
[2025-08-09 13:44:27.345]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 13:44:27.353]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.361]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_12837735_638903402673615312 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 13:44:27.368]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 13:44:27.375]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.383]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 13:44:27.390]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_48430751_638903402673909034 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 13:44:27.398]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 13:44:27.404]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.411]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 13:44:27.418]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 13:44:27.439]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 13:44:27.512]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 13:44:27.598]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_41819089_638903402675985573 (BaseViewModel) for NewClientViewModel
[2025-08-09 13:44:27.619]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 13:44:27.626]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.632]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_40827483_638903402676329054 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 13:44:27.639]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 13:44:27.645]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.652]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 13:44:27.658]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_31903028_638903402676588607 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 13:44:27.666]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 13:44:27.672]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.679]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 13:44:27.686]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 13:44:27.693]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_18691797_638903402676937080 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 13:44:27.701]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 13:44:27.707]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.715]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 13:44:27.723]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 13:44:27.732]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 13:44:27.739]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 13:44:27.750]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_34008447_638903402677504294 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 13:44:27.762]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 13:44:27.782]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 13:44:27.801]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 13:44:27.810]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 13:44:27.819]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 13:44:27.826]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 13:44:27.834]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 13:44:27.842]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 13:44:27.851]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 13:44:27.858]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 13:44:27.864]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 13:44:27.871]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 13:44:27.877]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 13:44:27.884]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 13:44:27.890]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 13:44:27.898]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 598ms (Success: True)
[2025-08-09 13:44:27.904]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 630ms (Success: True)
[2025-08-09 13:44:27.913]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x627 (Height-based width calculation)
[2025-08-09 13:44:27.947]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 13:44:27.954]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 13:44:27.958]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 13:44:27.962]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 15ms
[2025-08-09 13:44:27.969]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 13:44:27.980]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 13:44:27.986]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 40ms
[2025-08-09 13:44:28.011]  	[ERROR]		[App]	Unhandled UI thread exception: A TwoWay or OneWayToSource binding cannot work on the read-only property 'PrimaryPhoneNumber' of type 'UFU2.Models.PhoneNumbersCollectionModel'.
[2025-08-09 13:44:28.096]  	[ERROR]		[App]	Stack trace:    at MS.Internal.Data.PropertyPathWorker.CheckReadOnly(Object item, Object info)
   at MS.Internal.Data.PropertyPathWorker.ReplaceItem(Int32 k, Object newO, Object parent)
   at MS.Internal.Data.PropertyPathWorker.UpdateSourceValueState(Int32 k, ICollectionView collectionView, Object newValue, Boolean isASubPropertyChange)
   at MS.Internal.Data.ClrBindingWorker.AttachDataItem()
   at System.Windows.Data.BindingExpression.Activate(Object item)
   at System.Windows.Data.BindingExpression.AttachToContext(AttachAttempt attempt)
   at System.Windows.Data.BindingExpression.MS.Internal.Data.IDataBindEngineClient.AttachToContext(Boolean lastChance)
   at MS.Internal.Data.DataBindEngine.Task.Run(Boolean lastChance)
   at MS.Internal.Data.DataBindEngine.Run(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-08-09 13:44:28.109]  	[ERROR]		[App]	Exception in LogException: A TwoWay or OneWayToSource binding cannot work on the read-only property 'PrimaryPhoneNumber' of type 'UFU2.Models.PhoneNumbersCollectionModel'.
[2025-08-09 13:44:28.118]  	[ERROR]		[App]	Context - Source: App, Operation: LogException, Timestamp: 2025-08-09 13:44:28.107, ElapsedMs: 0, UserAgent: XLABZ
[2025-08-09 13:44:28.126]  	[ERROR]		[App]	Stack trace:    at MS.Internal.Data.PropertyPathWorker.CheckReadOnly(Object item, Object info)
   at MS.Internal.Data.PropertyPathWorker.ReplaceItem(Int32 k, Object newO, Object parent)
   at MS.Internal.Data.PropertyPathWorker.UpdateSourceValueState(Int32 k, ICollectionView collectionView, Object newValue, Boolean isASubPropertyChange)
   at MS.Internal.Data.ClrBindingWorker.AttachDataItem()
   at System.Windows.Data.BindingExpression.Activate(Object item)
   at System.Windows.Data.BindingExpression.AttachToContext(AttachAttempt attempt)
   at System.Windows.Data.BindingExpression.MS.Internal.Data.IDataBindEngineClient.AttachToContext(Boolean lastChance)
   at MS.Internal.Data.DataBindEngine.Task.Run(Boolean lastChance)
   at MS.Internal.Data.DataBindEngine.Run(Object arg)
   at System.Windows.Threading.ExceptionWrapper.InternalRealCall(Delegate callback, Object args, Int32 numArgs)
   at System.Windows.Threading.ExceptionWrapper.TryCatchWhen(Object source, Delegate callback, Object args, Int32 numArgs, Delegate catchHandler)
[2025-08-09 13:44:28.135]  	[DEBUG]		[ErrorDeduplicationManager]	No operation ID provided, allowing toast from App
[2025-08-09 13:44:28.143]  	[INFO]		[App]	Displaying user error toast: خطأ في التطبيق - حدث خطأ غير متوقع. سيحاول التطبيق المتابعة. يرجى حفظ عملك وإعادة التشغيل إذا استمرت المشاكل.
[2025-08-09 13:44:28.150]  	[DEBUG]		[ToastService]	Displaying Error toast: خطأ في التطبيق - حدث خطأ غير متوقع. سيحاول التطبيق المتابعة. يرجى حفظ عملك وإعادة التشغيل إذا استمرت المشاكل.
[2025-08-09 13:44:28.164]  	[INFO]		[ToastNotification]	Toast notification created: Error - خطأ في التطبيق
[2025-08-09 13:44:28.177]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 13:44:28.189]  	[INFO]		[ToastService]	Desktop toast displayed: Error - خطأ في التطبيق
[2025-08-09 13:44:28.208]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 13:44:29.003]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 13:44:29.018]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 13:44:29.367]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 2, Batched: 2, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentWindowState(1), IsMaximized(1)
[2025-08-09 13:44:29.382]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 3154.9238ms
[2025-08-09 13:44:29.391]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:29.404]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1911.0448ms
[2025-08-09 13:44:29.413]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.422]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1823.5813ms
[2025-08-09 13:44:29.443]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.451]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1818.1933ms
[2025-08-09 13:44:29.457]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.464]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1805.569ms
[2025-08-09 13:44:29.471]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.483]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1789.3036ms
[2025-08-09 13:44:29.490]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.496]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1746.179ms
[2025-08-09 13:44:29.503]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 13:44:29.541]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.549]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:29.557]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.563]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:29.571]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.581]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:29.653]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.662]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:29.672]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.681]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:29.689]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:29.696]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:30.412]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2918.8595ms
[2025-08-09 13:44:30.449]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:30.463]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2864.5102ms
[2025-08-09 13:44:30.543]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:30.624]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2991.9235ms
[2025-08-09 13:44:30.632]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:30.641]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2982.5318ms
[2025-08-09 13:44:30.652]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:30.691]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2997.6154ms
[2025-08-09 13:44:30.697]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:30.736]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2986.5131ms
[2025-08-09 13:44:30.743]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:44:32.353]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 13:44:33.177]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.191]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:33.198]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.206]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:33.212]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.220]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:33.295]  	[INFO]		[ToastService]	Displaying user info: تفاصيل الإشعار - A TwoWay or OneWayToSource binding cannot work on the read-only property 'PrimaryPhoneNumber' of type 'UFU2.Models.PhoneNumbersCollectionModel'.
[2025-08-09 13:44:33.373]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.402]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:33.412]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.423]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:44:33.430]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:44:33.447]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:44:38.354]  	[INFO]		[ToastNotification]	Toast notification closed: Error - خطأ في التطبيق
[2025-08-09 13:44:39.439]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.456]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.463]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.475]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.481]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.489]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.495]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.502]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.508]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:44:39.518]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 13290.1497ms
[2025-08-09 13:44:39.524]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.529]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 12036.556ms
[2025-08-09 13:44:39.545]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.561]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12200.1069ms
[2025-08-09 13:44:39.571]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.577]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12186.7817ms
[2025-08-09 13:44:39.586]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.592]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 11993.8467ms
[2025-08-09 13:44:39.602]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.634]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12001.7859ms
[2025-08-09 13:44:39.640]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.676]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12017.311ms
[2025-08-09 13:44:39.692]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.707]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12013.8263ms
[2025-08-09 13:44:39.720]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:44:39.770]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 12019.8263ms
[2025-08-09 13:44:39.792]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:03.159]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 13:45:03.910]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 13:45:04.425]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 66.7% increase in response time
[2025-08-09 13:45:05.017]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 66.7% increase in response time
[2025-08-09 13:45:09.050]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 233.3% increase in response time
[2025-08-09 13:45:09.567]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 233.3% increase in response time
[2025-08-09 13:45:20.407]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 345.4MB, System: 30.0%, Pressure: Normal
[2025-08-09 13:45:27.065]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 13:45:27.566]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 13:45:38.452]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.461]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.468]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.476]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.484]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.490]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.497]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.504]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.510]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 13:45:38.534]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:45:38.555]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:45:38.563]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:45:38.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 13:45:38.576]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 13:45:38.583]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 13:45:38.601]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 148.8773ms
[2025-08-09 13:45:38.608]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.614]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 153.3047ms
[2025-08-09 13:45:38.622]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.629]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 160.9236ms
[2025-08-09 13:45:38.636]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.644]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 168.3725ms
[2025-08-09 13:45:38.654]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.662]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 177.5835ms
[2025-08-09 13:45:38.672]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.678]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 187.7226ms
[2025-08-09 13:45:38.689]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.697]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 200.3415ms
[2025-08-09 13:45:38.706]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.749]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 245.4404ms
[2025-08-09 13:45:38.757]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:38.832]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 322.0513ms
[2025-08-09 13:45:38.842]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 13:45:40.617]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2164.6079ms
[2025-08-09 13:45:40.634]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.645]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2183.828ms
[2025-08-09 13:45:40.659]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.668]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2199.9468ms
[2025-08-09 13:45:40.682]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.693]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2217.3902ms
[2025-08-09 13:45:40.703]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.739]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2254.4616ms
[2025-08-09 13:45:40.746]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.754]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2263.7912ms
[2025-08-09 13:45:40.761]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.769]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2272.5975ms
[2025-08-09 13:45:40.776]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.784]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2280.3342ms
[2025-08-09 13:45:40.791]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:40.849]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2339.0404ms
[2025-08-09 13:45:40.855]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 13:45:42.582]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.599]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.610]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.618]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.625]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.632]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.639]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.646]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.652]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 13:45:42.681]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 4228.7589ms
[2025-08-09 13:45:42.691]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.700]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 4239.5406ms
[2025-08-09 13:45:42.707]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.725]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4256.21ms
[2025-08-09 13:45:42.776]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.804]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4328.5235ms
[2025-08-09 13:45:42.823]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.833]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4348.6672ms
[2025-08-09 13:45:42.840]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.847]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4356.9735ms
[2025-08-09 13:45:42.861]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.872]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4374.9995ms
[2025-08-09 13:45:42.888]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.897]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4393.6397ms
[2025-08-09 13:45:42.909]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 13:45:42.933]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 4423.6433ms
